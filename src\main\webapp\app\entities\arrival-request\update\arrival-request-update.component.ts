import { Component, ElementRef, inject, OnInit, ViewChild } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, finalize, map } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IServiceType } from 'app/entities/service-type/service-type.model';
import { DropzoneComponent, DropzoneConfigInterface, DropzoneModule } from 'ngx-dropzone-wrapper'; // Add this import // Add this import
import { IUser } from 'app/entities/user/user.model';
import { UserService } from 'app/entities/user/service/user.service';
import { ICompany } from 'app/entities/company/company.model';
import { CompanyService } from 'app/entities/company/service/company.service';
import { IArrivalRequestNationality } from 'app/entities/arrival-request-nationality/arrival-request-nationality.model';
import { ArrivalRequestNationalityService } from 'app/entities/arrival-request-nationality/service/arrival-request-nationality.service';
import { ICargoType } from 'app/entities/cargo-type/cargo-type.model';
import { CargoTypeService } from 'app/entities/cargo-type/service/cargo-type.service';
import { ArrivalRequestService } from '../service/arrival-request.service';
import { IArrivalRequest } from '../arrival-request.model';
import { ArrivalRequestFormGroup, ArrivalRequestFormService } from './arrival-request-form.service';
import { IServiceTypeLiabilityType } from '../../service-type-liability-type/service-type-liability-type.model';
import { ServiceTypeLiabilityTypeService } from '../../service-type-liability-type/service/service-type-liability-type.service';
import { ServiceTypeDocumentTypeService } from '../../service-type-document-type/service/service-type-document-type.service';
import { IServiceTypeDocumentType } from '../../service-type-document-type/service-type-document-type.model';
import { IServiceTypeTermsConditions } from '../../service-type-terms-conditions/service-type-terms-conditions.model';
import { ServiceTypeTermsConditionsService } from '../../service-type-terms-conditions/service/service-type-terms-conditions.service';
import { InvestmentTypeService } from '../../investment-type/service/investment-type.service';
import { IInvestmentType } from '../../investment-type/investment-type.model';
import { TranslateService } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { MarineMedium } from '../../marine-medium/model/marine-medium.model';
import { ServiceTypeService } from 'app/entities/service-type/service/service-type.service';
import { AccountService } from 'app/core/auth/account.service';
import { MarineMediumService } from '../../marine-medium/marine-medium.service';
import { IAttachment } from 'app/entities/attachment/attachment.model';
import { GregorianCalendarComponent } from 'app/shared/gregorian-calendar/gregorian-calendar.component';
import dayjs from 'dayjs/esm';
import { IPassenger } from '../../passenger/passenger.model';
import { PortCountryService } from '../../port-country/service/port-country.service';
import { IPortCode } from '../../port-code/port-code.model';
import { IPortCountry } from '../../port-country/port-country.model';
import { IRoutes } from '../../routes/routes.model';
import { PortType } from '../../enumerations/port-type.model';
import { PortCodeService } from '../../port-code/service/port-code.service';
import FormatMediumDatetimePipe from '../../../shared/date/format-medium-datetime.pipe';
import FormatMediumDatePipe from '../../../shared/date/format-medium-date.pipe';
import { IArrivalReason } from '../arrival-reason.model';
import { IMtaRequest } from '../../mta/mta-request.model';
import { Dayjs } from 'dayjs';
import { WizardStepComponent } from 'app/layouts/wizard-step/wizard-step.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { PageTitleComponent } from 'app/layouts/sub/page-title/page-title.component';
import { TabViewModule } from 'primeng/tabview';
import { PageTitleService } from 'app/core/util/page-title.service';
import { NotificationType } from '../../enumerations/notification-type.model';
import { SrsaAlertService } from '../../../shared/alert/srsa-alert/srsa.alert.service';

export type FileKeyValuePair = { key: string; file: File };

@Component({
  standalone: true,
  selector: 'jhi-arrival-request-update',
  templateUrl: './arrival-request-update.component.html',
  styleUrl: './arrival-request-update.component.scss',
  imports: [
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    GoogleMapsModule,
    GregorianCalendarComponent,
    RouterModule,
    DropzoneModule,
    FormatMediumDatetimePipe,
    FormatMediumDatePipe,
    WizardStepComponent,
    TabViewModule,
    PageTitleComponent,
    AngularSvgIconModule,
  ], // Ensure RouterModule is correctly imported
})
export class ArrivalRequestUpdateComponent implements OnInit {
  @ViewChild('successModal') successModal!: ElementRef;
  arrivalRequestNumber: string | null | undefined;
  isSaving = false;
  arrivalRequest: IArrivalRequest | null = null;
  passengers: IPassenger[] = [];
  selectedServiceType: IServiceType | null = null;
  investmentType: { nameAr: any; nameEn: any } = { nameAr: '', nameEn: '' };
  DEP_LAST_EXIT_DOC: IAttachment | undefined;
  DEP_SECURITY_CERTIFICATE: IAttachment | undefined;
  AdditionalAttachment: IAttachment[] = [];
  step = 0;
  fileSizeError = false; // Add this line
  getFileConfig(): DropzoneConfigInterface {
    return {
      maxFilesize: 5,
      clickable: true,
      addRemoveLinks: true,
      maxFiles: 1,
      acceptedFiles: '.pdf',
      dictDefaultMessage: this.isArabic() ? 'اضغط هنا لرفع الملف' : 'Press here to upload file',
      dictInvalidFileType: this.isArabic()
        ? 'لا تستطيع رفع ملف من هذه الصيغة. (الصيغ المسموحة: pdf)'
        : 'Cannot upload this file. (Allowed extensions: pdf)',
      dictFileTooBig: this.isArabic() ? 'الحجم الأقصى للصورة هو 5 MB' : 'Maximum image size is 5 MB',
      dictRemoveFile: this.isArabic() ? 'إزالة الملف' : 'Remove file',
      url: SERVER_API_URL + '/api/files',
    };
  }
  ownerIsIndividual = false;
  usersSharedCollection: IUser[] = [];
  companiesSharedCollection: ICompany[] = [];
  arrivalRequestNationalitiesSharedCollection: IArrivalRequestNationality[] = [];
  cargoTypesSharedCollection: ICargoType[] = [];
  arrivalReasons: IArrivalReason[] = [];
  investmentTypes: IInvestmentType[] = [];
  serviceTypeDocumentTypes?: IServiceTypeDocumentType[];
  serviceTypeTermsConditions?: IServiceTypeTermsConditions[];
  selectedMarineMedium: MarineMedium | null = null;
  marineMediums: MarineMedium[] = [];
  allPortCountries: IPortCountry[] = [];
  countriesForLastPort: IPortCountry[] = [];
  countriesForNextPort: IPortCountry[] = [];
  countriesForCurrentPort: IPortCountry[] = [];
  routes: IRoutes[] = [];
  editMode: boolean = false;

  disclaimerChecked = false;
  currentStep = 1;
  backFromStep4 = false;
  documentTypes: any[] = [];
  hidden1: boolean = true;
  hidden2: boolean = false;
  documents: FileKeyValuePair[] = [] as FileKeyValuePair[];
  documentsFilesError = false;
  documentsUploaded = false;

  steps: any[] = [
    { titleAr: 'بيانات الرئسية', titleEn: 'Major Data' },
    { titleAr: 'بيانات إضافية', titleEn: 'Additional Information' },
    { titleAr: 'المرفقات', titleEn: 'Attachment' },
    { titleAr: 'معاينة الطلب', titleEn: 'Preview' },
  ];
  step1: boolean = true;
  step2: boolean = false;
  step3: boolean = false;

  liabilityChecked = false;
  serviceTypeLiabilityType?: IServiceTypeLiabilityType[];
  wizardDisplay = false;

  hours: string[] = Array.from({ length: 24 }, (_, i) => (i < 10 ? '0' + i : i.toString()));
  minutes: string[] = Array.from({ length: 4 }, (_, i) => (i * 15).toString().padStart(2, '0'));
  filteredDepartureHours: string[] = this.hours;
  filteredDepartureMinutes: string[] = this.minutes;
  estimatedArrivalHour: string = '00';
  estimatedArrivalMinute: string = '00';
  estimatedDepartureHour: string = '00';
  estimatedDepartureMinute: string = '00';

  estimatedArrival: dayjs.Dayjs | null = null;
  estimatedDeparture: dayjs.Dayjs | null = null;
  public alertService = inject(SrsaAlertService);

  arrivalRequestService = inject(ArrivalRequestService);
  arrivalRequestFormService = inject(ArrivalRequestFormService);
  userService = inject(UserService);
  companyService = inject(CompanyService);
  arrivalRequestNationalityService = inject(ArrivalRequestNationalityService);
  cargoTypeService = inject(CargoTypeService);
  activatedRoute = inject(ActivatedRoute);
  serviceTypeLiabilityTypeService = inject(ServiceTypeLiabilityTypeService);
  serviceTypeDocumentTypeService = inject(ServiceTypeDocumentTypeService);
  serviceTypeTermsConditionsService = inject(ServiceTypeTermsConditionsService);
  translateService = inject(TranslateService);
  serviceTypeService = inject(ServiceTypeService);
  accountService = inject(AccountService);
  investmentTypeService = inject(InvestmentTypeService);
  marineMediumService = inject(MarineMediumService);
  portCountryService = inject(PortCountryService);
  protected pageTitleService = inject(PageTitleService);

  criteria: any;

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: ArrivalRequestFormGroup = this.arrivalRequestFormService.createArrivalRequestFormGroup();

  isArabic(): boolean {
    if (this.translateService && this.translateService.currentLang) {
      let currentLanguage = this.translateService.currentLang;
      currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
      return currentLanguage.startsWith('ar', 0);
    }
    return false;
  }

  compareUser = (o1: IUser | null, o2: IUser | null): boolean => this.userService.compareUser(o1, o2);

  compareCompany = (o1: ICompany | null, o2: ICompany | null): boolean => this.companyService.compareCompany(o1, o2);

  compareCargoType(o1: ICargoType | null, o2: ICargoType | null): boolean {
    return o1 && o2 ? o1.id === o2.id : o1 === o2;
  }

  compareArrivalRequestNationality = (o1: IArrivalRequestNationality | null, o2: IArrivalRequestNationality | null): boolean =>
    this.arrivalRequestNationalityService.compareArrivalRequestNationality(o1, o2);

  trackIdentityTerms(_index: number, item: IServiceTypeTermsConditions): number {
    return item.id!;
  }
  @ViewChild('dropzoneRef1', { static: false }) dropzoneComponent!: DropzoneComponent;
  @ViewChild('dropzoneRef2', { static: false }) dropzoneComponent2!: DropzoneComponent;

  onUploadFile(file: any, field: string) {
    const fileName = file.name;
    const visiblePart = fileName.startsWith('.') ? fileName.substring(1) : fileName;
    const dotCount = (visiblePart.match(/\./g) || []).length;

    if (dotCount > 1) {
      let dz;
      if (field == 'DEP_LAST_EXIT_DOC') {
        dz = this.dropzoneComponent.directiveRef?.dropzone();
      } else {
        dz = this.dropzoneComponent2.directiveRef?.dropzone();
      }
      if (dz) {
        dz.removeFile(file);
      }
      return;
    }

    this.onUploadFileSuccess(file, field);
  }
  trackPortCode(_index: number, item: IPortCode): number {
    return item.id!;
  }

  trackPortCountry(_index: number, item: IPortCountry): number {
    return item.id!;
  }

  trackRoute(index: number, route: IRoutes): any {
    return route.id;
  }

  ngOnInit(): void {
    this.loadRelationshipsOptions();
    this.step = 1;
    if (this.accountService.hasAnyAuthority('ROLE_INDIVIDUAL_USER')) {
      this.ownerIsIndividual = true;
      // this.currentStep = 1;
      // this.steps = this.steps.slice(1);
      // this.steps = this.steps.slice(1);
    }
    this.activatedRoute.data.subscribe(({ arrivalRequest }) => {
      this.arrivalRequest = arrivalRequest;
      if (arrivalRequest) {
        this.updateForm(arrivalRequest);
      } else {
        this.arrivalRequest = <IArrivalRequest>{};
        this.editForm.controls['estimatedArrival'].setValue(null);
        this.editForm.controls['estimatedDeparture'].setValue(null);
        this.editForm.controls['arrivalReason'].setValue(this.arrivalReasons[3]);
      }
    });
    this.initRoutes();

    this.pageTitleService.setBreadcrumb(this.translateService.instant('srsaApp.arrivalRequest.home.title'), '/arrival-notifications');
    // this.pageTitleService.setTitle(this.translateService.instant('srsaApp.arrivalRequest.detail.title'));
  }

  setEstimatedArrival(event: any): void {
    this.estimatedArrival = event;
    this.estimatedDeparture = null;
    this.editForm.controls['estimatedDeparture'].setValue(null);
  }

  getEstimatedArrival() {
    if (this.estimatedArrival) {
      return this.estimatedArrival!.toString();
    } else {
      return this.getDateNeededDaysFromNow(0);
    }
  }

  setEstimatedDeparture(event: any): void {
    this.estimatedDeparture = event;
    this.onArrivalTimeChange();
  }

  clickToShow1(): void {
    this.hidden1 = true;
    this.hidden2 = false;
  }

  clickToShow2(): void {
    this.hidden1 = false;
    this.hidden2 = true;
  }

  public onUploadFileSuccess(event: any, fileType: string): void {
    // this.documentsFilesError = false;
    const args = event;
    this.documents.push({
      key: fileType,
      file: new File([args], args.name, { type: args.type }),
    });
    this.documentsUploaded = this.documents.length > 1 && this.documents.length <= 6;
  }

  public onFileRemoved(event: any, fileType: string): void {
    if (this.currentStep === 4) {
      // do nothing
      return;
    } else {
      const removedFile = event;
      // Remove the file from the list
      this.documents = this.documents.filter(doc => doc.key !== fileType);
      this.documentsUploaded = this.documents.length > 1;
      this.documentsFilesError = false;
    }
  }

  public onUploadFileError(event: any): void {
    this.documentsFilesError = true;
    this.documentsUploaded = false;
  }

  getAttachment(id: number): void {
    this.marineMediumService.getAttachmentById(id).subscribe({
      next(response) {
        const url = window.URL.createObjectURL(response);
        window.open(url);
      },
    });
  }

  getMarineMedium(selectedMarine: MarineMedium | null): void {
    if (selectedMarine) {
      if (selectedMarine.id != null) {
        this.marineMediumService.find(selectedMarine.id).subscribe({
          next: response => {
            // Find the object in marineMediums array that matches the response ID
            const foundMarineMedium = this.marineMediums.find(medium => medium.id === response.data.id);

            // If found, set it as the selectedMarineMedium
            if (foundMarineMedium) {
              this.selectedMarineMedium = foundMarineMedium;
            } else {
              // If not found, set it to response data
              this.selectedMarineMedium = response.data as MarineMedium;
            }
          },
          error(err) {
            // Handle error
          },
        });
      }
    }
  }

  trackById(index: number, marineMedium: any): any {
    return marineMedium.id;
  }

  previousState(): void {
    window.history.back();
  }

  showWizard(): void {
    this.wizardDisplay = true;
  }

  wizardNextStep(): void {
    this.backFromStep4 = false;

    if (this.currentStep === 1 && this.estimatedArrival && this.estimatedDeparture) {
      this.estimatedArrival = dayjs(this.estimatedArrival);
      this.estimatedArrival = this.estimatedArrival.hour(Number(this.estimatedArrivalHour)).minute(Number(this.estimatedArrivalMinute));
      this.estimatedDeparture = dayjs(this.estimatedDeparture);
      this.estimatedDeparture = this.estimatedDeparture
        .hour(Number(this.estimatedDepartureHour))
        .minute(Number(this.estimatedDepartureMinute));
    }

    if (this.currentStep < this.steps.length) {
      this.currentStep++;
    }
  }

  wizardPrevStep(): void {
    this.backFromStep4 = this.currentStep === 4;
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  isCurrentWizardValid(): boolean {
    if (this.currentStep === 1) {
      return this.isStepOneValid();
    } else if (this.currentStep === 2) {
      return this.isStepTwoValid();
    } else if (this.currentStep === 3) {
      return this.documents.length === 1 && !this.documentsFilesError;
    }
    return true;
  }

  isStepOneValid(): boolean {
    const validInputs = !(
      (
        this.editForm.get('imoNumber')?.invalid ||
        this.editForm.get('yachtManufactureYear')?.invalid ||
        this.editForm.get('captainName')?.invalid ||
        this.editForm.get('nationality')?.invalid ||
        this.editForm.get('yachtLengthOverAll')?.invalid
      )
      // || this.editForm.get('arrivalReason')?.invalid
    );

    const validDates = this.estimatedArrival !== null && this.estimatedDeparture !== null;
    const validRoutes =
      this.routes.length > 0 &&
      !this.routes.some(
        route =>
          route.portCountry === null ||
          route.portCountry === undefined ||
          route.nationalityId === null ||
          route.nationalityId === undefined,
      );
    const validPassengers = this.passengers.length > 0 && !this.passengers.some(p => p.noOfPassengers === null || p.nationality === null);

    return validInputs && validDates && validRoutes && validPassengers;
  }

  isStepTwoValid(): boolean {
    return !(
      this.editForm.get('callSign')?.invalid ||
      this.editForm.get('arrivalDraft')?.invalid ||
      this.editForm.get('maxDraft')?.invalid ||
      this.editForm.get('cargoTonnageWeight')?.invalid ||
      this.editForm.get('cargoWeightInCubeMeter')?.invalid ||
      this.editForm.get('securityLevel')?.invalid ||
      this.editForm.get('securityCertificateNumber')?.invalid ||
      this.editForm.get('securityValidPeriod')?.invalid ||
      this.editForm.get('netRegisterTonnage')?.invalid ||
      this.editForm.get('deadWeightTonnage')?.invalid ||
      this.editForm.get('grossRegisterTonnage')?.invalid
    );
  }

  save(): void {
    this.isSaving = true;
    const arrivalRequest = this.arrivalRequestFormService.getArrivalRequest(this.editForm);

    if (this.estimatedArrival) {
      arrivalRequest.estimatedArrival = this.estimatedArrival;
    }
    if (this.estimatedDeparture) {
      arrivalRequest.estimatedDeparture = this.estimatedDeparture;
    }
    arrivalRequest.estimatedDeparture = this.estimatedDeparture;
    arrivalRequest.securityValidMeasureIn = null;
    arrivalRequest.firstCallDate = null;
    arrivalRequest.actualArrivalDate = null;
    arrivalRequest.nnerAnchorageDate = null;
    arrivalRequest.outerAnchorageDate = null;
    arrivalRequest.passengers = this.passengers;
    arrivalRequest.routes = this.routes;
    // this Static value is for this time we only have 'Others' as the Arrival Reason
    arrivalRequest.arrivalReason = this.arrivalReasons[3];
    arrivalRequest.marineMedium = this.selectedMarineMedium;
    arrivalRequest.requestType = this.arrivalRequest?.requestType;

    if (arrivalRequest.id !== null) {
      this.subscribeToUpdateResponse(this.arrivalRequestService.update(arrivalRequest, this.documents));
    } else {
      this.subscribeToSaveResponse(this.arrivalRequestService.create(arrivalRequest, this.documents));
    }
  }

  subscribeToUpdateResponse(result: Observable<HttpResponse<IArrivalRequest>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: (res: any) => {
        this.arrivalRequestNumber = res.body.arrivalRequestNumber;
        this.openModal(this.successModal);
      },
      error: () => this.onSaveError(),
    });
  }

  subscribeToSaveResponse(result: Observable<HttpResponse<IArrivalRequest>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: (res: any) => {
        this.arrivalRequestNumber = res.body.arrivalRequestNumber;
        this.openModal(this.successModal);
      },
      error: () => this.onSaveError(),
    });
  }

  loadServiceTypesRelations(): void {
    this.criteria = {};
    const serviceCategory = {
      'serviceTypeId.equals': Number(this.selectedServiceType?.id),
    };
    this.criteria = { ...this.criteria, ...serviceCategory };

    this.serviceTypeDocumentTypeService
      .query({ ...this.criteria })
      .pipe(map((res: HttpResponse<IServiceTypeDocumentType[]>) => res.body ?? []))
      .subscribe((serviceTypeDocumentType: IServiceTypeDocumentType[]) => {
        this.serviceTypeDocumentTypes = serviceTypeDocumentType;
      });
    this.serviceTypeTermsConditionsService
      .query({ ...this.criteria })
      .pipe(map((res: HttpResponse<IServiceTypeTermsConditions[]>) => res.body ?? []))
      .subscribe((serviceTypeTermsConditions: IServiceTypeTermsConditions[]) => {
        this.serviceTypeTermsConditions = serviceTypeTermsConditions;
      });
    this.serviceTypeLiabilityTypeService
      .query({ ...this.criteria })
      .pipe(map((res: HttpResponse<IServiceTypeLiabilityType[]>) => res.body ?? []))
      .subscribe((serviceTypeLiabilityType: IServiceTypeLiabilityType[]) => {
        this.serviceTypeLiabilityType = serviceTypeLiabilityType;
      });
  }

  onSaveSuccess(): void {
    this.previousState();
  }

  onSaveError(): void {
    // Api for inheritance.
  }

  onSaveFinalize(): void {
    this.isSaving = false;
  }

  updateForm(arrivalRequest: IArrivalRequest): void {
    this.arrivalRequest = arrivalRequest;
    this.arrivalRequestFormService.resetForm(this.editForm, arrivalRequest);

    this.usersSharedCollection = this.userService.addUserToCollectionIfMissing<IUser>(
      this.usersSharedCollection,
      arrivalRequest.requestCompanyOwner,
    );
    this.companiesSharedCollection = this.companyService.addCompanyToCollectionIfMissing<ICompany>(
      this.companiesSharedCollection,
      arrivalRequest.requestUserOwner,
      arrivalRequest.agent,
    );
    this.arrivalRequestNationalitiesSharedCollection =
      this.arrivalRequestNationalityService.addArrivalRequestNationalityToCollectionIfMissing<IArrivalRequestNationality>(
        this.arrivalRequestNationalitiesSharedCollection,
        arrivalRequest.nationality,
      );
    this.cargoTypesSharedCollection = this.cargoTypeService.addCargoTypeToCollectionIfMissing<ICargoType>(
      this.cargoTypesSharedCollection,
      arrivalRequest.cargoType,
    );

    if (this.arrivalRequest.attachments) {
      for (const file of this.arrivalRequest.attachments) {
        switch (file.attachmentType?.code) {
          case 'DEP_LAST_EXIT_DOC':
            this.DEP_LAST_EXIT_DOC = file;
            this.documents.push({ key: 'DEP_LAST_EXIT_DOC', file: new File([], file.docName!, undefined) });
            break;
          case 'DEP_SECURITY_CERTIFICATE':
            this.DEP_SECURITY_CERTIFICATE = file;
            this.documents.push({ key: 'DEP_SECURITY_CERTIFICATE', file: new File([], file.docName!, undefined) });
            break;
        }
      }
    }
    this.editMode = true;
    this.step = 2;
    this.showWizard();
    this.currentStep = 2;
  }

  loadRelationshipsOptions(): void {
    this.marineMediumService.listAvailableForArrivalRequest().subscribe({
      next: response => {
        this.marineMediums = response.data as MarineMedium[];
      },
      error() {},
    });
    this.userService
      .query()
      .pipe(map((res: HttpResponse<IUser[]>) => res.body ?? []))
      .pipe(map((users: IUser[]) => this.userService.addUserToCollectionIfMissing<IUser>(users, this.arrivalRequest?.requestCompanyOwner)))
      .subscribe((users: IUser[]) => (this.usersSharedCollection = users));

    this.arrivalRequestNationalityService
      .query()
      .pipe(map((res: HttpResponse<IArrivalRequestNationality[]>) => res.body ?? []))
      .pipe(
        map((arrivalRequestNationalities: IArrivalRequestNationality[]) =>
          this.arrivalRequestNationalityService.addArrivalRequestNationalityToCollectionIfMissing<IArrivalRequestNationality>(
            arrivalRequestNationalities,
            this.arrivalRequest?.nationality,
          ),
        ),
      )
      .subscribe(
        (arrivalRequestNationalities: IArrivalRequestNationality[]) =>
          (this.arrivalRequestNationalitiesSharedCollection = arrivalRequestNationalities),
      );

    this.cargoTypeService
      .query()
      .pipe(map((res: HttpResponse<ICargoType[]>) => res.body ?? []))
      .pipe(
        map((cargoTypes: ICargoType[]) =>
          this.cargoTypeService.addCargoTypeToCollectionIfMissing<ICargoType>(cargoTypes, this.arrivalRequest?.cargoType),
        ),
      )
      .subscribe((cargoTypes: ICargoType[]) => (this.cargoTypesSharedCollection = cargoTypes));

    this.arrivalRequestService
      .listArrivalReasons()
      .pipe(map((res: HttpResponse<IArrivalReason[]>) => res.body ?? []))
      .subscribe((arrivalReasons: IArrivalReason[]) => (this.arrivalReasons = arrivalReasons));

    // Load port countries
    this.portCountryService
      .query()
      .pipe(
        map((res: HttpResponse<IPortCountry[]>) => res.body ?? []),
        catchError(
          error => of([]), // Return an empty array in case of error
        ),
      )
      .subscribe((portCountries: IPortCountry[]) => {
        this.allPortCountries = portCountries;
      });
  }

  addPassengers(): void {
    const newPassenger: IPassenger = {
      noOfPassengers: null, // or any number if you want to initialize with a value
      nationality: null, // or { id: someId } if you want to set an initial nationality
      arrivalRequest: null, // or { id: someId } if you want to set an initial arrival request
    };
    this.passengers.push(newPassenger);
  }

  removePassengers(passengers: IPassenger): void {
    const index = this.passengers.indexOf(passengers);
    if (index > -1) {
      this.passengers.splice(index, 1);
    }
  }

  initRoutes(): void {
    const lastPreviousRoute: IRoutes = {
      portType: PortType.LAST_PREVIOUS_PORT,
    };
    const firstNextRoute: IRoutes = {
      portType: PortType.FIRST_NEXT_PORT,
    };
    const currentRoute: IRoutes = {
      portType: PortType.CURRENT_PORT,
    };
    this.routes.push(lastPreviousRoute, firstNextRoute, currentRoute);
  }

  selectPortCountries(route: any): void {
    if (route.portType === PortType.LAST_PREVIOUS_PORT) {
      this.countriesForLastPort = this.allPortCountries.filter(pc => pc.nationality?.id === route?.nationalityId);
    } else if (route.portType === PortType.FIRST_NEXT_PORT) {
      this.countriesForNextPort = this.allPortCountries.filter(pc => pc.nationality?.id === route?.nationalityId);
    } else if (route.portType === PortType.CURRENT_PORT) {
      this.countriesForCurrentPort = this.allPortCountries.filter(pc => pc.nationality?.id === route?.nationalityId);
    }
  }

  openModal(modal: ElementRef): void {
    modal.nativeElement.classList.add('show');
    modal.nativeElement.style.display = 'block';
    document.body.classList.add('modal-open');
  }

  closeModal(modal: ElementRef): void {
    // Access the nativeElement and call Bootstrap's modal method to hide
    modal.nativeElement.classList.remove('show');
    modal.nativeElement.style.display = 'none';
    document.body.classList.remove('modal-open');
  }

  getDateNeededDaysFromNow(daysFromNow: number) {
    const today = new Date();
    const sevenDaysFromNow = new Date(today.setDate(today.getDate() + daysFromNow));
    const year = sevenDaysFromNow.getFullYear();
    const month = String(sevenDaysFromNow.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(sevenDaysFromNow.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  onArrivalTimeChange(): void {
    const arrivalHour = parseInt(this.estimatedArrivalHour, 10);
    const arrivalMinute = parseInt(this.estimatedArrivalMinute, 10);
    // not in the same day no need to filter hours and minute
    if (this.estimatedArrival !== this.estimatedDeparture) {
      this.filteredDepartureHours = this.hours;
      this.filteredDepartureMinutes = this.minutes;
      return;
    }

    // if (arrivalHour === 23 && arrivalMinute >= 45) {
    //   // Set the estimated departure date to the next day using dayjs
    //   if (this.estimatedArrival) {
    //     this.estimatedDeparture = this.estimatedArrival.add(1, 'day'); // Move to the next day
    //   }
    // }

    // Filter hours to only allow times after the arrival time
    this.filteredDepartureHours = this.hours.filter(hour => parseInt(hour, 10) >= arrivalHour);

    // Adjust the departure hour and minute if they no longer fit the conditions
    if (parseInt(this.estimatedDepartureHour, 10) <= arrivalHour) {
      this.estimatedDepartureHour = this.filteredDepartureHours[0];
    }

    // If the departure hour is the same as the arrival hour, filter the minutes
    if (this.estimatedDepartureHour === this.estimatedArrivalHour) {
      this.filteredDepartureMinutes = this.minutes.filter(minute => parseInt(minute, 10) >= arrivalMinute);
    } else {
      this.filteredDepartureMinutes = this.minutes; // Reset to all minutes for other hours
    }

    if (parseInt(this.estimatedDepartureHour, 10) === arrivalHour && parseInt(this.estimatedDepartureMinute, 10) <= arrivalMinute) {
      this.estimatedDepartureMinute = this.filteredDepartureMinutes[0];
    }
  }
  setRequestType(type: string): void {
    if (type === 'ENTRY') {
      this.arrivalRequest!.requestType = NotificationType.ENTRY;
      this.setStaticValuesForEntry();
    } else if (type === 'EXIT') {
      this.arrivalRequest!.requestType = NotificationType.EXIT;
      this.setStaticValuesForExit();
    }
  }

  // Method to set static values for ENTRY request type
  private readonly SAUDI_NATIONALITY_ID = 236;
  private readonly SAUDI_NATIONALITY_NAME = 'SAUDI ARABIA';

  private setStaticValuesForEntry(): void {
    // Find the FIRST_NEXT_PORT route
    const firstNextPortRoute = this.routes?.find((route: IRoutes) => route.portType === 'FIRST_NEXT_PORT');
    const currentPortRoute = this.routes?.find((route: IRoutes) => route.portType === 'CURRENT_PORT');
    if (currentPortRoute) {
      currentPortRoute.nationalityId = this.SAUDI_NATIONALITY_ID;
      currentPortRoute.nationalityName = this.SAUDI_NATIONALITY_NAME;
    }

    if (firstNextPortRoute) {
      // Set Saudi nationality ID
      firstNextPortRoute.nationalityId = this.SAUDI_NATIONALITY_ID;
      firstNextPortRoute.nationalityName = this.SAUDI_NATIONALITY_NAME;

      // Set Saudi territorial waters
      firstNextPortRoute.portCountry = {
        id: 1, // You can use a static ID or generate one
        nameAr: 'المياة الإقليمية السعودية',
        nameEn: 'Saudi Territorial Waters',
        code: 'SAU-TW',
      };
    }
  }

  // Method to set static values for EXIT request type
  private setStaticValuesForExit(): void {
    const lastPortRoute = this.routes?.find((route: IRoutes) => route.portType === 'LAST_PREVIOUS_PORT');
    if (lastPortRoute) {
      lastPortRoute.nationalityId = this.SAUDI_NATIONALITY_ID;
      lastPortRoute.nationalityName = this.SAUDI_NATIONALITY_NAME;
    }
  }

  // Helper method to get nationality name by ID for display
  getNationalityName(route: IRoutes | undefined) {
    if (!route?.nationalityName || !this.arrivalRequestNationalitiesSharedCollection) {
      return '';
    }

    const nationality = this.arrivalRequestNationalitiesSharedCollection.find((nat: any) => nat.nameEn === route?.nationalityName);

    if (nationality) {
      this.selectPortCountries(route);
      return this.isArabic() ? nationality.nameAr : nationality.nameEn;
    }

    return '';
  }

  // Helper method to get port country name for display
  getPortCountryName(portCountry: any): string {
    if (!portCountry) {
      return '';
    }

    // If portCountry is an object with nameAr/nameEn properties
    if (typeof portCountry === 'object' && (portCountry.nameAr || portCountry.nameEn)) {
      const name = this.isArabic() ? portCountry.nameAr : portCountry.nameEn;
      return portCountry.code ? `${name} - ${portCountry.code}` : name;
    }

    // If portCountry is just a string
    if (typeof portCountry === 'string') {
      return portCountry;
    }

    return '';
  }
  protected readonly NotificationType = NotificationType;
}
