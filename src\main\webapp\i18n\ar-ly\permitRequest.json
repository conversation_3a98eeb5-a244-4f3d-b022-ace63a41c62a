{"srsaApp": {"permitRequest": {"home": {"title": " تراخيص الأعمال", "refreshListLabel": "Refresh list", "createLabel": "إصدار ترخيص جديد", "createOrEditLabel": "إصدار ترخيص جديد", "saveAsDraft": "ح<PERSON>ظ كمسودة", "sendRequest": "إرسال الطلب", "notFound": "لا يوجد تصاريح"}, "created": "تم إنشاء الطلب", "createdNumber": "تم ارسال الطلب رقم {{ param }} بنجاح , و سيتم اشعاركم عند تحديث حالة الطلب", "updated": "تم تعديل ترخيص ل {{ param }}", "deleted": "تم حذف ترخيص ل {{ param }}", "cancelRequest": "إلغاء إنشاء الطلب", "delete": {"question": "هل متأكد من حذف ترخيص ل {{ id }}?"}, "detail": {"title": "الطلب", "startService": "بدا الخدمة", "requiredDocuments": "المستندات المطلوبة", "serviceTerms": " شروط استخدام الخدمة", "serviceTerm1": "ان يكون السجل التجاري نشط", "serviceTerm2": "ان يكون نشاط السجل التجاري هو {{ param }}", "serviceTerm3": "الموافقة على الشروط والأحكام", "obligations": "الإلتزامات", "steps": "الخطوات", "subsidiaryCommercialRegistrationSelect": "يمكنك اختيار السجل التجاري الفرعي للمنشأة", "confirmation": "تأكيد", "googleMapSelect": "(الرجاء الضغط على الخريطة أدناه لتحديد الموقع ) ", "saveAsDraft": "ح<PERSON>ظ كمسودة", "sendRequest": "إرسال الطلب", "resendRequest": "إعادة إرسال الطلب", "myRequest": "اذه<PERSON> الى طلباتي", "otherVal": "أدخل الواسطات الأخرى"}, "licenseType": "نوع الطلب", "licenseStatus": "حالة الطلب", "issueDate": "تاريخ الاصدار", "expiryDate": "تاريخ الانتهاء", "id": "الرقم", "requestNumber": "رق<PERSON> الطلب", "licenceNumber": "رقم الترخيص", "requestStatus": "حالة الطلب", "submitDate": "تاريخ الطلب", "misaLicenseNumber": "رقم رخصة وزارة الاستثمار", "eczaLicenseNumber": "رقم رخصة هيئة المدن و المناطق الاقتصادية", "saudiLaborCount": "عد<PERSON> العاملين السعوديين", "foreignersLaborCount": "<PERSON><PERSON><PERSON> العاملين الأجانب", "numberOfMaleEmployees": "<PERSON><PERSON><PERSON> العاملين الذكور", "numberOfFemaleEmployees": "<PERSON><PERSON><PERSON> العاملين الإناث", "contactInformation": "بيانات التواصل", "saudizationLevel": "مستوى التوطين", "maleFemaleTotalError": "عد<PERSON> العاملين الذكور والاناث لا يساوون مجموع العاملين السعوديين والاجانب", "rateRange": "معدل التوطين %", "generalInformationOfLabor": "معلومات عامة عن العاملين", "contactOfficerName": "اسم ضابط الاتصال", "contactOfficerMobileNumber": "رقم جوال ضابط الاتصال", "contactOfficerEmailAddress": "البريد الالكتروني لضابط الاتصال", "vatRegistrationNumber": "الرقم الضريبي", "requestNote": "ملاحظات", "disclaimerChecked": "الشروط والأحكام", "fees": "الرسوم (بالريال)", "totalPriceWithoutVat": "المبلغ الإجمالي بدون ضريبه", "totalPriceWithVat": "المبلغ الإجمالي بالضريبه", "assignedEmployeeName": " إسم الموظف المسؤول", "attachments": "المرفقات", "cR": "السجل التجاري", "CR": "السجل التجاري", "MainCr": "السجل التجاري الرئيسي ", "MainCR": "السجل التجاري الرئيسي ", "nationalAddress": "العنوان الوطني للمنشأة", "CompanyNationalAddress": "العنوان الوطني للمنشأة", "all": "الكل", "workforceMinistryHR": "نسخة من بيانات القوى العاملة من وزارة الموارد البشرية", "investmentLicense": "رخصة الاستثمار", "InvestmentLicense": "رخصة الاستثمار", "bankGuarantee": "ضمان بنكي صادر من بنك محلي في المملكة العربية السعودية لا يقل عن 500 ألف ريال غير قابل للإلغاء و غير مقيد و غير مشروط  و ينتهي بعد 3 أشهر من انتهاء الترخيص", "BankGuarantee": "ضمان بنكي صادر من بنك محلي في المملكة العربية السعودية لا يقل عن 500 ألف ريال غير قابل للإلغاء و غير مقيد و غير مشروط  و ينتهي بعد 3 أشهر من انتهاء الترخيص", "zakatLicense": "ترخيص هيئة الزكاة و النقل للتخليص الجمركي ", "ZakatLicense": "ترخيص هيئة الزكاة و النقل للتخليص الجمركي ", "internationalSafetyManagement": "شهادات الامتثال لنظام إدارة السلامة الدولية", "InternationalSafetyManagement": "شهادات الامتثال لنظام إدارة السلامة الدولية", "IdentityRegistered": "الهوية والعلامة التجارية المسجل", "ListMarineOwned": "قائمة بالوسائط البحرية المملوكة", "AdditionalAttachment": "مرفقات اضافية ", "contractAuthority": "عقد بين المنشاة ووكيل ملاحي سياحي مرخص من قبل الهيئة السعودية للبحر الاحمر ", "ContractAuthority": "عقد بين المنشاة ووكيل ملاحي سياحي مرخص من قبل الهيئة السعودية للبحر الاحمر ", "ContractAuthorityTooltip": "ملاحظة: في حال أن المنشأة تمتلك رخصة وكيل ملاحي سياحي الرجاء ارفاق رخصة الوكيل الملاحي السياحي", "MarinaDocument": "وثيقة سياسات المرسى البحري السياحي", "operatingContract": "عقد تشغيل بين مالك المرسى والمشغل", "OperatingContract": "عقد تشغيل بين مالك المرسى والمشغل", "insuranceCoverage": "التغطية التأمينية  (تشمل مسؤولية الطرف الثالث كحد أدنى)", "InsuranceCoverage": "التغطية التأمينية  (تشمل مسؤولية الطرف الثالث كحد أدنى)", "commercialRegisterMarinaOwner": "السجل التجاري لصاحب المرسى", "CommercialRegisterMarinaOwner": "السجل التجاري لصاحب المرسى", "company": "الشركة", "workLocation": "موقع العمل", "assignedEmployee": "الموظف المسؤول", "licenseProvider": "الجهة المرخصة", "serviceType": "تصنيف الطلب", "licenseRequest": "بيانات الطلب", "COMPANY_FILE": "ملف الشركة", "CONSTRUCTION_PERMIT": "رخصة بناء مرسى بحري", "ENVIRONMENTAL_LICENSE": "الترخيص البيئي للتشغيل", "PROOF_OF_OWNERSHIP_OF_THE_MARINA": "إثبات ملكية المرسى", "IDENTITY_OF_THE_OWNER_OF_THE_MARINA": "هوية مالك المرسى", "CYBER_SECURITY_CERTIFICATE": "شهادة الامن السيبراني", "COMPANY_PROFILE": "الملف التعريفي للشركة", "cRDetail": "بيانات السجل التجاري", "additionalInfo": "بيانات اضافية", "permitData": "بيانات الطلب", "extraData": "معلومات إضافية", "preview": "مراجعة البيانات", "targetAudience": "الفئة المستهدفة", "establishments": "منشآت", "serviceTime": "وقت تنفيذ الخدمة", "serviceFees": "رسوم الخدمة", "customerService": "خدمة العملاء", "establishmentData": "بيانات المنشأه", "establishmentName": "اسم المنشأه", "contactNumber": "رقم التواصل", "subsidiaryCR": "رقم السجل الفرعي", "ownerInformation": "بيانات الملاك", "ownerName": "اسم المالك", "nationality": "الجنسية", "entity": "الكيان", "relationshipType": "نوع الارتباط", "investmentType": "نوع الاستثمار", "licensingAuthority": "الجهة المرخصة", "maritimeMediation": "الواسطات البحرية", "selectAllApply": "(الرجاء على اختيار كل ما ينطبق)", "servicesList": "قائمة الخدمات", "marineLocation": "موقع المارينا", "licensesDuration": "مدة التراخيص المطلوبة", "providedLanguages": "اللغات المقدمة", "operationCountries": "الدول التي عملت بها الشركة", "noOfOwnedMaritimeMediumsKsaWorld": "عد<PERSON> الوسائط البحرية المملوكه للشركة فى المملكه وفي العالم", "noOfOwnedMaritimeMediumsKsa": "عد<PERSON> الوسائط البحرية المملوكه للشركة فى المملكه", "noOfOwnedMaritimeMediumsWorld": "عد<PERSON> الوسائط البحرية المملوكه للشركة في العالم", "maritimeTourAgent": "الوكيل الملاحي السياحي", "yearsOfExperience": "عدد سنوات الخبره في النشاط", "noOfCountryCompanyOpr": "ع<PERSON><PERSON> الدو<PERSON> التي عملت بها الشركة", "noOfManagedMaritimeMediumsGeneral": "عدد الوسائط البحرية المدارة من الشركه بشكل عام", "workforceData": "بيانات القوى العاملة", "ministryOfHumanResourcesNumber": "رقم المنشأة لدى وزارة الموارد البشرية", "marinaOwnerName": "اسم مالك المارينا بالعربية", "marinaOwnerNameEn": "اسم مالك المارينا بالإنجليزية", "marinaCrNumber": "رقم المنشأة للشركة المالكة للمارينا ", "marinaCrNameAr": "اسم السجل التجاري للمارينا بالعربية", "marinaCrNameEn": "اسم السجل التجاري للمارينا بالإنجليزية", "numberOfDocks": "ع<PERSON><PERSON> الأرصفة", "dockLength": "طول الرصيف", "dockDepth": "عمق الرصيف", "dockWidth": "عرض الرصيف", "noOfWaterStations": "ع<PERSON><PERSON> محطات المياه", "noOfFuelStations": "ع<PERSON><PERSON> محطات الوقود", "noOfWasteStations": "ع<PERSON><PERSON> محطات النفايات", "noOfElectricChargingStations": "ع<PERSON><PERSON> محطات الشحن الكهربائي", "noOfControlTowers": "ع<PERSON><PERSON> أب<PERSON><PERSON>ج المراقبة", "noOfMarinaFacilities": "عد<PERSON> مرا<PERSON><PERSON> المرسى", "boatsCapacity": "الطاقة الاستيعابية للقوارب", "yachtsCapacity": "الطاقة الاستيعابية لليخوت", "otherMaritimeMediumCapacity": "الطاقة الاستيعابية للوسائط البحرية الاخرى", "fuelTypes": "أنواع الوقود", "marineTourOperatorServices": "خدمات مشغل المرسى ", "termsAndConditions": "أوافق وأقر على الشروط والأحكام وسياسات الهيئة السعودية للبحر الأحمر <strong>الشروط والاحكام</strong>\n", "obligations": "أوافق وأقر على الرقابة والالتزامات وسياسات الهيئة السعودية للبحر الأحمر ", "rqMng": "إدارة الطلبات", "rqNo": "طلبات جديدة", "rqNew": "رق<PERSON> الطلب", "esnad": "إسناد", "cancelEsnad": "إلغاء الإسناد", "rqPhase": "مراحل سير الطلب", "rqDetails": "تفاصيل الطلب", "rqDate": "تاريخ الطلب", "asignedTo": "مسندة إلى", "crName": "اسم المنشأة", "rqType": "نوع الطلب", "rqStatus": "حالة الطلب", "visitDate": "موع<PERSON> الزيارة", "visitStatus": "حالة الزيارة", "visitReschedule": "إعادة جدولة زيارة ميدانية", "visitComplete": "إتمام الزيارة الميدانية", "visitEdit": "تعديل محضر وتقرير الزيارة", "returnRq": "إعادة الطلب", "visitSchedule": "جدولة زيارة ميدانية", "rejectRq": "<PERSON><PERSON><PERSON> الطلب", "confirmRq": "تأ<PERSON>يد الطلب", "returnRqOfficer": "إرجاع الطلب لموظف المعاينة", "TameedRq": "تع<PERSON>ي<PERSON> الطلب", "visitScheduleTitle": "تحديد موعد الزيارة الميدانية", "visitScheduleTime": "وقت الزيارة الميدانية", "confirm": "تأكيد", "cancel": "إلغاء", "confirmVisit": "تأكيد الزيارة", "uploadVisitDocs": "رفع محضر و تقرير الزيارة الميدانية", "returnReqToProvider": "اعادة الطلب الى المنشأة للاستكمال", "returnToLicensingSupervisor": "إعادة إلى مشرف تراخيص السياحة الساحلية", "sendRqToMngr": "ارسال الطلب الى مدير المعاينة", "editAndsendRqToMngr": "تعديل واعادة ارسال الى مدير المعاينة", "finalConfirmRq": "الإعتماد النهائي للطلب", "previewRq": "يرجى التأكد من جميع التفاصيل قبل الاعتماد النهائي للطلب.", "exceptionApproval": "هل الطلب معفي من الشروط؟", "feeRequired": "هل يوجد رسوم للطلب؟", "exceptionalDoc": "رفع مستند مبررات الإستثناء", "EXCEPTIONAL_DOC": "رفع مستند مبررات الإستثناء", "sendRqToProvider": "إرسال الطلب للمنشأة", "visitReport": "محضر وتقرير الزيارة الميدانية", "Visit_Report_DOC": "محضر وتقرير الزيارة الميدانية", "edit": "تعديل", "visitRequired": "هل الطلب فى حاجة إلي زيارة ميدانية؟", "yes": "نعم", "no": "لا", "rejectReason": "سبب الرفض", "ifRejectRq": "هل متأكد من رفض الطلب؟", "ifReturnRq": "هل أنت متأكد من إعادة الطلب؟", "ifComplatedReq": "هل أنت متأكد أن الطلب مكتمل؟", "ifComplatedReqNote": "بمجرد تأكيدك، لا يمكنك إرجاع الطلب.", "back": "التراجع", "licenceOfficer": "موظف التراخيص", "selectOfficer": "اختيار الموظف", "inspectionOfficer": "إخصائي أول إمتثال السياحة الساحلية", "managerlisence": "مدير إدارة تراخيص السياحة الساحلية", "ifCancelRq": "هل متأكد من إلغاء الإسناد؟", "history": "السجلات و الملاحظات", "requestPreview": "معاينة الطلب", "notes": "الملاحظات", "requestConfirmDate": "تاريخ تأكيد الطلب", "download": "تحميل", "editVisit": "تعديل محضر وتقرير الزيارة الميدانية", "totalFundingOfProject": "إجمالي التمويل للمشروع", "beachOwnerName": "اسم مالك الشاطئ", "beachTradeName": "الاسم التجاري للشاطئ", "beachOwnerCommercialRegistrationNumber": "رقم السجل التجاري التابع لمالك الشاطئ", "numberOfRestrooms": "كم عدد دورات المياه الموجودة", "numberOfLifeguards": "كم عدد المنقذين الموجودة", "isThereCabinsForRent": "<PERSON><PERSON> يو<PERSON>د غرف او كبائن للأيجار", "numberOfCabinsAndRooms": "ع<PERSON><PERSON> الكبائن والغرف", "numberOfFoodTruckParking": "عدد مواقف الفوود تراك", "visitorCapacity": "عد<PERSON> الطاقة الاستيعابية للزوار", "isThereMarineVessel": "ه<PERSON> يوجد وسائط بحرية", "numberOfOtherMarineVessel": "كم عدد الوسائط البحرية", "previouslyOperatedBeachInsideOrOutsideSaudiArabia": "هل سبق وان تم تشغيل شاطئ قبل ذلك داخل او خارج المملكة العربية السعودية", "beachLocation": "موقع ممارسة النشاط", "beachEquipment": "المعدات التي تستخدم أو سوف تستخدم", "beachActivities": "انواع الانشطة", "statusVisit": {"SCHEDULED": "مجدول", "COMPLETED": "مكتمل", "NOT_VISITED": "لم تتم الزيارة"}, "licenseDuration": {"ONE_YEAR": "سنة واحدة", "TWO_YEAR": "سنتان", "THREE_YEAR": "3 سنوات"}, "saudiLevel": {"PLATINUM": "بلاتينيوم", "HIGH_GREEN": "أخضر عالي", "MEDIUM_GREEN": "أخضر متوسط", "LOW_GREEN": "أخضر منخفض", "RED": "<PERSON><PERSON><PERSON><PERSON>"}, "licenseNumber": "رق<PERSON> الطلب", "assignedTo": "مسندة الى", "attributionStatus": "حالة الإسناد", "allRequests": "جميع الطلبات", "newRequests": "طلبات جديدة", "notAssigned": "لم يتم اسناد", "assigned": "تم اسناد", "inProgressRequests": "قيد التنفيذ", "issuedRequests": "تم الإصدار", "assignedRequests": "طلبات مسندة", "noValidPermit": "عفوا، لا توجد تصاريح ملاحية سارية لهذه الواسطة، أو أن التصريح سينتهي خلال أقل من 15 يومًا.", "noValidTourismPermit": "عفوا , لا يوجد تصاريح فنية ساريه لهذه الواسطة", "noValidTechnicalPermit": "عذرًا، لا يمكن إصدار تصريح رحلة سياحية لأن الترخيص الفني منتهي أو على وشك الانتهاء.", "serviceChannels": "قنوات الخدمة", "webMobileApp": "تطبيق الويب", "paymentMethods": "قنوات الدفع", "beachOperator": {"cR": "السجل التجاري", "CR": "السجل التجاري", "investmentLicense": "رخصة الاستثمار", "InvestmentLicense": "رخصة الاستثمار", "MARINE_VEHICLES_AND_ACTIVITIES": "بيان بعدد وتراخيص الوسائط البحرية، والأنشطة البحرية المرخصة التي تُمارس في مياه الشواطئ من الجهات المختصة", "WATER_SAFETY_EQUIPMENT": "بيان بأدوات وتجهيزات السلامة المائية للشواطئ", "SAFETY_MANAGEMENT_PLAN": "خطة سلامة الشواطئ", "ENVIRONMENTAL_PERMIT": "التصريح البيئي للتشغيل الصادر عن المركز الوطني للرقابة على الالتزام البيئي", "LIFEGUARDS_LIST": "بيان بعدد وأسماء المنقذين", "MARITIME_SPACE_PLANNING": "تخطيط الحيز البحري لمناطق ممارسة الأنشطة البحرية في مياه الشواطئ", "OCCUPANCY_CERTIFICATE": "شهادة الإشغال الصادرة عن الجهة ذات الاختصاص المكاني", "SITE_VERIFICATION": "إثبات الملكية للموقع", "COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH": "السجل التجاري او الهوية لمالك الموقع (الشاطئ)", "operatingContract": "نسخة من عقد التشغيل ", "OperatingContract": "نسخة من عقد التشغيل ", "insuranceCoverage": "وثيقة التأمين", "InsuranceCoverage": "وثيقة التأمين", "COMPANY_PROFILE": "الملف التعريفي للمنشأة"}}}}