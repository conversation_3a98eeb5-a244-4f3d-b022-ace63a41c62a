package com.elm.srsa.fileScan;

import java.util.Collections;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class FileScanningService {

    @Value("${elm.providers.filescan.mock.enabled}")
    private boolean mockEnabled;

    @Value("${elm.providers.filescan.rest.url}")
    private String fileScanUrl;

    @Value("${elm.providers.filescan.username}")
    private String fileScanUsername;

    @Value("${elm.providers.filescan.password}")
    private String fileScanPassword;

    private final RestTemplate restTemplate;

    public FileScanningService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public boolean scanFile(byte[] bytesToScan) {
        if (mockEnabled) {
            log.info("File scan is mocked. Returning success.");
            return true;
        }

        if (bytesToScan == null || bytesToScan.length == 0) {
            log.warn("No bytes provided for scanning. Scan aborted.");
            return false;
        }

        StopWatch stopWatch = new StopWatch();
        boolean result = false;

        log.info("Starting file scan. Byte array size: {}", bytesToScan.length);
        stopWatch.start("File scanning process");

        try {
            // Prepare file resource
            ByteArrayResource fileResource = new ByteArrayResource(bytesToScan) {
                @Override
                public String getFilename() {
                    return "scanned_file.bin";
                }

                @Override
                public long contentLength() {
                    return bytesToScan.length;
                }
            };

            // Construct request
            MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("file", fileResource);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set("username", fileScanUsername);
            headers.set("password", fileScanPassword);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            log.info("Sending file scan request to {}", fileScanUrl);

            // Make HTTP request
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                fileScanUrl,
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            log.info("Received response with status code: {}", response.getStatusCode());

            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null) {
                log.info("Response body: {}", responseBody);
                Object resultValue = responseBody.get("result");
                if ("SUCCESS".equalsIgnoreCase(String.valueOf(resultValue))) {
                    log.info("File scan successful.");
                    result = true;
                } else {
                    log.error("File scan failed. Result: {}", resultValue);
                }
            } else {
                log.error("Received null response body from file scan service.");
            }
        } catch (Exception ex) {
            log.error("Exception occurred during file scan: {}", ex.getMessage(), ex);
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            log.info("File scan completed. Total time: {} ms. Final result: {}", stopWatch.getTotalTimeMillis(), result);
        }
        return result;
    }
}
