import { Injectable } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { ILicenseRequest, NewLicenseRequest } from '../license-request.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts ILicenseRequest for edit and NewLicenseRequestFormGroupInput for create.
 */
type LicenseRequestFormGroupInput = ILicenseRequest | PartialWithRequiredKeyOf<NewLicenseRequest>;

type LicenseRequestFormDefaults = Pick<NewLicenseRequest, 'id'>;

type LicenseRequestFormGroupContent = {
  id: FormControl<ILicenseRequest['id'] | NewLicenseRequest['id']>;
  noOfYearsExperience: FormControl<ILicenseRequest['noOfYearsExperience']>;
  noOfCountryCompanyOpr: FormControl<ILicenseRequest['noOfCountryCompanyOpr']>;
  noOfManagedMaritimeMediums: FormControl<ILicenseRequest['noOfManagedMaritimeMediums']>;
  noOfOwnedMaritimeMediumsWorld: FormControl<ILicenseRequest['noOfOwnedMaritimeMediumsWorld']>;
  noOfOwnedMaritimeMediumsKsa: FormControl<ILicenseRequest['noOfOwnedMaritimeMediumsKsa']>;
  noOfManagedMaritimeMediumsGeneral: FormControl<ILicenseRequest['noOfManagedMaritimeMediumsGeneral']>;
  requestedLicenseDuration: FormControl<ILicenseRequest['requestedLicenseDuration']>;
  marinaOwnerNameAr: FormControl<ILicenseRequest['marinaOwnerNameAr']>;
  marinaOwnerNameEn: FormControl<ILicenseRequest['marinaOwnerNameEn']>;
  marinaCrNumber: FormControl<ILicenseRequest['marinaCrNumber']>;
  marinaCrNameAr: FormControl<ILicenseRequest['marinaCrNameAr']>;
  marinaCrNameEn: FormControl<ILicenseRequest['marinaCrNameEn']>;
  numberOfDocks: FormControl<ILicenseRequest['numberOfDocks']>;
  dockLength: FormControl<ILicenseRequest['dockLength']>;
  dockDepth: FormControl<ILicenseRequest['dockDepth']>;
  noOfWaterStations: FormControl<ILicenseRequest['noOfWaterStations']>;
  noOfWaterStationsPerPlatform: FormControl<ILicenseRequest['noOfWaterStationsPerPlatform']>;
  noOfFuelStations: FormControl<ILicenseRequest['noOfFuelStations']>;
  noOfWasteStations: FormControl<ILicenseRequest['noOfWasteStations']>;
  noOfWasteStationsPerPlatform: FormControl<ILicenseRequest['noOfWasteStationsPerPlatform']>;
  noOfElectricChargingStations: FormControl<ILicenseRequest['noOfElectricChargingStations']>;
  noOfControlTowers: FormControl<ILicenseRequest['noOfControlTowers']>;
  noOfMarinaFacilities: FormControl<ILicenseRequest['noOfMarinaFacilities']>;
  boatsCapacity: FormControl<ILicenseRequest['boatsCapacity']>;
  yachtsCapacity: FormControl<ILicenseRequest['yachtsCapacity']>;
  otherMaritimeMediumCapacity: FormControl<ILicenseRequest['numberOfOtherMarineVessels']>;
  marinaLocation: FormControl<ILicenseRequest['marinaLocation']>;
  maritimeTourismAgent: FormControl<ILicenseRequest['maritimeTourismAgent']>;
};

export type LicenseRequestFormGroup = FormGroup<LicenseRequestFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class LicenseRequestFormService {
  createLicenseRequestFormGroup(licenseRequest: LicenseRequestFormGroupInput = { id: null }): LicenseRequestFormGroup {
    const licenseRequestRawValue = {
      ...this.getFormDefaults(),
      ...licenseRequest,
    };
    return new FormGroup<LicenseRequestFormGroupContent>({
      id: new FormControl(
        { value: licenseRequestRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      noOfYearsExperience: new FormControl(licenseRequestRawValue.noOfYearsExperience),
      noOfCountryCompanyOpr: new FormControl(licenseRequestRawValue.noOfCountryCompanyOpr),
      noOfManagedMaritimeMediums: new FormControl(licenseRequestRawValue.noOfManagedMaritimeMediums),
      noOfOwnedMaritimeMediumsWorld: new FormControl(licenseRequestRawValue.noOfOwnedMaritimeMediumsWorld),
      noOfOwnedMaritimeMediumsKsa: new FormControl(licenseRequestRawValue.noOfOwnedMaritimeMediumsKsa),
      noOfManagedMaritimeMediumsGeneral: new FormControl(licenseRequestRawValue.noOfManagedMaritimeMediumsGeneral),
      requestedLicenseDuration: new FormControl(licenseRequestRawValue.requestedLicenseDuration),
      marinaOwnerNameAr: new FormControl(licenseRequestRawValue.marinaOwnerNameAr),
      marinaOwnerNameEn: new FormControl(licenseRequestRawValue.marinaOwnerNameEn),
      marinaCrNumber: new FormControl(licenseRequestRawValue.marinaCrNumber),
      marinaCrNameAr: new FormControl(licenseRequestRawValue.marinaCrNameAr),
      marinaCrNameEn: new FormControl(licenseRequestRawValue.marinaCrNameEn),
      numberOfDocks: new FormControl(licenseRequestRawValue.numberOfDocks),
      dockLength: new FormControl(licenseRequestRawValue.dockLength),
      dockDepth: new FormControl(licenseRequestRawValue.dockDepth),
      noOfWaterStations: new FormControl(licenseRequestRawValue.noOfWaterStations),
      noOfWaterStationsPerPlatform: new FormControl(licenseRequestRawValue.noOfWaterStationsPerPlatform),
      noOfFuelStations: new FormControl(licenseRequestRawValue.noOfFuelStations),
      noOfWasteStations: new FormControl(licenseRequestRawValue.noOfWasteStations),
      noOfWasteStationsPerPlatform: new FormControl(licenseRequestRawValue.noOfWasteStationsPerPlatform),
      noOfElectricChargingStations: new FormControl(licenseRequestRawValue.noOfElectricChargingStations),
      noOfControlTowers: new FormControl(licenseRequestRawValue.noOfControlTowers),
      noOfMarinaFacilities: new FormControl(licenseRequestRawValue.noOfMarinaFacilities),
      boatsCapacity: new FormControl(licenseRequestRawValue.boatsCapacity),
      yachtsCapacity: new FormControl(licenseRequestRawValue.yachtsCapacity),
      otherMaritimeMediumCapacity: new FormControl(licenseRequestRawValue.numberOfOtherMarineVessels),
      marinaLocation: new FormControl(licenseRequestRawValue.marinaLocation),
      maritimeTourismAgent: new FormControl(licenseRequestRawValue.maritimeTourismAgent),
    });
  }

  getLicenseRequest(form: LicenseRequestFormGroup): ILicenseRequest | NewLicenseRequest {
    return form.getRawValue() as ILicenseRequest | NewLicenseRequest;
  }

  resetForm(form: LicenseRequestFormGroup, licenseRequest: LicenseRequestFormGroupInput): void {
    const licenseRequestRawValue = { ...this.getFormDefaults(), ...licenseRequest };
    form.reset(
      {
        ...licenseRequestRawValue,
        id: { value: licenseRequestRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): LicenseRequestFormDefaults {
    return {
      id: null,
    };
  }
}
