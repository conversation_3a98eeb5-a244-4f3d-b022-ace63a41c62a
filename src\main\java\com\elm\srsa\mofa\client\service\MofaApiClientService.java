package com.elm.srsa.mofa.client.service;

import com.elm.srsa.mofa.client.mock.MofaMockResponse;
import com.elm.srsa.mofa.client.request.CountryDTO;
import com.elm.srsa.mofa.client.request.IssueVisaRequest;
import com.elm.srsa.mofa.client.request.TourismVisaRequestDTO;
import com.elm.srsa.mofa.client.response.AuthTokenResponse;
import com.elm.srsa.mofa.client.response.VisaResponse;
import com.elm.srsa.mofa.client.response.VisaTourismApplication;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Service
public class MofaApiClientService {

    private final RestTemplate restTemplate;

    @Value("${srsa.mofa.enabled}")
    private Boolean enabled;

    @Value("${srsa.mofa.mock}")
    private Boolean mock;

    @Value("${srsa.mofa.baseUrl}")
    private String baseUrl;

    @Value("${srsa.mofa.iban}")
    private String iban;

    @Value("${srsa.mofa.grantType}")
    private String grantType;

    @Value("${srsa.mofa.scope}")
    private String scope;

    @Value("${srsa.mofa.audience}")
    private String audience;

    @Value("${srsa.mofa.clientId}")
    private String clientId;

    @Value("${srsa.mofa.clientSecret}")
    private String clientSecret;

    @Value("${srsa.mofa.tokenUrl}")
    private String tokenUrl;

    @Value("${srsa.mofa.countryUrl}")
    private String countryUrl;

    @Value("${srsa.mofa.arabicCountryUrl}")
    private String arabicCountryUrl;

    @Value("${srsa.mofa.submitVisaUrl}")
    private String submitVisaUrl;

    @Value("${srsa.mofa.issueVisaUrl}")
    private String issueVisaUrl;

    private long tokenExpirationTime;

    public MofaApiClientService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    // Get Arabic countries
    public List<String> getArabicCountries() {
        if (!enabled || mock) {
            return MofaMockResponse.getArabicCountryCodesMockResponse();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + getAuthToken());
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));

        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<List<String>> response = restTemplate.exchange(
            baseUrl + arabicCountryUrl,
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<String>>() {}
        );

        return response.getBody();
    }

    // Get all visa countries
    public List<CountryDTO> getVisaCountries() {
        if (!enabled || mock) {
            return MofaMockResponse.getAllCountriesMockResponse();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + getAuthToken());
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));

        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<List<CountryDTO>> response = restTemplate.exchange(
            baseUrl + countryUrl,
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<CountryDTO>>() {}
        );

        return response.getBody();
    }

    // Submit tourism visa request
    public VisaTourismApplication submitTourismVisaRequest(TourismVisaRequestDTO request) {
        if (!enabled || mock) {
            return MofaMockResponse.submitTourismVisaRequest();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + getAuthToken());
        headers.setContentType(MediaType.APPLICATION_JSON);
        request.setIban(iban);
        HttpEntity<TourismVisaRequestDTO> entity = new HttpEntity<>(request, headers);

        ResponseEntity<VisaTourismApplication> response = restTemplate.exchange(
            baseUrl + submitVisaUrl,
            HttpMethod.POST,
            entity,
            VisaTourismApplication.class
        );
        return response.getBody();
    }

    public VisaResponse issueVisa(IssueVisaRequest visaRequest) {
        if (!enabled || mock) {
            return MofaMockResponse.issueVisa();
        }
        String url = baseUrl + issueVisaUrl;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + getAuthToken());

        HttpEntity<IssueVisaRequest> requestEntity = new HttpEntity<>(visaRequest, headers);

        ResponseEntity<VisaResponse> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, VisaResponse.class);

        return response.getBody();
    }

    private synchronized String getAuthToken() {
        String authToken = null;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setBasicAuth(clientId, clientSecret);

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("grant_type", grantType);
        requestBody.add("scope", scope);
        requestBody.add("audience", audience);

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<AuthTokenResponse> response = restTemplate.exchange(
                baseUrl + tokenUrl,
                HttpMethod.POST,
                requestEntity,
                AuthTokenResponse.class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                authToken = response.getBody().getAccess_token();
                // Set expiration time (with 60 seconds buffer)
                tokenExpirationTime = System.currentTimeMillis() + (response.getBody().getExpires_in() - 60) * 1000L;
                return authToken;
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to obtain authentication token", e);
        }

        throw new RuntimeException("Failed to obtain authentication token");
    }
}
