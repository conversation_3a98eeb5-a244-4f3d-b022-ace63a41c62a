package com.elm.srsa.util;

import com.elm.srsa.fileScan.FileScanningService;
import com.elm.srsa.web.rest.errors.BadRequestAlertException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class FileScanUtil {

    private final Logger log = LoggerFactory.getLogger(FileScanUtil.class);

    private final FileScanningService fileScanningService;

    public FileScanUtil(FileScanningService fileScanningService) {
        this.fileScanningService = fileScanningService;
    }

    public void validateFile(byte[] bytesToScan) {
        if (bytesToScan == null || bytesToScan.length == 0) {
            log.info("No file bytes provided for validation. Skipping scan.");
            return;
        }

        log.info("Validating uploaded file. Byte size: {}", bytesToScan.length);

        try {
            boolean safe = fileScanningService.scanFile(bytesToScan);
            if (!safe) {
                log.info("File scan failed. Potential security issue detected.");
                throw new BadRequestAlertException("The uploaded file may have a security issue", "fileUpload", "file.security.issue");
            }

            log.info("File scan passed. File is safe to proceed.");
        } catch (BadRequestAlertException e) {
            // rethrow after logging
            log.error("File validation failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during file validation: {}", e.getMessage(), e);
            throw new BadRequestAlertException("Unexpected error occurred during file validation", "fileUpload", "systemException");
        }
    }
}
