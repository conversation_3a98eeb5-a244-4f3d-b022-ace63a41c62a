import { ILicenseRequest, NewLicenseRequest } from './license-request.model';

export const sampleWithRequiredData: ILicenseRequest = {
  id: 31316,
};

export const sampleWithPartialData: ILicenseRequest = {
  id: 20346,
  noOfYearsExperience: 20575,
  noOfManagedMaritimeMediumsGeneral: 17456,
  requestedLicenseDuration: 'TWO_YEAR',
  marinaCrNumber: 'demob latitude incidentally',
  marinaCrNameEn: 'and',
  dockDepth: 32499,
  noOfWaterStations: 15890,
  noOfWaterStationsPerPlatform: 25274,
  noOfFuelStations: 15747,
  boatsCapacity: 29867,
  yachtsCapacity: 30504,
  numberOfOtherMarineVessels: 21046,
};

export const sampleWithFullData: ILicenseRequest = {
  id: 11634,
  noOfYearsExperience: 23864,
  noOfCountryCompanyOpr: 11340,
  noOfManagedMaritimeMediums: 19836,
  noOfOwnedMaritimeMediumsKsaWorld: 55,
  noOfManagedMaritimeMediumsGeneral: 21690,
  requestedLicenseDuration: 'ONE_YEAR',
  marinaOwnerNameAr: 'foolish bemuse amongst',
  marinaCrNumber: 'viciously',
  marinaCrNameAr: 'that',
  marinaCrNameEn: 'times',
  numberOfDocks: 27225,
  dockLength: 28412,
  dockDepth: 19727,
  noOfWaterStations: 24469,
  noOfWaterStationsPerPlatform: 28714,
  noOfFuelStations: 17580,
  noOfWasteStations: 4997,
  noOfWasteStationsPerPlatform: 2696,
  noOfElectricChargingStations: 30522,
  noOfControlTowers: 9359,
  noOfMarinaFacilities: 21289,
  boatsCapacity: 14096,
  yachtsCapacity: 12251,
  numberOfOtherMarineVessels: 29990,
};

export const sampleWithNewData: NewLicenseRequest = {
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
