package com.elm.srsa.domain.visa;

import jakarta.persistence.*;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "visa_applicant_previous_travel")
@Getter
@Setter
public class VisaApplicantPreviousTravel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "visa_applicant_id")
    private VisaApplicant visaApplicant;

    private String country; // الدولة
    private String purpose; // الغرض
    private LocalDate fromDate; // من تاريخ
    private LocalDate toDate; // إلى تاريخ
}
