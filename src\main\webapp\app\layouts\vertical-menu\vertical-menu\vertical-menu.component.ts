import { Component, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';
import { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { Authority } from 'app/config/authority.constants';
import { Title } from '@angular/platform-browser';
import { AccountService } from 'app/core/auth/account.service';
import { TranslateService } from '@ngx-translate/core';
interface MenuItem {
  text: string;
  link: string;
  icon?: string;
  submenu?: MenuItem[];
  expanded?: boolean;
}
@Component({
  selector: 'app-vertical-menu',
  standalone: true,
  imports: [RouterLink, CommonModule, RouterLinkActive, AngularSvgIconModule, RouterOutlet],
  templateUrl: './vertical-menu.component.html',
  styleUrl: './vertical-menu.component.scss',
})
export class VerticalMenuComponent {
  // toggle submenu
  toggle: boolean = true;
  baseUrl = '../../../content/images/main-nav';
  spMenuItems: any[] = [
    {
      titleAr: 'الرئيسية',
      titleEn: 'Home',
      link: '/dashboard-sp',
      icon: `${this.baseUrl}/home.svg`,
    },
    // {
    //   titleAr: 'قائمة المنشآت',
    //   titleEn: 'Facilities',
    //   // link: '/permit-request',
    //   icon: `${this.baseUrl}/list.svg`,
    // },
    // {
    //   titleAr: 'التراخيص',
    //   titleEn: 'Licenses',
    //   link: '/licenses',
    //   icon: `${this.baseUrl}/licenses.svg`,
    // },
    // {
    //   titleAr: 'التصاريح',
    //   titleEn: 'Permits',
    //   // link: '/licenses',
    //   icon: `${this.baseUrl}/licenses.svg`,
    // },
    // {
    //   titleAr: 'تفويض الوكيل الملاحي',
    //   titleEn: 'Negotiations',
    //   // link: '/licenses',
    //   icon: `${this.baseUrl}/delegates.svg`,
    // },
    // {
    //   titleAr: 'الطلبات',
    //   titleEn: 'Requests',
    //   // link: '/licenses',
    //   icon: `${this.baseUrl}/requests.svg`,
    // },
    {
      titleAr: ' تراخيص الأعمال',
      titleEn: 'Business License',
      link: '/permit-request',
      icon: `${this.baseUrl}/permit-requests-icon.svg`,
    },
    {
      titleAr: 'تفويض الوكيل الملاحي',
      titleEn: 'Maritime Agent Delegation',
      link: '/mta',
      icon: `${this.baseUrl}/mta.svg`,
    },
    {
      titleAr: 'الواسطات البحرية',
      titleEn: 'Marines Medium',
      link: '/marine-medium',
      icon: `${this.baseUrl}/marine-medium.svg`,
    },
    {
      titleAr: ' إدارة الواسطات البحرية',
      titleEn: 'Vessel management',
      icon: `${this.baseUrl}/marine-medium-services.svg`,
      submenu: [
        {
          titleAr: 'الترخيص الملاحي',
          titleEn: 'Navigational License',
          link: '/navigation-permit',
        },
        {
          titleAr: 'اشعارات الوصول',
          titleEn: 'Arrival notifications',
          link: '/arrival-notifications',
        },
        {
          titleAr: 'اقرارات اليخوت',
          titleEn: 'Yacht Declarations',
          link: '/disclosure',
        },
        {
          titleAr: 'تصاريح المغادرة',
          titleEn: 'Departure Permit',
          link: '/departure-permit',
        },
        {
          titleAr: 'الترخيص الفني (السياحي الترفيهي) ',
          titleEn: 'Technical License (Leisure Tourism)',
          link: '/technical-permit',
        },
        {
          titleAr: 'تصريح الرحلة السياحية',
          titleEn: 'Tourism trip permit',
          link: '/tourism-permit',
        },
        {
          titleAr: 'ترخيص تأجير سياحي',
          titleEn: 'Yacht Charter License',
          link: '/yacht-charter-permit',
        },
        {
          titleAr: 'التأشيرات السياحية',
          titleEn: 'Tourism Visa ',
          link: '/visa-tourism',
        },
        // {
        //   titleAr: 'تصاريح المرور',
        //   titleEn: 'Transit Licenses',
        //   link: '/transit-permit',
        // },
      ],
    },
  ];

  menuItems: any[] = [
    // {
    //   titleAr: 'التراخيص',
    //   titleEn: 'Licenses',
    //   link: '/licenses',
    //   icon: `${this.baseUrl}/requests.svg`,
    // },
    // {
    //   titleAr: ' تراخيص الأعمال',
    //   titleEn: 'Business License',
    //   link: '/permit-request',
    //   icon: `${this.baseUrl}/licenses.svg`,
    // },
    // {
    //   titleAr: 'الفواتير',
    //   titleEn: 'Invoices',
    //   link: '/payment/invoice',
    //   icon: `${this.baseUrl}/invoice.svg`,
    // },
    {
      titleAr: 'تفويض الوكيل الملاحي',
      titleEn: 'Maritime Agent Delegation',
      link: '/mta',
      icon: `${this.baseUrl}/licenses.svg`,
    },
    {
      titleAr: 'الواسطات البحرية',
      titleEn: 'Marines Medium',
      link: '/marine-medium',
      icon: `${this.baseUrl}/licenses.svg`,
    },
    {
      titleAr: ' إدارة الواسطات البحرية',
      titleEn: 'Vessel management',
      icon: `${this.baseUrl}/marine-medium-services.svg`,
      submenu: [
        {
          titleAr: 'الترخيص الملاحي',
          titleEn: 'Navigational License',
          link: '/navigation-permit',
        },
        {
          titleAr: 'اشعارات الوصول',
          titleEn: 'Arrival notifications',
          link: '/arrival-notifications',
        },
        {
          titleAr: 'اقرارات اليخوت',
          titleEn: 'Yacht Declarations',
          link: '/disclosure',
        },
        {
          titleAr: 'تصاريح المغادرة',
          titleEn: 'Departure Permit',
          link: '/departure-permit',
        },
        {
          titleAr: 'الترخيص الفني (السياحي الترفيهي) ',
          titleEn: 'Technical License (Leisure Tourism)',
          link: '/technical-permit',
        },
        {
          titleAr: 'تصريح الرحلة السياحية',
          titleEn: 'Tourism trip permit',
          link: '/tourism-permit',
        },
        {
          titleAr: 'تصاريح تأجير اليخوت',
          titleEn: 'Yacht Charter License',
          link: '/yacht-charter-permit',
        },
        {
          titleAr: 'التأشيرات السياحية',
          titleEn: 'Tourism Visa ',
          link: '/visa-tourism',
        },
        // {
        //   titleAr: 'تصاريح المرور',
        //   titleEn: 'Transit Licenses',
        //   link: '/transit-permit',
        // },
      ],
    },
    // {
    //   titleAr: 'تصاريح المرور',
    //   titleEn: 'Transit Licenses',
    //   link: '/transit-permit',
    //   icon: `${this.baseUrl}/requests.svg`,
    // },
  ];

  fMenuItems: any[] = [
    // {
    //   titleAr: 'الرئيسية',
    //   titleEn: 'Home',
    //   link: '/dashboard-sp',
    //   icon: `${this.baseUrl}/Home.svg`,
    // },
    {
      titleAr: 'تفويض الوكيل الملاحي',
      titleEn: 'Maritime Agent Delegation',
      link: '/mta',
      icon: `${this.baseUrl}/mta.svg`,
    },
    {
      titleAr: 'الواسطات البحرية',
      titleEn: 'Marines Medium',
      link: '/marine-medium',
      icon: `${this.baseUrl}/licenses.svg`,
    },
    {
      titleAr: 'الترخيص الملاحي',
      titleEn: 'Navigational License',
      link: '/navigation-permit',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'اشعارات الوصول',
      titleEn: 'Arrival notifications',
      link: '/arrival-notifications',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'اقرارات اليخوت',
      titleEn: 'Yacht Declarations',
      link: '/disclosure',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'تصاريح المغادرة',
      titleEn: 'Departure Permit',
      link: '/departure-permit',
      icon: `${this.baseUrl}/permits.svg`,
    },
    // {
    //   titleAr: 'تصاريح المرور',
    //   titleEn: 'Transit Licenses',
    //   link: '/transit-permit',
    //   icon: `${this.baseUrl}/requests.svg`,
    // },
  ];

  superAdminMenuItems: any[] = [
    {
      titleAr: 'التراخيص',
      titleEn: 'Permits',
      link: '/permit-request',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'الواسطات البحرية',
      titleEn: 'Marines Medium',
      link: '/marine-medium',
      icon: `${this.baseUrl}/licenses.svg`,
    },
    {
      titleAr: 'تفويض الوكيل الملاحي',
      titleEn: 'Maritime Agent Delegation',
      link: '/mta',
      icon: `${this.baseUrl}/licenses.svg`,
    },
    {
      titleAr: 'الترخيص الملاحي',
      titleEn: 'Navigational License',
      link: '/navigation-permit',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'الترخيص الفني',
      titleEn: 'Technical License (Leisure Tourism)',
      link: '/technical-permit',
      icon: `${this.baseUrl}/licenses.svg`,
    },
    {
      titleAr: 'تصريح الرحلة السياحية',
      titleEn: 'Tourism trip permit',
      link: '/tourism-permit',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'اشعارات الوصول',
      titleEn: 'Arrival notifications',
      link: '/arrival-notifications',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'اقرارات اليخوت',
      titleEn: 'Yacht Declarations',
      link: '/disclosure',
      icon: `${this.baseUrl}/permits.svg`,
    },
    {
      titleAr: 'تصاريح المغادرة',
      titleEn: 'Departure Permit',
      link: '/departure-permit',
      icon: `${this.baseUrl}/permits.svg`,
    },
    // {
    //   titleAr: 'تصاريح المرور',
    //   titleEn: 'Transit Licenses',
    //   link: '/transit-permit',
    //   icon: `${this.baseUrl}/requests.svg`,
    // },
  ];

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private router: Router,
    private accountService: AccountService,
    private titleService: Title,
    private translateService: TranslateService,
  ) {}

  menuItemsToshow(): any[] {
    if (this.isServiceProvider()) {
      return this.spMenuItems;
    } else if (this.isForeign()) {
      return this.fMenuItems;
    } else if (this.isSuperAdmin()) {
      return this.superAdminMenuItems;
    }
    return this.menuItems;
  }

  isArabic() {
    let currentLanguage = this.translateService.currentLang;
    currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;

    return currentLanguage.startsWith('ar', 0);
  }

  isServiceProvider(): boolean {
    return this.accountService.hasAnyAuthority(Authority.ROLE_SERVICE_PROVIDER);
  }
  isForeign(): boolean {
    return (
      this.accountService.hasAnyAuthority(Authority.ROLE_SERVICE_PROVIDER_FOREIGN) ||
      this.accountService.hasAnyAuthority(Authority.ROLE_INDIVIDUAL_USER_FOREIGN)
    );
  }

  isSuperAdmin(): boolean {
    return this.accountService.hasAnyAuthority(Authority.SUPER_ADMIN);
  }

  toggleSubmenu(item: MenuItem): void {
    if (item.submenu) {
      item.expanded = !item.expanded;
    }
  }

  // toggle the menu when click on any menu item, works only in small screens
  toggleMenu() {
    if (window.innerWidth < 1000) {
      const body = document.getElementsByTagName('body')[0];
      body.classList.toggle('nav-open');
    }
  }

  // call the function after the view Initiated
  ngAfterViewInit() {
    this.addClickEventToNavLinks();
  }

  // add eventlistner to the menu links to call the toggleMenu function
  addClickEventToNavLinks() {
    const navLinks = this.el.nativeElement.querySelectorAll('.nav-link:not(.dropdown-toggle)');
    navLinks.forEach((link: any) => {
      this.renderer.listen(link, 'click', () => {
        this.toggleMenu();
      });
    });
  }

  ngOnInit(): void {}

  hasMenu(): boolean {
    return this.accountService.hasAnyAuthority([Authority.ROLE_SERVICE_PROVIDER, Authority.ROLE_INDIVIDUAL_USER, Authority.SUPER_ADMIN]);
  }
}
