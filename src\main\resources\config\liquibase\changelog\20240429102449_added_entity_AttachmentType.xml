<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AttachmentType.
    -->
    <changeSet id="20240429102449-1" author="jhipster">
        <createTable tableName="attachment_type">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name_ar" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="name_en" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="code" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="mandatory" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="record_activity" type="boolean" defaultValue="true">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="varchar(50)" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp" defaultValueDate="${now}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20240429102449-1-data" author="jhipster" >
        <loadData
                  file="config/liquibase/fake-data/attachment_type.csv"
                  separator=";"
                  tableName="attachment_type"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="name_ar" type="string"/>
            <column name="name_en" type="string"/>
            <column name="code" type="string"/>
            <column name="mandatory" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
    <changeSet id="20250424000000-new-types" author="aw">
        <insert tableName="attachment_type">
            <column name="id" value="17"/>
            <column name="name_ar" value="الملف الخاص بالموافقة الإستثنائية"/>
            <column name="name_en" value="Exceptional Document"/>
            <column name="code" value="EXCEPTIONAL_DOC"/>
            <column name="mandatory" value="true"/>

        </insert>
    </changeSet>
    <changeSet id="20240525000000-new-types" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="21"/>
            <column name="name_ar" value="محضر وتقرير الزيارة الميدانية"/>
            <column name="name_en" value="Permit Visit Report"/>
            <column name="code" value="Visit_Report_DOC"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20240521016000-new-types" author="faalkanhal">
        <insert tableName="attachment_type">
            <column name="id" value="18"/>
            <column name="name_ar" value="ملف الشركة"/>
            <column name="name_en" value="Company file"/>
            <column name="code" value="COMPANY_FILE"/>
            <column name="mandatory" value="true"/>

        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="19"/>
            <column name="name_ar" value="رخصة بناء مرسى بحري"/>
            <column name="name_en" value="Marine Pier Construction Permit"/>
            <column name="code" value="CONSTRUCTION_PERMIT"/>
            <column name="mandatory" value="false"/>

        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="20"/>
            <column name="name_ar" value="الترخيص البيئي للتشغيل"/>
            <column name="name_en" value="Environmental Operating License"/>
            <column name="code" value="ENVIRONMENTAL_LICENSE"/>
            <column name="mandatory" value="false"/>

        </insert>
    </changeSet>
    <changeSet id="20250724000000-new-types" author="faalkanhal">
        <insert tableName="attachment_type">
            <column name="id" value="22"/>
            <column name="name_ar" value="صورة الهوية الوطنية"/>
            <column name="name_en" value="Copy of National ID"/>
            <column name="code" value="nationalIdImage"/>
            <column name="mandatory" value="true"/>

        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="23"/>
            <column name="name_ar" value="صورة جواز السفر"/>
            <column name="name_en" value="Copy of Passport"/>
            <column name="code" value="passportImage"/>
            <column name="mandatory" value="true"/>

        </insert>
    </changeSet>
    <changeSet id="20250804000000-new-types" author="faalkanhal">
        <insert tableName="attachment_type">
            <column name="id" value="24"/>
            <column name="name_ar" value="صورة شهادة الترخيص التجاري"/>
            <column name="name_en" value="copy of the commercial license certificate"/>
            <column name="code" value="commercialLicenseCertificate"/>
            <column name="mandatory" value="true"/>

        </insert>
    </changeSet>
    <changeSet id="20240820130000_marine_medium_attachment-types" author="rsobh">
        <insert tableName="attachment_type">
            <column name="id" value="25"/>
            <column name="name_ar" value="هوية مالك الواسطة البحرية"/>
            <column name="name_en" value="Marine Owner Id Document"/>
            <column name="code" value="MARINE_OWNER_ID_DOCUMENT"/>
            <column name="mandatory" value="false"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="26"/>
            <column name="name_ar" value="شهارد تسجيل الواسطة البحرية"/>
            <column name="name_en" value="Marine Registration Document"/>
            <column name="code" value="MARINE_REGISTRATION_DOCUMENT"/>
            <column name="mandatory" value="false"/>
        </insert>
    </changeSet>
    <changeSet id="20240902160000_mta_delegation_attachment-type" author="rsobh">
        <insert tableName="attachment_type">
            <column name="id" value="27"/>
            <column name="name_ar" value="العقد مع الوكيل الملاحي السياحي"/>
            <column name="name_en" value="MTA Contract"/>
            <column name="code" value="MTA_DELEGATION_CONTRACT"/>
            <column name="mandatory" value="false"/>
        </insert>
    </changeSet>
    <changeSet id="20240902160000_arrival_departure_attachment-types" author="rsobh">
        <insert tableName="attachment_type">
            <column name="id" value="28"/>
            <column name="name_ar" value="وثيقة آخر تصريح مغادرة"/>
            <column name="name_en" value="Last Exit Document"/>
            <column name="code" value="DEP_LAST_EXIT_DOC"/>
            <column name="mandatory" value="true"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="29"/>
            <column name="name_ar" value="شهادة الضمان"/>
            <column name="name_en" value="Security Certificate "/>
            <column name="code" value="DEP_SECURITY_CERTIFICATE"/>
            <column name="mandatory" value="true"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="30"/>
            <column name="name_ar" value="تأييد العبور"/>
            <column name="name_en" value="transit Endorse"/>
            <column name="code" value="DEP_TRANSIT_ENDORSE"/>
            <column name="mandatory" value="false"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="31"/>
            <column name="name_ar" value="وثيقة بابلي"/>
            <column name="name_en" value="baplie Document"/>
            <column name="code" value="DEP_BAPLIE_DOC"/>
            <column name="mandatory" value="false"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="32"/>
            <column name="name_ar" value="وثيقة خطة التخزين"/>
            <column name="name_en" value="stowage Plan Document"/>
            <column name="code" value="DEP_STOWAGE_PLAN_DOC"/>
            <column name="mandatory" value="false"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="33"/>
            <column name="name_ar" value="شهادة التخليص"/>
            <column name="name_en" value="Clearance Certificate"/>
            <column name="code" value="DEP_CLEARANCE_CERTIFICATE"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="34"/>
            <column name="name_ar" value="محضر وتقرير الزيارة الميدانية الفنية"/>
            <column name="name_en" value="Tech Permit Visit Report"/>
            <column name="code" value="Tech_Visit_Report_DOC"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v1" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="35"/>
            <column name="name_ar" value="عقد مبرم مع الوكيل الملاحي السياحي معتمد من الهيئة"/>
            <column name="name_en" value="Contract concluded with the tourism manager approved by the Authority"/>
            <column name="code" value="Contract_concluded_tourism_manager"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v2" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="36"/>
            <column name="name_ar" value="مُستند يوّضح خط سير اليخت المخطط له ومدة إقامة اليخت ومناطق الإبحار داخل النطاق الجغرافي"/>
            <column name="name_en" value="Document the yacht's planned route, duration of stay and sailing areas within the geographical area"/>
            <column name="code" value="Document_yacht_planned_route_geographical_area"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v3" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="37"/>
            <column name="name_ar" value="شهادة تسجيل اليخت"/>
            <column name="name_en" value="Yacht Registration Certificate"/>
            <column name="code" value="Yacht_Registration_Certificate"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v4" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="38"/>
            <column name="name_ar" value="نسخة من عقد الرصيف"/>
            <column name="name_en" value="Copy of the contract The sidewalk"/>
            <column name="code" value="Copy_contract_sidewalk"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v5" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="39"/>
            <column name="name_ar" value="قائمة بيانات افراد الطاقم وتاشيراته"/>
            <column name="name_en" value="Crew Data List and Visas"/>
            <column name="code" value="Crew_Data_List_Visas"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v6" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="40"/>
            <column name="name_ar" value="نسخة من رخصة اللاسلكي لليخت"/>
            <column name="name_en" value="Copy of the yacht's wireless license"/>
            <column name="code" value="Copy_yacht_wireless_license"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v7" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="41"/>
            <column name="name_ar" value="وثيقة التامين"/>
            <column name="name_en" value="Insurance document"/>
            <column name="code" value="Insurance_document"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v8" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="42"/>
            <column name="name_ar" value="نسخة من جوازات السفر للطاقم والركاب"/>
            <column name="name_en" value="Copy of passports for crew and passengers"/>
            <column name="code" value="Copy_passports_crew_passengers"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v9" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="43"/>
            <column name="name_ar" value="شهادات الربان والطاقم المتواجدين على متن اليخت"/>
            <column name="name_en" value="Testimonies of the captain and crew on board the yacht"/>
            <column name="code" value="Testimonies_captain_crew_yacht"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v10" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="44"/>
            <column name="name_ar" value="قائمة بيانات الركاب"/>
            <column name="name_en" value="Passenger Data List"/>
            <column name="code" value="Passenger_Data_List"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241025000000-new-types-v11" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="45"/>
            <column name="name_ar" value="تقرير الفحص العام"/>
            <column name="name_en" value="General inspection report"/>
            <column name="code" value="General_Inspection_Report"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>


    <changeSet id="20241025000000-new-types-v12" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="46"/>
            <column name="name_ar" value="شهادة الامتثال الصادرة من دولة العلم"/>
            <column name="name_en" value="Certificate of Compliance issued by the flag state"/>
            <column name="code" value="Certificate_Compliance_flag"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>


    <changeSet id="20241025000000-new-types-v13" author="seissa">
        <insert tableName="attachment_type">
            <column name="id" value="47"/>
            <column name="name_ar" value="البيانات التعريفية لمالك اليخت"/>
            <column name="name_en" value="Yacht Owner Identification Data"/>
            <column name="code" value="Yacht_Owner_Identification_Data"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>

    <changeSet id="20241216000000-new-types-v14" author="faalkanhal">
        <insert tableName="attachment_type">
            <column name="id" value="48"/>
            <column name="name_ar" value="بيانات الطاقم وقائمة الركاب"/>
            <column name="name_en" value="Crew Data and Passenger List"/>
            <column name="code" value="Crew_Data_Passenger_List"/>
            <column name="mandatory" value="true"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="49"/>
            <column name="name_ar" value="شهادات الامتثال الصادرة هيئة التصنيف"/>
            <column name="name_en" value="Compliance Certificates issued by the Classification Authority"/>
            <column name="code" value="Compliance_Certificates_By_ClassificationAuthority"/>
            <column name="mandatory" value="true"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" value="50"/>
            <column name="name_ar" value="جوازات سفر القبطان والطاقم"/>
            <column name="name_en" value="Passports of the Captain and Crew"/>
            <column name="code" value="Passport_Captain_and_Crew"/>
            <column name="mandatory" value="true"/>
        </insert>
    </changeSet>
    <changeSet id="20250427105500_arrival_departure_attachment-types" author="faalkanhal">
    <insert tableName="attachment_type">
        <column name="id" value="63"/>
        <column name="name_ar" value="شهادة التخليص النهائية"/>
        <column name="name_en" value="Clearance Certificate Final"/>
        <column name="code" value="DEP_CLEARANCE_CERTIFICATE_Final"/>
        <column name="mandatory" value="false"/>
    </insert>
    </changeSet>
    <changeSet id="20250611140000_new_attachment_type" author="rsobh">

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="مستند الزيارة"/>
            <column name="name_en" value="Visit Document"/>
            <column name="code" value="Visit_Document"/>
            <column name="mandatory" value="true"/>
            <column name="record_activity" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>
    </changeSet>

    <changeSet id="20250612140000_new_attachment_type" author="rsobh">

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="إثبات الرحلة التأجيرية"/>
            <column name="name_en" value="Proof of Rental Trip"/>
            <column name="code" value="TOURISM_PERMIT_RENTAL_DOCUMENT"/>
            <column name="mandatory" value="true"/>
            <column name="record_activity" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>
    </changeSet>

    <changeSet id="20250427105502_attachment-types" author="osama">
        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="هوية مالك المرسى"/>
            <column name="name_en" value="Marina owner's ID"/>
            <column name="code" value="IDENTITY_OF_THE_OWNER_OF_THE_MARINA"/>
            <column name="mandatory" value="false"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="شهادة الامن السيبراني"/>
            <column name="name_en" value="Cyber Security certificate"/>
            <column name="code" value="CYBER_SECURITY_CERTIFICATE"/>
            <column name="mandatory" value="false"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="الملف التعريفي للشركة"/>
            <column name="name_en" value="Company profile"/>
            <column name="code" value="COMPANY_PROFILE"/>
            <column name="mandatory" value="false"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="إثبات ملكية المرسى"/>
            <column name="name_en" value="Proof of marina ownership"/>
            <column name="code" value="PROOF_OF_OWNERSHIP_OF_THE_MARINA"/>
            <column name="mandatory" value="false"/>
        </insert>
    </changeSet>

    <changeSet id="20250721045502_attachment-types" author="faalkanhal">
        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="السجل التجاري او الهوية لمالك الموقع (الشاطئ)"/>
            <column name="name_en" value="Commercial registration or identity of the owner ot the site (beach)"/>
            <column name="code" value="COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="إثبات الملكية للموقع"/>
            <column name="name_en" value="Site verification"/>
            <column name="code" value="SITE_VERIFICATION"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="شهادة الإشغال الصادرة عن الجهة ذات الاختصاص المكاني"/>
            <column name="name_en" value="An occupancy certificate issued by the competent local authority"/>
            <column name="code" value="OCCUPANCY_CERTIFICATE"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="تخطيط الحيز البحري لمناطق ممارسة الأنشطة البحرية في مياه الشواطئ"/>
            <column name="name_en" value="Maritime space planning for areas where maritime activities are carried out in the coastal water"/>
            <column name="code" value="MARITIME_SPACE_PLANNING"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="بيان بعدد وأسماء المنقذين"/>
            <column name="name_en" value="A statement of the number and names of lifeguards"/>
            <column name="code" value="LIFEGUARDS_LIST"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="التصريح البيئي للتشغيل الصادر عن المركز الوطني للرقابة على الالتزام البيئي"/>
            <column name="name_en" value="An environmental operating permit issued by the NCEC"/>
            <column name="code" value="ENVIRONMENTAL_PERMIT"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="خطة سلامة الشواطئ"/>
            <column name="name_en" value="Safety and health management plans"/>
            <column name="code" value="SAFETY_MANAGEMENT_PLAN"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="بيان بأدوات وتجهيزات السلامة المائية للشواطئ"/>
            <column name="name_en" value="A statement of water safety tools and equipment for beaches"/>
            <column name="code" value="WATER_SAFETY_EQUIPMENT"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>

        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="بيان بعدد وتراخيص الوسائط البحرية، والأنشطة البحرية المرخصة التي تمارس في مياه الشواطئ من الجهات المختصة"/>
            <column name="name_en" value="A statement of the number and licenses of marine vehicles and licensed marine activities carried out in coastal waters by the competent authorities"/>
            <column name="code" value="MARINE_VEHICLES_AND_ACTIVITIES"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>
    </changeSet>

    <changeSet id="20250806230000_visa_request_attachment-types" author="rsobh">
        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="صورة جواز السفر"/>
            <column name="name_en" value="Passport Copy"/>
            <column name="code" value="VISA_APPLICANT_PASSPORT_COPY"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>
        <insert tableName="attachment_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM attachment_type)"/>
            <column name="name_ar" value="صورة شخصية"/>
            <column name="name_en" value="Personal Photo"/>
            <column name="code" value="VISA_APPLICANT_PERSONAL_PHOTO"/>
            <column name="record_activity" value="true"/>
            <column name="mandatory" value="true"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
        </insert>
    </changeSet>

</databaseChangeLog>
