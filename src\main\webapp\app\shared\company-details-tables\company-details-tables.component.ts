import { Component, inject, Input, OnInit } from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';
import TranslateDirective from '../language/translate.directive';
import { TranslateService } from '@ngx-translate/core';
import { fork<PERSON>oin } from 'rxjs';
import { CompanyDetailsService } from './company_details.service';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { ICompanyOwner } from '../../account/register/model/company.owner.model';
import { UserService } from '../../entities/user/user.service';
import { AccountService } from '../../core/auth/account.service';

@Component({
  selector: 'jhi-company-details-tables',
  standalone: true,
  imports: [NgForOf, NgIf, TranslateDirective],
  templateUrl: './company-details-tables.component.html',
  styleUrl: './company-details-tables.component.scss',
})
export class CompanyDetailsTablesComponent implements OnInit {
  protected translateService = inject(TranslateService);
  protected accountService = inject(AccountService);
  @Input() crNumber!: string;
  company: any;
  owners: any[] = [];
  managers: any[] = [];
  branches: any[] = [];

  loading = true;
  error = false;

  constructor(private companyService: CompanyDetailsService) {}

  ngOnInit(): void {
    this.loadCompanyDetails();
  }
  trackIdentity(_index: number, item: any): string {
    return item.name;
  }
  loadCompanyDetails(): void {
    this.loading = true;
    this.error = false;

    this.companyService.getCompanyInfo(this.crNumber).subscribe({
      next: company => {
        this.company = this.isArabic() ? company.body.crInfoWathqResponseAr : company.body.crInfoWathqResponseEn;
        this.managers = this.isArabic() ? company.body.managersAr : company.body.managersEn;

        // Now fetch owners
        this.companyService.getCompanyOwners(this.crNumber).subscribe({
          next: owners => {
            this.owners = owners.body;

            // Now fetch branches
            this.companyService.getCompanyBranches(this.crNumber).subscribe({
              next: branches => {
                this.branches = branches.body;
                this.loading = false;
              },
              error: () => {
                this.error = true;
                this.loading = false;
              },
            });
          },
          error: () => {
            this.error = true;
            this.loading = false;
          },
        });
      },
      error: () => {
        this.error = true;
        this.loading = false;
      },
    });
  }

  isArabic() {
    if (this.translateService && this.translateService!.currentLang) {
      let currentLanguage = this.translateService!.currentLang;
      currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
      return currentLanguage.startsWith('ar', 0);
    }
    return 'ar';
  }
  get companyDetailsFields() {
    return [
      { label: this.isArabic() ? 'اسم الشركة' : 'Company Name', value: this.company?.name },
      { label: this.isArabic() ? 'رقم السجل التجاري' : 'CR Number', value: this.company?.crNumber },
      { label: this.isArabic() ? 'الرقم الوطني للسجل التجاري' : 'CR National Number', value: this.company?.crNationalNumber },
      { label: this.isArabic() ? 'تاريخ الإصدار (هجري)' : 'Issue Date (Hijri)', value: this.company?.issueDateHijri },
      { label: this.isArabic() ? 'نوع الكيان' : 'Entity Type', value: this.company?.entityType?.formName },
      { label: this.isArabic() ? 'حالة السجل' : 'Status', value: this.company?.status?.name },
      {
        label: this.isArabic() ? 'تاريخ التأكيد (هجري)' : 'Confirmation Date (Hijri)',
        value: this.company?.status?.confirmationDate?.hijri || '-',
      },
      {
        label: this.isArabic() ? 'تاريخ الإيقاف (هجري)' : 'Suspension Date (Hijri)',
        value: this.company?.status?.suspensionDate?.hijri || '-',
      },
      {
        label: this.isArabic() ? 'يوجد نشاط إلكتروني' : 'Has E-Commerce',
        value: this.company?.hasEcommerce ? (this.isArabic() ? 'نعم' : 'Yes') : this.isArabic() ? 'لا' : 'No',
      },
      { label: this.isArabic() ? 'المدينة الرئيسية' : 'Headquarter City', value: this.company?.headquarterCityName },
      { label: this.isArabic() ? 'رأس المال (عام)' : 'General Capital', value: this.company?.crCapital },
      { label: this.isArabic() ? 'العملة' : 'Currency', value: this.company?.capital?.currencyName },
      { label: this.isArabic() ? 'رأس المال النقدي' : 'Cash Capital', value: this.company?.capital?.contributionCapital?.cashCapital },
      { label: this.isArabic() ? 'رأس المال العيني' : 'In-Kind Capital', value: this.company?.capital?.contributionCapital?.inKindCapital },
      {
        label: this.isArabic() ? 'نسبة المساهمة' : 'Contribution Value (%)',
        value: `${this.company?.capital?.contributionCapital?.contributionValue}%`,
      },
      {
        label: this.isArabic() ? 'إجمالي المساهمة النقدية' : 'Total Cash Contribution',
        value: this.company?.capital?.contributionCapital?.totalCashContribution,
      },
      {
        label: this.isArabic() ? 'إجمالي المساهمة العينية' : 'Total In-Kind Contribution',
        value: this.company?.capital?.contributionCapital?.totalInKindContribution,
      },
      { label: this.isArabic() ? 'رقم الهاتف الثابت' : 'Phone Number', value: this.company?.contactInfo?.phoneNo },
      { label: this.isArabic() ? 'رقم الجوال' : 'Mobile Number', value: this.company?.contactInfo?.mobileNo },
      { label: this.isArabic() ? 'البريد الإلكتروني' : 'Email', value: this.company?.contactInfo?.email },
      { label: this.isArabic() ? 'الموقع الإلكتروني' : 'Website', value: this.company?.contactInfo?.websiteUrl },
      { label: this.isArabic() ? 'المدينة' : 'City', value: this.company?.address?.national?.city },
      { label: this.isArabic() ? 'الحي' : 'District', value: this.company?.address?.national?.districtName },
      { label: this.isArabic() ? 'اسم الشارع' : 'Street Name', value: this.company?.address?.national?.streetName },
      { label: this.isArabic() ? 'الرمز البريدي' : 'Zip Code', value: this.company?.address?.national?.zipcode },
    ];
  }
  isServiceProvider() {
    return this.accountService.hasAnyAuthority(['ROLE_SERVICE_PROVIDER']);
  }
}
