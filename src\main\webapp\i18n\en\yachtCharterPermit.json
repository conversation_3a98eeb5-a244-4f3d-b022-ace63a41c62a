{"srsaApp": {"yachtCharterPermit": {"home": {"title": "Yacht Charter Permits", "refreshListLabel": "Refresh list", "createLabel": "Create a new Yacht Charter Permit", "createOrEditLabel": "Create or edit a Yacht Charter Permit", "notFound": "No Yacht Charter Permits found"}, "created": "A new Yacht Charter Permit is created with identifier {{ param }}", "updated": "A Yacht Charter Permit is updated with identifier {{ param }}", "deleted": "A Yacht Charter Permit is deleted with identifier {{ param }}", "delete": {"question": "Are you sure you want to delete Yacht Charter Permit {{ id }}?"}, "detail": {"title": "Yacht Charter Permit", "serviceTerm1": "The user is an MTA", "serviceTerm2": "The MTA has an active license and delegation", "step1": "Fill Application form", "step2": "Submit Application for review", "step3": "Display the issued license"}, "status": {"NEW": "New", "DRAFT": "DRAFT", "DELETED": "Deleted", "ACTIVE": "Active", "EXPIRED": "Expired", "RETURNED": "Returned", "CANCELLED": "Cancelled", "null": "", "PENDING_REVIEW": "Pending Review", "PENDING_REPORT_APPROVAL": "Pending Report Approval", "PENDING_MANAGEMENT_LICENSING": "Pending Management Licensing", "RETURNED_INSPECTION_OFFICER": "Returned by inspection officer", "UNDER_PROCESS": "Under Process", "PENDING_INSPECTION": "Pending Inspection", "UNDER_INSPECTION": "Under Inspection", "PENDING_VISIT": "Pending Office Inspection", "REJECTED": "Rejected", "COMPLETED": "Completed", "VISIT_COMPLETED": "Visit Completed", "PENDING_PAYMENT": "Pending Payment", "RETURNED_LICENSING_OFFICER": "Returned by licensing officer", "RETURNED_LICENSING_MANAGER": "Returned by licensing manager"}, "id": "ID", "marineOwnerNameAr": "Marine Owner Name Ar", "marineOwnerNameEn": "Marine Owner Name En", "marineOwnerIdNumber": "Marine Owner Id Number", "marineOwnerPassportNumber": "Marine Owner Passport Number", "delegatorNameAr": "Delegator Name Ar", "delegatorNameEn": "Delegator Name En", "delegatorMobileNumber": "Delegator Mobile Number", "delegatorEmailNumber": "Delegator Email Number", "permitNumber": "License Number", "requestStatus": "Request Status", "marineColor": "Marine Color", "engineType": "Engine Type", "permitActivityLocation": "Permit Activity Location", "permitActivityLatitude": "Permit Activity Latitude", "permitActivityLogitude": "Permit Activity Logitude", "noOfPassengers": "No Of Passengers", "noOfCrew": "No Of Crew", "captainName": "Captain Name", "anchorageArea": "Anchorage Area", "chartererName": "Charterer Name", "chartererNumber": "Charterer Number", "startDate": "Start Date", "endDate": "End Date", "captainMobileNumber": "Captain Mobile Number", "captainPassportNumber": "Captain Passport Number", "arrivalDate": "Arrival Date", "departureDate": "Departure Date", "disclosureChecked": "Disclosure Checked", "passengers": "Passengers", "marineMedium": "Marine Medium", "marineCompanyOwner": "Marine Company Owner", "marineOwner": "Marine Owner", "marineDelegatorUser": "Marine Delegator User", "marineDelegatorCompany": "Marine Delegator Company", "arrivalPort": "Destination", "departurePort": "Departure Port", "captainNatinoality": "Captain <PERSON><PERSON>", "allRequests": "Licenses List", "permitRequests": "Licenses Requests", "newRequests": " New Requests", "select": "Select", "returnRq": "Return request for more info", "approveRq": "Approve", "returnRqConfirm": "Are you sure you want to return the request ?", "activity": "Activities", "otherActivity": "Other Activities", "yacht_technical_license": "Yacht Technical License (Leisure Tourism)", "yacht_navigational_license": "Yacht Navigational License", "valid_technical_inspection_report": "Valid Technical Inspection Report", "tourist_shipping_agent_license": "Tourist Shipping Agent License", "yacht_charter_company_license": "The Yacht Charter Company License", "owner_identification_data": "The Owner's Identification Data", "registration_and_technical_certificates": "Registration Certificate and Technical Certificates of the Yacht", "minimum_safe_manning_document": "Minimum Safe Finening Document Attachment", "marine_captain_certificate": "Marine Captain Certificate", "insurance_certificate": "Insurance Certificate", "docking_contract": "Docking Contract/Berthing License", "approved_tenancy_contract": "Approved Tenancy Contract", "addAccessories": "Add Accessories", "accessories": "Accessory", "chassisNumber": "<PERSON><PERSON><PERSON> Number", "accessorieColor": "Accessory Color", "noOfaccessorie": "No Of Accessory ", "purpose": "Purpose Of Visit", "visitDate": "Inspection Date", "visitStatus": "Inspection Status", "statusVisit": {"SCHEDULED": "SCHEDULED", "COMPLETED": "COMPLETED", "NOT_VISITED": "NOT VISITED"}, "visitReschedule": "Reschedule Inspection", "visitComplete": "Complete Inspection", "visitEdit": "Edit the Inspection Report", "visitSchedule": "Send for Inspection", "visitScheduleTitle": "Select Inspection Date", "confirmVisit": "Confirm Inspection", "uploadVisitDocs": "Upload Inspection Documents", "visitReport": "Inspection Report", "visitRequired": "Does the request require an inspection ?", "navigationalData": "Navigational License Data", "Contract_concluded_tourism_manager": "Contract concluded with the tourism manager approved by the Authority", "Document_yacht_planned_route_geographical_area": "Document the yacht's planned route, duration of stay and sailing areas within the geographical area", "Yacht_Registration_Certificate": "Yacht Registration Certificate", "Copy_contract_sidewalk": "Copy of the birth contract", "Crew_Data_List_Visas": "List of crew members' details and their visas", "Copy_yacht_wireless_license": "Copy of the yacht's wireless license", "Insurance_document": "Insurance document", "Copy_passports_crew_passengers": "Copy of passports for crew and passengers", "Testimonies_captain_crew_yacht": "Testimonies of the captain and crew on board the yacht", "Passenger_Data_List": "Passenger Data List", "General_Inspection_Report": "General inspection report", "Certificate_Compliance_flag": "Certificate of Compliance issued by the flag state", "Yacht_Owner_Identification_Data": "Identification Details of the Yacht Owner", "EXCEPTIONAL_DOC": "Exceptional approval documents ", "Tech_Visit_Report_DOC": "Visit Report document", "exceptionApproval": "Is It Exempted Form Condition", "feeRequired": "Is It Exempted Form Fees", "yachtTechnicalLicense": "Yacht Technical License", "yachtNavigationLicense": "Yacht Navigation License", "validTechnicalInspectionReport": "Valid Technical Inspection Report", "copyOfTouristMaritimeAgentLicense": "Copy of Tourist Maritime Agent License", "copyOfOwnerIdentificationData": "Copy of Owner’s Identification Data", "yachtRegistrationAndTechnicalCertificates": "Yacht Registration Certificate and Technical Certificates", "minimumSafeManningDocument": "Minimum Safe Manning Document", "captainsMaritimeCertificate": "Captain’s Maritime Certificate", "insuranceCertificate": "Insurance Certificate", "copyOfYachtRentalCompanyLicense": "Copy of Yacht Rental Company License", "mooringContractLicense": "Mooring Contract / Mooring License", "approvedRentalContract": "Approved Rental Contract", "yachtRentalFacility": "Business account with a yacht rental facility license", "error": {"noValidLicense": "You don't have a valid Yacht Rental Facility License. Please obtain a valid license before starting the service.", "checkLicense": "An error occurred while checking your license. Please try again later."}}}}