<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (licenseRequest()) {
      <div>
        <h2 data-cy="licenseRequestDetailsHeading"><span jhiTranslate="srsaApp.licenseRequest.detail.title">License Request</span></h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.id">ID</span></dt>
          <dd>
            <span>{{ licenseRequest()!.id }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfYearsExperience">No Of Years Experience</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfYearsExperience }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfCountryCompanyOpr">No Of Country Company Opr</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfCountryCompanyOpr }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfManagedMaritimeMediums">No Of Managed Maritime Mediums</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfManagedMaritimeMediums }}</span>
          </dd>
          <dt>
            <span jhiTranslate="srsaApp.licenseRequest.noOfOwnedMaritimeMediumsWorld">No Of Owned Maritime Mediums Ksa World</span>
          </dt>
          <dd>
            <span>{{ licenseRequest()!.noOfOwnedMaritimeMediumsWorld }}</span>
          </dd>
          <dt>
            <span jhiTranslate="srsaApp.licenseRequest.noOfOwnedMaritimeMediumsKsa">No Of Owned Maritime Mediums Ksa World</span>
          </dt>
          <dd>
            <span>{{ licenseRequest()!.noOfOwnedMaritimeMediumsKsa }}</span>
          </dd>
          <dt>
            <span jhiTranslate="srsaApp.licenseRequest.noOfManagedMaritimeMediumsGeneral">No Of Managed Maritime Mediums General</span>
          </dt>
          <dd>
            <span>{{ licenseRequest()!.noOfManagedMaritimeMediumsGeneral }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.requestedLicenseDuration">Requested License Duration</span></dt>
          <dd>
            <span [jhiTranslate]="'srsaApp.LicenseDuration.' + (licenseRequest()!.requestedLicenseDuration ?? 'null')">{{
              { null: '', ONE_YEAR: 'ONE_YEAR', TWO_YEAR: 'TWO_YEAR', THREE_YEAR: 'THREE_YEAR' }[
                licenseRequest()!.requestedLicenseDuration ?? 'null'
              ]
            }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.marinaOwnerName">Marina Owner Name</span></dt>
          <dd>
            <span>{{ licenseRequest()!.marinaOwnerNameAr }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.marinaCrNumber">Marina Cr Number</span></dt>
          <dd>
            <span>{{ licenseRequest()!.marinaCrNumber }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.marinaCrNameAr">Marina Cr Name Ar</span></dt>
          <dd>
            <span>{{ licenseRequest()!.marinaCrNameAr }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.marinaCrNameEn">Marina Cr Name En</span></dt>
          <dd>
            <span>{{ licenseRequest()!.marinaCrNameEn }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.numberOfDocks">Number Of Docks</span></dt>
          <dd>
            <span>{{ licenseRequest()!.numberOfDocks }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.dockLength">Dock Length</span></dt>
          <dd>
            <span>{{ licenseRequest()!.dockLength }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.dockDepth">Dock Depth</span></dt>
          <dd>
            <span>{{ licenseRequest()!.dockDepth }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfWaterStations">No Of Water Stations</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfWaterStations }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfWaterStationsPerPlatform">No Of Water Stations Per Platform</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfWaterStationsPerPlatform }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfFuelStations">No Of Fuel Stations</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfFuelStations }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfWasteStations">No Of Waste Stations</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfWasteStations }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfWasteStationsPerPlatform">No Of Waste Stations Per Platform</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfWasteStationsPerPlatform }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfElectricChargingStations">No Of Electric Charging Stations</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfElectricChargingStations }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfControlTowers">No Of Control Towers</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfControlTowers }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.noOfMarinaFacilities">No Of Marina Facilities</span></dt>
          <dd>
            <span>{{ licenseRequest()!.noOfMarinaFacilities }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.boatsCapacity">Boats Capacity</span></dt>
          <dd>
            <span>{{ licenseRequest()!.boatsCapacity }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.yachtsCapacity">Yachts Capacity</span></dt>
          <dd>
            <span>{{ licenseRequest()!.yachtsCapacity }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.otherMaritimeMediumCapacity">Other Maritime Medium Capacity</span></dt>
          <dd>
            <span>{{ licenseRequest()!.numberOfOtherMarineVessels }}</span>
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.marinaLocation">Marina Location</span></dt>
          <dd>
            @if (licenseRequest()!.marinaLocation) {
              <div>
                <a [routerLink]="['/national-address', licenseRequest()!.marinaLocation?.id, 'view']">{{
                  licenseRequest()!.marinaLocation?.id
                }}</a>
              </div>
            }
          </dd>
          <dt><span jhiTranslate="srsaApp.licenseRequest.maritimeTourismAgent">Maritime Tourism Agent</span></dt>
          <dd>
            @if (licenseRequest()!.maritimeTourismAgent) {
              <div>
                <a [routerLink]="['/company', licenseRequest()!.maritimeTourismAgent?.id, 'view']">{{
                  licenseRequest()!.maritimeTourismAgent?.id
                }}</a>
              </div>
            }
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">رجوع</span>
        </button>

        <button type="button" [routerLink]="['/license-request', licenseRequest()!.id, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">تعديل</span>
        </button>
      </div>
    }
  </div>
</div>
