import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MarineMediumService } from '../../marine-medium/marine-medium.service';
import { ICompany } from 'app/entities/company/company.model';
import { CompanyService } from 'app/entities/company/service/company.service';
import { UserService } from 'app/entities/user/service/user.service';
import { YachtCharterPermitStatusType } from 'app/entities/enumerations/yacht-charter-permit-status-type.model';
import { YachtCharterPermitService } from '../service/yacht-charter-permit.service';
import { IYachtCharterPermit, NewYachtCharterPermit } from '../yacht-charter-permit.model';
import { YachtCharterPermitFormGroup, YachtCharterPermitFormService } from './yacht-charter-permit-form.service';
import { MarineMedium } from '../../marine-medium/model/marine-medium.model';
import { DropzoneConfigInterface, DropzoneModule } from 'ngx-dropzone-wrapper';
import FormatMediumDatetimePipe from '../../../shared/date/format-medium-datetime.pipe';
import HasAnyAuthorityDirective from '../../../shared/auth/has-any-authority.directive';
import { MultiSelectModule } from 'primeng/multiselect';
import { SrsaAlertComponent } from '../../../shared/alert/srsa-alert/srsa.alert.component';
import { MarineAccessories } from '../../enumerations/marine-accessories.model';
import { ILicenseProvider } from '../../license-provider/license-provider.model';
import dayjs from 'dayjs/esm';
import { IServiceTypeLiabilityType } from '../../service-type-liability-type/service-type-liability-type.model';
import { IServiceType } from '../../service-type/service-type.model';
import { FileKeyValuePair } from '../../mta/update/mta-update.component';
import { IAttachment } from '../../attachment/attachment.model';
import { IMaritimeMedium } from '../../maritime-medium/maritime-medium.model';
import { IMaritimeTourismServices } from '../../maritime-tourism-services/maritime-tourism-services.model';
import { ILicenseRequest } from '../../license-request/license-request.model';
import { IInvestmentType } from '../../investment-type/investment-type.model';
import { ICountry } from '../../country/country.model';
import { ICompanyOwner } from '../../../account/register/model/company.owner.model';
import { MarineTourOperatorServicesService } from '../../marine-tour-operator-services/service/marine-tour-operator-services.service';
import { TranslateService } from '@ngx-translate/core';
import { AccountService } from '../../../core/auth/account.service';
import { InvestmentTypeService } from '../../investment-type/service/investment-type.service';
import { MaritimeMediumService } from '../../maritime-medium/service/maritime-medium.service';
import { MaritimeTourismServicesService } from '../../maritime-tourism-services/service/maritime-tourism-services.service';
import { ProvidedLanguagesService } from '../../provided-languages/service/provided-languages.service';
import { ServiceTypeDocumentTypeService } from '../../service-type-document-type/service/service-type-document-type.service';
import { ServiceTypeTermsConditionsService } from '../../service-type-terms-conditions/service/service-type-terms-conditions.service';
import { ServiceTypeLiabilityTypeService } from '../../service-type-liability-type/service/service-type-liability-type.service';
import { FuelTypesService } from '../../fuel-types/service/fuel-types.service';
import { LicenseProviderService } from '../../license-provider/service/license-provider.service';
import { ServiceTypeService } from '../../service-type/service/service-type.service';
import { LicenseRequestService } from '../../license-request/service/license-request.service';
import { StatusType } from '../../enumerations/status-type.model';
import { Authority } from '../../../config/authority.constants';
import { GregorianCalendarComponent } from '../../../shared/gregorian-calendar/gregorian-calendar.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { PageTitleComponent } from 'app/layouts/sub/page-title/page-title.component';
import { TabViewModule } from 'primeng/tabview';
import { PageTitleService } from 'app/core/util/page-title.service';
import { SrsaAlertService } from '../../../shared/alert/srsa-alert/srsa.alert.service';
import { PermitRequestService } from '../../permit-request/service/permit-request.service';

declare let $: any;

@Component({
  standalone: true,
  selector: 'jhi-yacht-charter-permit-update',
  styleUrl: './yacht-charter-permit-update.component.scss',
  templateUrl: './yacht-charter-permit-update.component.html',
  imports: [
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    DropzoneModule,
    FormatMediumDatetimePipe,
    HasAnyAuthorityDirective,
    MultiSelectModule,
    SrsaAlertComponent,
    RouterLink,
    GregorianCalendarComponent,
    TabViewModule,
    PageTitleComponent,
    AngularSvgIconModule,
  ],
})
export class YachtCharterPermitUpdateComponent implements OnInit, OnDestroy {
  readonly Authority = Authority;
  editMode: boolean = false;
  startDate: dayjs.Dayjs | null = null;
  endDate: dayjs.Dayjs | null = null;
  registrationNumber: string = '';
  ownerIdNumber: string = '';
  marineAccessories = Object.keys(MarineAccessories);
  requestTypeStatusEnum?: keyof typeof YachtCharterPermitStatusType | null;
  step = 0;
  isSaving = false;
  CrNeedRenewal = false;
  CrExpired = false;
  // companiesSharedCollection: ICompany[] = [];
  // usersSharedCollection: IUser[] = [];
  investmentType: { nameAr: any; nameEn: any } = { nameAr: '', nameEn: '' };
  selectedLicenseProvider: ILicenseProvider | null = null;
  disclaimerChecked = false;
  liabilityChecked = false;
  serviceTypeLiabilityType?: IServiceTypeLiabilityType[];
  selectedServiceType: IServiceType | null = null;
  marineMediums: MarineMedium[] = [];

  currentStep = 1;
  step1: boolean = true;
  step2: boolean = false;
  step3: boolean = false;
  zoom = 18;
  center = {
    lat: 24.746604039896134,
    lng: 46.724558262553245,
  };

  fileSizeError = false;

  requiredDocuments = [
    {
      val_ar: 'الترخيص الفني لليخت',
      val_en: 'Yacht Technical License',
    },
    {
      val_ar: 'الترخيص الملاحي لليخت',
      val_en: 'Yacht Navigation License',
    },
    {
      val_ar: 'تقرير معاينة فنية ساري المفعول',
      val_en: 'Valid Technical Inspection Report',
    },
    {
      val_ar: 'نسخة من رخصة الوكيل الملاحي السياحي',
      val_en: 'Copy of Tourist Maritime Agent License',
    },
    {
      val_ar: 'نسخة من البيانات التعريفية للمالك',
      val_en: 'Copy of Owner’s Identification Data',
    },
    {
      val_ar: 'شهادة التسجيل والمعدات الفنية لليخت',
      val_en: 'Yacht Registration Certificate and Technical Certificates',
    },
    {
      val_ar: 'وثيقة الحد الأدنى للتنظيم الآمن',
      val_en: 'Minimum Safe Manning Document',
    },
    {
      val_ar: 'شهادة القبطان البحرية',
      val_en: 'Captain’s Maritime Certificate',
    },
    {
      val_ar: 'شهادة التأمين',
      val_en: 'Insurance Certificate',
    },
    {
      val_ar: 'نسخة من ترخيص شركة تأجير اليخوت',
      val_en: 'Copy of Yacht Rental Company License',
    },
    {
      val_ar: 'عقد رسو / ترخيص',
      val_en: 'Mooring Contract / Mooring License',
    },
    {
      val_ar: 'عقد الإيجار المعتمد',
      val_en: 'Approved Rental Contract',
    },
  ];

  documents: FileKeyValuePair[] = [] as FileKeyValuePair[];
  documentTypes: any[] = [];
  yachtTechnicalLicenseAttachment: IAttachment | undefined;
  yachtNavigationalLicenseAttachment: IAttachment | undefined;
  validTechnicalInspectionReportAttachment: IAttachment | undefined;
  touristShippingAgentLicenseAttachment: IAttachment | undefined;
  ownerIdentificationDataAttachment: IAttachment | undefined;
  registrationCertificateAndTechnicalCertificatesOfYachtAttachment: IAttachment | undefined;
  minimumSafeFineningDocumentAttachment: IAttachment | undefined;
  marineCaptainCertificateAttachment: IAttachment | undefined;
  insuranceCertificateAttachment: IAttachment | undefined;
  yachtCharterCompanyLicenseAttachment: IAttachment | undefined;
  dockingContractAttachment: IAttachment | undefined;
  approvedTenancyContractAttachment: IAttachment | undefined;

  maritimeMediums?: IMaritimeMedium[];
  registeredCompany: any | null = null;
  displayOtherTextField = false;
  otherTextValue?: string | null = '';
  maritimeTourismServices?: IMaritimeTourismServices[] = [];
  licenseProvidersSharedCollection: ILicenseProvider[] = [];
  maritimeTourismAgentCompanies: ICompany[] = [];
  selectedAgent?: ICompany;
  selectedMarineMedium: MarineMedium | null = null;
  selectedMarineMediumModel: MarineMedium | null = null;
  subCompany: any | null = null;
  selectedSubCompany: ICompany | null = null;
  requiredCrField = false;
  licenseProvidersByCategory: ILicenseProvider[] = [];
  serviceTypesSharedCollection: IServiceType[] = [];
  licenseRequestsSharedCollection: ILicenseRequest[] = [];
  investmentTypes: IInvestmentType[] = [];
  selectedInvestmentType: IInvestmentType | null = null;
  invTypeId: number | null | undefined = 0;
  countries?: ICountry[] | null;
  companyOwners: any[] | null | undefined = [];
  relatedCompanies: any[] = [];
  permitNumber: any;

  validPermit = true;
  validPermitStartDate: any;
  validPermitEndDate: any;
  validPermitIndex = 0;
  readonly YachtCharterPermitStatusType = YachtCharterPermitStatusType;

  marineMediumService = inject(MarineMediumService);
  companyService = inject(CompanyService);
  userService = inject(UserService);
  activatedRoute = inject(ActivatedRoute);
  marineTourOperatorServicesService = inject(MarineTourOperatorServicesService);
  translateService = inject(TranslateService);
  accountService = inject(AccountService);
  investmentTypeService = inject(InvestmentTypeService);
  maritimeMediumService = inject(MaritimeMediumService);
  maritimeTourismServicesService = inject(MaritimeTourismServicesService);
  providedLanguagesService = inject(ProvidedLanguagesService);
  serviceTypeDocumentTypeService = inject(ServiceTypeDocumentTypeService);
  serviceTypeTermsConditionsService = inject(ServiceTypeTermsConditionsService);
  serviceTypeLiabilityTypeService = inject(ServiceTypeLiabilityTypeService);
  fuelTypesService = inject(FuelTypesService);
  licenseProviderService = inject(LicenseProviderService);
  serviceTypeService = inject(ServiceTypeService);
  licenseRequestService = inject(LicenseRequestService);
  router = inject(Router);
  yachtCharterPermit: IYachtCharterPermit | NewYachtCharterPermit | undefined;

  yachtCharterPermitService = inject(YachtCharterPermitService);
  yachtCharterPermitFormService = inject(YachtCharterPermitFormService);
  pageTitleService = inject(PageTitleService);
  alertService = inject(SrsaAlertService);
  permitRequestService = inject(PermitRequestService);
  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: YachtCharterPermitFormGroup = this.yachtCharterPermitFormService.createYachtCharterPermitFormGroup();
  wizardDisplay: boolean = false;
  showCrDetails: boolean = false;
  shownSteps: any[] = [];
  hidden1: boolean = true;
  hidden2: boolean = false;
  hidden3: boolean = false;
  hidden4: boolean = false;
  hidden5: boolean = false;
  hidden6: boolean = false;
  hidden7: boolean = false;
  hidden8: boolean = false;
  hidden9: boolean = false;
  hidden10: boolean = false;
  hidden11: boolean = false;
  hidden12: boolean = false;
  hidden13: boolean = false;
  hidden14: boolean = false;
  steps: any[] = [
    { titleAr: 'السجل التجاري ', titleEn: 'CR' },
    { titleAr: 'تحديد الواسطة البحرية', titleEn: 'Select Maritime Medium' },
    { titleAr: 'بيانات الطلب', titleEn: 'Request Info' },
    { titleAr: 'المرفقات', titleEn: 'Attachment' },
    { titleAr: 'معاينة الطلب', titleEn: 'Preview' },
  ];

  // TODO check this
  getFileConfig(): DropzoneConfigInterface {
    return {
      maxFilesize: 5,
      clickable: true,
      addRemoveLinks: true,
      maxFiles: 1,
      acceptedFiles: '.pdf,.jpeg,.jpg,.png',
      dictDefaultMessage: this.isArabic() ? 'اضغط هنا لرفع الملف' : 'Press here to upload file',
      dictInvalidFileType: this.isArabic()
        ? 'لا تستطيع رفع ملف من هذه الصيغة. (الصيغ المسموحة: pdf, jpeg ,jpg ,png)'
        : 'Cannot upload this file. (Allowed extensions: pdf, jpeg ,jpg ,png)',
      dictFileTooBig: this.isArabic() ? 'الحجم الأقصى للصورة هو 5 MB' : 'Maximum image size is 5 MB',
      url: SERVER_API_URL + '/api/files',
      //   headers: {'X-XSRF-TOKEN': this.cookieService.get('XSRF-TOKEN')}
    };
  }

  trackIdentity(_index: number, item: ICompanyOwner): string {
    return item.name;
  }

  ngOnInit(): void {
    this.center = {
      lat: 24.746604039896134,
      lng: 46.724558262553245,
    };
    this.activatedRoute.data.subscribe(({ yachtCharterPermit }) => {
      if (yachtCharterPermit) {
        this.yachtCharterPermitService.find(yachtCharterPermit.id).subscribe({
          next: response => {
            this.updateForm(response.body as IYachtCharterPermit);
          },
          error(err) {},
        });
      } else {
        this.yachtCharterPermit = <IYachtCharterPermit>{};
      }

      this.loadRelationshipsOptions();
      this.shownSteps = this.steps.filter(x => x.titleEn !== 'License Data');
    });

    this.pageTitleService.setBreadcrumb(this.translateService.instant('srsaApp.yachtCharterPermit.home.title'), '/yacht-charter-permit');

    // Load service type data dynamically instead of using hardcoded values
    this.serviceTypeService.findByCode('TC').subscribe({
      next: response => {
        this.selectedServiceType = response.body;
      },
      error() {
        // If there's an error loading the service type, log it
        console.error('Error loading service type with code TC');
      },
    });
  }

  previousState(): void {
    window.history.back();
  }

  onSelectedCR(event: any): void {
    this.selectedSubCompany = this.subCompany;
    this.companyService
      .getCrOwner(this.registeredCompany?.crNumber)
      .pipe(map((res: HttpResponse<ICompanyOwner[]>) => res.body ?? []))
      .subscribe((owner: ICompanyOwner[]) => {
        this.companyOwners = owner;
        for (const compOwner of this.companyOwners) {
          if (compOwner.nationalityCode != '1') {
            this.requiredCrField = true;
            break;
          }
        }
      });
  }

  onSelectedInvestmentType(): void {
    this.licenseProvidersByCategory = [];
    const invTypeId = this.editForm.get('invTypeId')?.value;
    this.invTypeId = invTypeId; // Keep the component property in sync for backward compatibility

    for (const licenseProvider of this.licenseProvidersSharedCollection) {
      if (invTypeId === licenseProvider.investmentType?.id) {
        this.licenseProvidersByCategory.push(licenseProvider);
      }
    }
    if (invTypeId) {
      this.investmentType = {
        nameAr: this.investmentTypes.find(x => x.id === invTypeId)!.nameAr,
        nameEn: this.investmentTypes.find(x => x.id === invTypeId)!.nameEn,
      };
    }
  }

  onSelectedLicenseProvider(event: any): void {
    this.selectedLicenseProvider = event;
  }

  isCurrentWizardInvalid(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.checkStep1();
      case 2:
        return this.checkStep2();
      case 3:
        return this.checkStep3();
      case 4:
        return this.checkStep4();
      case 5:
        return false;
      default:
        // If we reach an unknown step, consider it invalid
        return true;
    }
  }

  checkStep1(): boolean {
    if (
      (this.requiredCrField &&
        this.invTypeId !== 1 &&
        this.selectedLicenseProvider?.code === 'MISA' &&
        this.editForm.get('misaLicenseNumber')?.invalid) ??
      (this.requiredCrField &&
        this.invTypeId !== 1 &&
        this.selectedLicenseProvider?.code === 'ECZA' &&
        this.editForm.get('eczaLicenseNumber')?.invalid) ??
      (this.requiredCrField &&
        this.invTypeId !== 1 &&
        (this.selectedLicenseProvider?.code === null || this.selectedLicenseProvider?.code === undefined))
    ) {
      return true;
    }
    return false;
  }

  checkStep2(): boolean {
    return this.selectedMarineMedium === null || this.selectedMarineMedium === undefined || !this.validPermit;
  }

  checkStep3(): boolean {
    if (!this.selectedMarineMedium || !this.selectedAgent) {
      return true;
    }

    // Check each required input field individually
    if (!this.editForm.get('captainName')?.value || this.editForm.get('captainName')?.invalid) {
      return true;
    }

    if (!this.editForm.get('captainMobileNumber')?.value || this.editForm.get('captainMobileNumber')?.invalid) {
      return true;
    }

    if (!this.editForm.get('anchorageArea')?.value || this.editForm.get('anchorageArea')?.invalid) {
      return true;
    }

    if (!this.editForm.get('charterName')?.value || this.editForm.get('charterName')?.invalid) {
      return true;
    }

    if (!this.editForm.get('charterNumber')?.value || this.editForm.get('charterNumber')?.invalid) {
      return true;
    }

    if (!this.editForm.get('startDate')?.value) {
      return true;
    }

    if (!this.editForm.get('endDate')?.value) {
      return true;
    }

    // Check if end date is after start date
    const startDateValue = this.editForm.get('startDate')?.value;
    const endDateValue = this.editForm.get('endDate')?.value;
    if (startDateValue && endDateValue) {
      const startDate = new Date(startDateValue);
      const endDate = new Date(endDateValue);
      if (startDate > endDate) {
        return true;
      }
    }

    return false;
  }

  checkStep4(): boolean {
    const isValid =
      this.documents.some(doc => doc.key === 'validTechnicalInspectionReport') &&
      this.documents.some(doc => doc.key === 'copyOfOwnerIdentificationData') &&
      this.documents.some(doc => doc.key === 'yachtRegistrationAndTechnicalCertificates') &&
      this.documents.some(doc => doc.key === 'minimumSafeManningDocument') &&
      this.documents.some(doc => doc.key === 'insuranceCertificate') &&
      this.documents.some(doc => doc.key === 'copyOfYachtRentalCompanyLicense') &&
      this.documents.some(doc => doc.key === 'mooringContractLicense');
    if (!isValid) {
      return true;
    }
    return this.fileSizeError;
  }
  loadAllMarineMediums(): void {
    this.marineMediumService.getAvailableForTechTourismPermit().subscribe({
      next: response => {
        this.marineMediums = response.data as MarineMedium[];
        if (this.yachtCharterPermit?.marineMedium) {
          if (this.yachtCharterPermit?.marineMedium.id != null) {
            this.selectedMarineMediumModel =
              this.marineMediums.find(medium => medium.id == this.yachtCharterPermit?.marineMedium?.id) || null;
          }
        }
      },
      error: () => {},
    });
  }

  searchMarineByRegistrationAndOwnerId(): void {
    if (this.registrationNumber && this.ownerIdNumber) {
      this.marineMediumService.searchMarineByRegistrationAndOwnerId(this.registrationNumber, this.ownerIdNumber).subscribe({
        next: response => {
          if (response.data) {
            this.selectedMarineMedium = response.data as MarineMedium;
          }
        },
        error: err => {
          this.alertService.addAlert({
            type: 'danger',
            translationKey: err.error.message,
            source: 'main',
          });
        },
      });
    }
  }

  processMarineData(marineId: number): void {
    this.marineMediumService.getShipInfo(marineId).subscribe({
      next: response => {
        this.selectedMarineMedium = response.data as MarineMedium;
      },
      error: err => {
        this.alertService.addAlert({
          type: 'danger',
          translationKey: err.error.message,
          source: 'main',
        });
      },
    });
  }

  getMarineMedium(selectedMarine: MarineMedium | null): null {
    if (selectedMarine) {
      this.selectedMarineMedium = selectedMarine;
      if (this.selectedMarineMedium.id) {
        this.processMarineData(this.selectedMarineMedium.id);
      }
    }
    return null;
  }

  getMarineAttachment(id: number): void {
    this.marineMediumService.getAttachmentById(id).subscribe({
      next(response) {
        const url = window.URL.createObjectURL(response);
        window.open(url);
      },
      error() {},
    });
  }

  getAttachment(id: number): void {
    this.yachtCharterPermitService.getAttachmentById(id).subscribe({
      next(response) {
        const url = window.URL.createObjectURL(response);
        window.open(url);
      },
      error() {},
    });
  }

  selectAgent(selectedAgent: ICompany): void {
    this.selectedAgent = selectedAgent;
    this.step = 0;
  }

  setStartDate($event: any): void {
    this.startDate = $event;
    this.editForm.get('startDate')?.setValue($event);
  }

  setEndDate($event: any): void {
    this.endDate = $event;
    this.editForm.get('endDate')?.setValue($event);
  }

  onSaveError(): void {
    this.alertService.addAlert({
      type: 'danger',
      translationKey: 'srsaApp.yachtCharterPermit.error.save',
      source: 'main',
    });
  }

  onSaveFinalize(): void {
    this.isSaving = false;
  }

  isArabic(): boolean {
    if (this.translateService.currentLang) {
      let currentLanguage = this.translateService.currentLang;
      currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
      currentLanguage.includes('ar') ? $.calendars.instance('gregorian', 'ar-EG') : $.calendars.instance('gregorian', 'en');
      //  this.adjustActivity();
      return currentLanguage.includes('ar');
    }
    return true; // Default to Arabic if translateService or currentLang is not available
  }

  prepareRequestStatus(status: string): void {
    if (status === 'DRAFT') {
      this.requestTypeStatusEnum = YachtCharterPermitStatusType.DRAFT;
    }
    if (status === 'UNDER_REVIEW' || status === 'PENDING_REVIEW') {
      this.requestTypeStatusEnum = YachtCharterPermitStatusType.PENDING_REVIEW;
    }
    if (status === 'RETURNED') {
      if (this.yachtCharterPermit?.requestStatus === YachtCharterPermitStatusType.RETURNED_LICENSING_MANAGER) {
        this.requestTypeStatusEnum = StatusType.PENDING_MANAGEMENT_LICENSING;
      } else if (this.yachtCharterPermit?.requestStatus === YachtCharterPermitStatusType.RETURNED_LICENSING_OFFICER) {
        this.requestTypeStatusEnum = StatusType.UNDER_PROCESS;
      } else if (this.yachtCharterPermit?.requestStatus === YachtCharterPermitStatusType.RETURNED_INSPECTION_OFFICER) {
        this.requestTypeStatusEnum = StatusType.UNDER_INSPECTION;
      }
    }
  }

  wizardNextStep(): void {
    if (this.currentStep === 4) {
      this.isComponentBeingDestroyed = true;
    }
    if (this.currentStep < this.steps.length) {
      this.currentStep++;
    }
  }

  wizardPrevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  showWizard(): void {
    this.wizardDisplay = true;
  }

  /**
   * Shows the specified section by setting the corresponding hidden property to true
   * and all others to false.
   * @param sectionNumber The section number to show (1-16)
   */
  clickToShow(sectionNumber: number): void {
    // Reset all hidden properties to false
    for (let i = 1; i <= 16; i++) {
      (this as any)[`hidden${i}`] = false;
    }

    // Set the specified section to true
    if (sectionNumber >= 1 && sectionNumber <= 16) {
      (this as any)[`hidden${sectionNumber}`] = true;
    }
  }

  public onUploadFileSuccess(event: any, fileType: string): void {
    // If we update the attachment we remove already exist file
    if (this.yachtCharterPermit?.id !== null && this.yachtCharterPermit?.id !== undefined && this.yachtCharterPermit.attachments!) {
      for (const attachment of this.yachtCharterPermit.attachments) {
        if (attachment.attachmentType?.code === fileType && attachment.attachmentType.code !== 'AdditionalAttachment') {
          attachment.isRemoved = true;

          if (this.documents.some(doc => doc.key === fileType)) {
            this.documents.splice(
              this.documents.findIndex(doc => doc.key === fileType),
              1,
            );
          }
        }
      }
    }
    const args = event;
    this.fileSizeError = false;
    this.documents.push({ key: fileType, file: new File([args], args.name, { type: args.type }) });
    if (this.documents && this.documents.length > 0) {
      this.documentTypes = [];
      this.documents.forEach(doc => (this.documentTypes.indexOf(doc.key) < 0 ? this.documentTypes.push(doc.key) : null));
    }
  }

  public onUploadFileError(event: any): void {
    this.fileSizeError = true;
  }
  isComponentBeingDestroyed = false;

  public onFileRemoved(event: any, fileType: string): void {
    if (this.isComponentBeingDestroyed) {
      console.log('Dropzone removed due to DOM destruction, skipping...');
      return;
    }
    if (fileType === 'AdditionalAttachment' && this.documents.findIndex(doc => doc.key === fileType) !== -1) {
      this.documents.splice(
        this.documents.findIndex(doc => doc.file.name === event.name),
        1,
      );
    } else if (this.documents.findIndex(doc => doc.key === fileType) !== -1) {
      this.documents.splice(
        this.documents.findIndex(doc => doc.key === fileType),
        1,
      );
    }
    // this.document = undefined;
    this.fileSizeError = false;
  }

  save(): void {
    this.isSaving = true;
    const yachtCharterPermit = this.yachtCharterPermitFormService.getYachtCharterPermit(this.editForm);
    yachtCharterPermit.marineMedium = this.selectedMarineMedium;
    yachtCharterPermit.marineDelegatorCompany = this.selectedAgent;
    yachtCharterPermit.requestStatus = this.requestTypeStatusEnum;
    yachtCharterPermit.secondaryCompany = this.selectedSubCompany;
    yachtCharterPermit.branchDto = this.subCompany;

    // yachtCharterPermit.startDate = this.startDate;
    // yachtCharterPermit.endDate = this.endDate;
    this.subscribeToSaveResponse(this.yachtCharterPermitService.save(yachtCharterPermit, this.documents));
  }

  subscribeToSaveResponse(result: Observable<HttpResponse<IYachtCharterPermit>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  onSaveSuccess(): void {
    this.previousState();
  }

  /**
   * Cleanup when the component is destroyed
   */
  ngOnDestroy(): void {
    // Clear any alerts when navigating away from the page
    this.alertService.clearAlerts('main');
  }

  /**
   * Checks if the user has a valid yacht rental facility license
   * If not, shows an error message
   * If yes, opens the modal
   * @param event The click event
   */
  checkYachtRentalFacility(event: Event): void {
    event.preventDefault();
    event.stopPropagation();

    this.permitRequestService.hasYachtRentalFacility().subscribe({
      next: (hasValidLicense: boolean) => {
        if (hasValidLicense) {
          // If user has a valid license, proceed with opening the modal
          // Manually trigger the modal using jQuery
          $('#exampleModal').modal('show');
        } else {
          // If user doesn't have a valid license, show error message
          this.alertService.addAlert({
            type: 'danger',
            translationKey: 'srsaApp.yachtCharterPermit.error.noValidLicense',
            source: 'main',
          });
        }
      },
      error: () => {
        // If there's an error, show a generic error message
        this.alertService.addAlert({
          type: 'danger',
          translationKey: 'srsaApp.yachtCharterPermit.error.checkLicense',
          source: 'main',
        });
      },
    });
  }

  updateForm(yachtCharterPermit: IYachtCharterPermit): void {
    this.yachtCharterPermitFormService.resetForm(this.editForm, yachtCharterPermit);
    this.yachtCharterPermit = yachtCharterPermit;
    this.selectedMarineMedium = this.yachtCharterPermit.marineMedium!;
    // this.companiesSharedCollection = this.companyService.addCompanyToCollectionIfMissing<ICompany>(
    //   this.companiesSharedCollection,
    //   yachtCharterPermit.marineCompanyOwner,
    //   yachtCharterPermit.marineDelegatorCompany,
    // );
    // this.usersSharedCollection = this.userService.addUserToCollectionIfMissing<IUser>(
    //   this.usersSharedCollection,
    //   yachtCharterPermit.marineOwner,
    //   yachtCharterPermit.marineDelegatorUser,
    // );

    if (this.yachtCharterPermit.company?.companyOwners) {
      this.companyOwners = this.yachtCharterPermit.company.companyOwners;
      for (const compOwner of this.yachtCharterPermit.company.companyOwners) {
        if (compOwner.nationalityCode !== 'SA') {
          this.requiredCrField = true;
          break;
        }
      }
    }

    if (this.yachtCharterPermit.serviceType) {
      this.selectedServiceType = this.yachtCharterPermit.serviceType;
    }

    if (this.yachtCharterPermit.company) {
      this.registeredCompany = this.yachtCharterPermit.company;
    }
    if (this.yachtCharterPermit.secondaryCompany) {
      this.selectedSubCompany = this.yachtCharterPermit.secondaryCompany;
    }

    if (this.yachtCharterPermit.marineDelegatorCompany) {
      this.selectedAgent = this.yachtCharterPermit.marineDelegatorCompany;
    }

    if (this.yachtCharterPermit.marineMedium) {
      this.getMarineMedium(this.yachtCharterPermit.marineMedium);
    }

    if (this.yachtCharterPermit.attachments) {
      this.documentTypes = [];
      for (const file of this.yachtCharterPermit.attachments) {
        this.documentTypes.push(file.attachmentType?.code);

        switch (file.attachmentType?.code) {
          case 'yachtTechnicalLicense':
            this.yachtTechnicalLicenseAttachment = file;
            this.documents.push({
              key: 'yachtTechnicalLicense',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'yachtNavigationLicense':
            this.yachtNavigationalLicenseAttachment = file;
            this.documents.push({
              key: 'yachtNavigationLicense',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'validTechnicalInspectionReport':
            this.validTechnicalInspectionReportAttachment = file;
            this.documents.push({
              key: 'validTechnicalInspectionReport',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'copyOfTouristMaritimeAgentLicense':
            this.touristShippingAgentLicenseAttachment = file;
            this.documents.push({
              key: 'copyOfTouristMaritimeAgentLicense',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'copyOfOwnerIdentificationData':
            this.ownerIdentificationDataAttachment = file;
            this.documents.push({
              key: 'copyOfOwnerIdentificationData',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'yachtRegistrationAndTechnicalCertificates':
            this.registrationCertificateAndTechnicalCertificatesOfYachtAttachment = file;
            this.documents.push({
              key: 'yachtRegistrationAndTechnicalCertificates',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'minimumSafeManningDocument':
            this.minimumSafeFineningDocumentAttachment = file;
            this.documents.push({
              key: 'minimumSafeManningDocument',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'captainsMaritimeCertificate':
            this.marineCaptainCertificateAttachment = file;
            this.documents.push({
              key: 'captainsMaritimeCertificate',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'insuranceCertificate':
            this.insuranceCertificateAttachment = file;
            this.documents.push({
              key: 'insuranceCertificate',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'copyOfYachtRentalCompanyLicense':
            this.yachtCharterCompanyLicenseAttachment = file;
            this.documents.push({
              key: 'copyOfYachtRentalCompanyLicense',
              file: new File([], file.docName!, undefined),
            });
            break;
          case 'mooringContractLicense':
            this.dockingContractAttachment = file;
            this.documents.push({ key: 'mooringContractLicense', file: new File([], file.docName!, undefined) });
            break;
          case 'approvedRentalContract':
            this.approvedTenancyContractAttachment = file;
            this.documents.push({
              key: 'approvedRentalContract',
              file: new File([], file.docName!, undefined),
            });
            break;
        }
      }
    }

    this.showCrDetails = true;

    this.step = 3;
    this.currentStep = 3;
    this.editMode = true;
    this.showWizard();
  }

  loadRelationshipsOptions(): void {
    this.loadAllMarineMediums();

    // this.companyService
    //   .query()
    //   .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
    //   .pipe(
    //     map((companies: ICompany[]) =>
    //       this.companyService.addCompanyToCollectionIfMissing<ICompany>(
    //         companies,
    //         this.yachtCharterPermit?.marineCompanyOwner,
    //         this.yachtCharterPermit?.marineDelegatorCompany,
    //       ),
    //     ),
    //   )
    //   .subscribe((companies: ICompany[]) => (this.companiesSharedCollection = companies));
    //
    // this.userService
    //   .query()
    //   .pipe(map((res: HttpResponse<IUser[]>) => res.body ?? []))
    //   .pipe(
    //     map((users: IUser[]) =>
    //       this.userService.addUserToCollectionIfMissing<IUser>(
    //         users,
    //         this.yachtCharterPermit?.marineOwner,
    //         this.yachtCharterPermit?.marineDelegatorUser,
    //       ),
    //     ),
    //   )
    //   .subscribe((users: IUser[]) => (this.usersSharedCollection = users));

    this.maritimeMediumService
      .query()
      .pipe(map((res: HttpResponse<IMaritimeMedium[]>) => res.body ?? []))
      .subscribe((maritimeMediums: IMaritimeMedium[]) => (this.maritimeMediums = maritimeMediums));

    this.licenseProviderService
      .query()
      .pipe(map((res: HttpResponse<ILicenseProvider[]>) => res.body ?? []))
      .pipe(
        map((licenseProviders: ILicenseProvider[]) =>
          this.licenseProviderService.addLicenseProviderToCollectionIfMissing<ILicenseProvider>(
            licenseProviders,
            this.yachtCharterPermit?.licenseProvider,
          ),
        ),
      )
      .subscribe((licenseProviders: ILicenseProvider[]) => (this.licenseProvidersSharedCollection = licenseProviders));

    this.investmentTypeService
      .query()
      .pipe(map((res: HttpResponse<IInvestmentType[]>) => res.body ?? []))
      .subscribe((iInvestmentTypes: IInvestmentType[]) => (this.investmentTypes = iInvestmentTypes));

    this.companyService
      .findAllMaritimeTourismAgentCompanies()
      .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
      .subscribe((companies: ICompany[]) => (this.maritimeTourismAgentCompanies = companies));

    this.companyService
      .getRegisteredCompany()
      .pipe(map((res: HttpResponse<ICompany>) => res.body ?? []))
      .subscribe((companyFullInfo: any) => {
        this.registeredCompany = companyFullInfo.company;
        this.selectedAgent = companyFullInfo.company;
        this.onSelectedCR(this.registeredCompany);
        this.companyService
          .getRelatedCompany(this.registeredCompany.crNumber)
          .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
          .subscribe((companies: ICompany[]) => {
            this.relatedCompanies = companies;
            this.subCompany = this.relatedCompanies.find(comp => comp.id === this.selectedSubCompany?.id) || null;
          });
      });
  }

  hasFieldError(fieldName: string, errorType: string): boolean {
    const field = this.editForm.get(fieldName);
    return !!(field && field.errors && field.errors[errorType] && (field.dirty || field.touched));
  }
}
