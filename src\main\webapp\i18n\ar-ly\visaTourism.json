{"srsaApp": {"visaTourism": {"establishmentDetails": "تفاصيل المنشأة", "home": {"title": "التأشيرات السياحية ", "refreshListLabel": "تحديث", "createLabel": "إنشاء تأشيرات سياحية", "createOrEditLabel": "تعديل التأشيرات سياحية", "notFound": "لا يوجد تأشيرات سياحية"}, "created": "تم إنشاء تأشيرات سياحية ل {{ param }}", "updated": "تم تعديل تأشيرات سياحية ل {{ param }}", "deleted": "تم حذف تأشيرات سياحية ل {{ param }}", "delete": {"question": "هل متأكد من حذف التصريح الفني ل {{ id }}?"}, "detail": {"title": "الترخيص الفني (السياحي الترفيهي)"}, "status": {"NEW": "جديد", "DRAFT": "مسودّة", "DELETED": "م<PERSON><PERSON><PERSON><PERSON>", "ACTIVE": "فعال", "EXPIRED": "منتهي", "RETURNED": "معاد", "COMPLETED": "مكتمل", "CANCELLED": "ملغي", "null": "", "PENDING_REVIEW": "بإنتظار المراجعة", "PENDING_REPORT_APPROVAL": "تحت إعتماد التقرير", "PENDING_MANAGEMENT_LICENSING": "تحت الإعتماد", "RETURNED_INSPECTION_OFFICER": "معاد من قبل ضابط الفحص", "UNDER_PROCESS": "تحت المراجعة", "PENDING_INSPECTION": "بإنتظار المعاينة", "UNDER_INSPECTION": "تحت المعاينة", "PENDING_VISIT": "بانتظار الفحص المكتبي", "REJECTED": "مرفو<PERSON>", "VISIT_COMPLETED": "إتمام الزيارة الميدانية", "PENDING_PAYMENT": "بإنتظار الدفع", "RETURNED_LICENSING_OFFICER": "معاد من قبل إخصائي أول تراخيص السياحة الساحلية", "RETURNED_LICENSING_MANAGER": "معاد من قبل مدير إدارة تراخيص السياحة الساحلية"}, "permitPurpose": {"TOURISM": "السياحة", "REFUELING": "التزود بالوقود", "MAINTENANCE": "الصيانة", "STOCK_UP": "المخزون", "PARTICIPATION_IN_MARINE_EXHIBITIONS": "المشاركة في المعارض البحرية"}, "id": "الرقم", "marineOwnerNameAr": "إسم مالك الواسطة", "marineOwnerNameEn": "إسم مالك الواسطة", "marineOwnerIdNumber": "رقم هوية مالك الواسطة", "marineOwnerPassportNumber": "رقم جواز مالك الواسطة", "delegatorNameAr": "إسم الوكيل", "delegatorNameEn": "إسم الوكيل", "delegatorMobileNumber": "رقم جوال الوكيل", "delegatorEmailNumber": "البريد الإلكتروني للوكيل", "permitNumber": "رقم التصريح", "requestStatus": "حالة التصريح", "marineColor": "لون الواسطة", "engineType": "نوع المحرك", "permitActivityLocation": "موقع الأنشطة", "permitActivityLatitude": "Permit Activity Latitude", "permitActivityLogitude": "Permit Activity Logitude", "noOfPassengers": "<PERSON><PERSON><PERSON> الركاب", "noOfCrew": "<PERSON><PERSON><PERSON> الطاقم", "captainName": "إس<PERSON> القبطان", "captainMobileNumber": "رقم جوال القبطان", "captainPassportNumber": "رقم جواز سفر القبطان", "arrivalDate": "تاريخ الوصول", "departureDate": "تاريخ المغادرة", "disclosureChecked": "Disclosure Checked", "passengers": "الركاب", "marineMedium": "الواسطة", "marineCompanyOwner": "الوكيل", "marineOwner": "المالك", "marineDelegatorUser": "الوكيل", "marineDelegatorCompany": "Marine Delegator Company", "allRequests": " قائمة الطلبات", "needActionRequests": "قائمة الطلبات معلقة", "draftRequests": "قائمة الطلبات المسودة", "newRequests": " الطلبات الجديدة", "select": "إختيار", "returnRq": "إعادة الطلب لإستكمال المتطلبات", "approveRq": "قبول", "returnRqConfirm": "هل انت متاكد من إعادة الطلب ؟", "returnRqReasonConfirm": "اسباب إعادة الطلب ", "confirmReturn": " إعادة الطلب ", "activity": "الأنشطه التي ستتم ممارستها", "otherActivity": "الأنشطه الأخرى", "Contract_Attachment": "عقد مبرم مع الوكيل الملاحي السياحي معتمد من الهيئة", "Document_yacht_Attachment": "مُستند يوّضح خط سير اليخت المخطط له ومدة إقامة اليخت ومناطق الإبحار داخل النطاق الجغرافي", "Yacht_Registration_Attachment": "شهادة تسجيل اليخت", "Copy_contract_Attachment": "نسخة من عقد الرصيف", "Crew_Data_Attachment": "قائمة بيانات افراد الطاقم وتاشيراته", "Copy_yacht_Attachment": "نسخة من رخصة اللاسلكي لليخت", "Insurance_Attachment": "وثيقة التامين", "Copy_passports_Attachment": "نسخة من جوازات السفر للطاقم والركاب", "Testimonies_Attachment": "شهادات الربان والطاقم المتواجدين على متن اليخت", "Passenger_Attachment": "قائمة بيانات الركاب", "General_Inspection_Attachment": "تقرير الفحص العام", "Certificate_Compliance_Attachment": "شهادة الامتثال الصادرة من دولة العلم", "Yacht_Owner_Identification_Attachment": "البيانات التعريفية لمالك اليخت", "purpose": "الغرض من الزيارة", "navigationalData": "بيانات الترخيص الملاحي", "Yacht_Registration_Certificate": "شهادة تسجيل اليخت", "Copy_contract_sidewalk": "نسخة من عقد الرصيف", "Crew_Data_List_Visas": "قائمة بيانات افراد الطاقم وتاشيراته", "exceptionApproval": "هل الطلب معفي من الشروط", "feeRequired": "<PERSON><PERSON> يوجد رسوم للطلب", "noValidNavPermit": "عفوا، لا يمكن إتمام العملية لعدم وجود تصريح ملاحي ساري لليخت المحدد .", "noValidTechnicalPermit": "عفوا، لا يمكن إتمام العملية لعدم وجود تصريح فني ساري لليخت المحدد .", "noValidVisaPassengersCount": "عفوا، لا يمكن إتمام العملية لان عدد التأشيرات الفعاله تتجاوز الطاقه الاستيعابيه لليخت .", "visaModal": {"verification": "التحقق", "welcome": "مرحباً بك في طلب التأشيرة", "welcomeMessage": "يرجى التأكد من توفر جميع المستندات المطلوبة قبل المتابعة مع الطلب.", "verificationMessage": "يرجى تقديم معلوماتك الأساسية لبدء عملية طلب التأشيرة.", "requiredDocuments": "المستندات المطلوبة:", "importantNote": "ملاحظة مهمة", "verificationNote": "يرجى التأكد من أن المعلومات تطابق تماماً تفاصيل جواز السفر الخاص بك. سيتم استخدام هذه المعلومات في جميع مراحل عملية الطلب.", "fullName": "الاسم الكامل", "passportNumber": "رقم جواز السفر", "nameRequired": "الاسم الكامل مطلوب", "passportRequired": "رقم جواز السفر مطلوب", "startApplication": "التحقق", "addPersonDetails": "إضافة تفاصيل الشخص", "visaRequestView": "عرض طلب التأشيرة", "personalInformation": "المعلومات الشخصية", "passportInformation": "معلومات جواز السفر", "travelInformation": "معلومات السفر", "healthInformation": "المعلومات الصحية", "emergencyContact": "جهة الاتصال في حالات الطوارئ", "documentsUpload": "رفع المستندات", "reviewSubmit": "مراجعة وإرسال", "fullNameEnglish": "الاسم الكامل (بالإنجليزية)", "fullNameArabic": "الاسم الكامل (بالعربية)", "firstNameArabic": "الاسم الأول (بالعربية)", "fatherNameArabic": "اسم الأب (بالعربية)", "grandfatherNameArabic": "اسم الجد (بالعربية)", "firstNameEnglish": "الاسم الأول (بالإنجليزية)", "fatherNameEnglish": "اسم الأب (بالإنجليزية)", "grandfatherNameEnglish": "اسم الجد (بالإنجليزية)", "documentType": "نوع الوثيقة", "passportPhoto": "صورة جواز السفر", "uploadPassportPhoto": "رفع صورة جواز السفر", "uploadFile": "رفع ملف", "fileUploadError": "خطأ في رفع الملف", "arabicNames": "الأسماء بالعربية", "englishNames": "الأسماء بالإنجليزية", "personalDetails": "التفاصيل الشخصية", "firstNameArabicPlaceholder": "الاسم الأول", "fatherNameArabicPlaceholder": "اسم الأب", "grandfatherNameArabicPlaceholder": "اسم الجد", "firstNameEnglishPlaceholder": "الاسم الأول", "fatherNameEnglishPlaceholder": "اسم الأب", "grandfatherNameEnglishPlaceholder": "اسم الجد", "firstNameArabicRequired": "الاسم الأول بالعربية مطلوب", "fatherNameArabicRequired": "اسم الأب بالعربية مطلوب", "grandfatherNameArabicRequired": "اسم الجد بالعربية مطلوب", "firstNameEnglishRequired": "الاسم الأول بالإنجليزية مطلوب", "fatherNameEnglishRequired": "اسم الأب بالإنجليزية مطلوب", "grandfatherNameEnglishRequired": "اسم الجد بالإنجليزية مطلوب", "passportNumberPlaceholder": "أدخل رقم جواز السفر", "nationality": "الجنسية", "selectNationality": "اختر الجنسية", "nationalityRequired": "الجنسية مطلوبة", "selectDocumentType": "اختر نوع الوثيقة", "documentTypeRequired": "نوع الوثيقة مطلوب", "passport": "جواز سفر", "nationalId": "هوية وطنية", "residencePermit": "إقامة", "selectPassportType": "اختر النوع", "passportTypeRequired": "النوع مطلوب", "ordinary": "عادي", "diplomatic": "دبلوماسي", "service": "خدمة", "official": "رسمي", "birthDateRequired": "تاريخ الميلاد مطلوب", "gender": "الجنس", "selectGender": "اختر الجنس", "genderRequired": "الجنس مطلوب", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى", "passportPhotoRequired": "صورة جواز السفر مطلوبة", "personalDetailsExtended": "التفاصيل الشخصية", "additionalInformation": "معلومات إضافية", "locationInformation": "معلومات المكان", "birthCountry": "<PERSON><PERSON><PERSON> الميلاد", "selectBirthCountry": "ا<PERSON><PERSON>ر بلد الميلاد", "birthCountryRequired": "بلد الميلاد مطلوب", "residenceCountry": "بل<PERSON> الإقامة", "selectResidenceCountry": "اختر بلد الإقامة", "residenceCountryRequired": "بلد الإقامة مطلوب", "birthPlace": "مكان الميلاد", "birthPlacePlaceholder": "أد<PERSON>ل مكان الميلاد", "birthPlaceRequired": "مكان الميلاد مطلوب", "countryCode": "كو<PERSON> الدولة", "countryCodePlaceholder": "أ<PERSON><PERSON><PERSON> كود الدولة", "countryCodeRequired": "كود الدولة مطلوب", "passportDetails": "تفاصيل جواز السفر", "contactInformation": "معلومات الاتصال", "additionalNationality": "الجنسية الإضافية", "hasOtherNationalityQuestion": "هل تملك جنسية أخرى؟", "guardianInformation": "معلومات الوصي", "guardianName": "اسم الوصي", "guardianRelation": "صلة القرابة بالوصي", "guardianPassportNumber": "رقم جواز سفر الوصي", "guardianAge": "ع<PERSON><PERSON> الوصي", "yes": "نعم", "no": "لا", "father": "الأب", "mother": "الأم", "brother": "الأخ", "sister": "الأخت", "uncle": "العم", "aunt": "العمة", "grandfather": "الجد", "grandmother": "الجدة", "birthDate": "تاريخ الميلاد", "maritalStatus": "الحالة الاجتماعية", "passportIssueDate": "تاريخ إصدار جواز السفر", "passportExpiryDate": "تاريخ انتهاء جواز السفر", "passportCountry": "الدولة المصدرة لجواز السفر", "email": "عنوان البريد الإلكتروني", "phoneNumber": "رقم الهاتف", "alternatePhone": "رقم هاتف بديل", "city": "المدينة", "country": "الدولة", "postalCode": "الر<PERSON>ز البريدي", "expectedExitDate": "تاريخ الخروج المتوقع", "purposeOfVisit": "الغرض من الزيارة", "hasOtherNationality": "هل تحمل جنسية أخرى؟", "otherNationality": "الجنسية الأخرى", "previousVisaToSaudi": "هل سبق لك زيارة المملكة العربية السعودية؟", "previousVisaDetails": "تفاصيل التأشيرة السابقة", "tookVaccines": "هل تلقيت التطعيمات المطلوبة؟", "vaccineDetails": "تفاصيل التطعيمات", "emergencyContactName": "اسم جهة الاتصال في حالات الطوارئ", "emergencyContactPhone": "هاتف جهة الاتصال في حالات الطوارئ", "emergencyContactRelation": "صلة القرابة", "emergencyContactAddress": "عنوان جهة الاتصال في حالات الطوارئ", "documentsUploaded": "المستندات المرفوعة", "cancel": "إلغاء", "previous": "السابق", "next": "التالي", "submit": "إرسال", "submitting": "جاري الإرسال...", "selectMaritalStatus": "اختر الحالة الاجتماعية", "family": "زيارة عائلية", "medical": "طبي", "education": "تعليم", "other": "أ<PERSON><PERSON><PERSON>", "passportType": "نوع جواز السفر", "additionalDetails": "تفاصيل إضافية", "personalStatus": "الحالة الشخصية", "maritalStatusRequired": "الحالة الاجتماعية مطلوبة", "religion": "الديانة", "selectReligion": "اختر الديانة", "religionRequired": "الديانة مطلوبة", "islam": "الإسلام", "christianity": "المسيحية", "judaism": "اليهودية", "hinduism": "الهندوسية", "buddhism": "البوذية", "addressInformation": "معلومات العنوان", "address": "العنوان", "addressPlaceholder": "أد<PERSON>ل عنوانك الكامل", "addressRequired": "العنوان مطلوب", "workInformation": "معلومات العمل", "occupation": "طبيعة العمل", "selectOccupation": "اختر المهنة", "occupationRequired": "طبيعة العمل مطلوبة", "engineer": "مهندس", "doctor": "طبيب", "teacher": "معلم", "businessman": "رجل أعمال", "student": "طالب", "retired": "متقا<PERSON>د", "unemployed": "عاطل عن العمل", "visaInformation": "معلومات التأشيرة", "visaType": "نوع التأشيرة", "selectVisaType": "اختر نوع التأشيرة", "visaTypeRequired": "نوع التأشيرة مطلوب", "selectVisaDuration": "اختر مدة التأشيرة", "visaDurationRequired": "مدة التأشيرة مطلوبة", "visaValidity": "صلاحية التأشيرة", "selectVisaValidity": "اختر صلاحية التأشيرة", "visaValidityRequired": "صلاحية التأشيرة مطلوبة", "expectedEntryDate": "تاريخ الدخول المتوقع", "expectedEntryDateRequired": "تاريخ الدخول المتوقع مطلوب", "personalPhoto": "صورة شخصية", "uploadPersonalPhoto": "رفع صورة شخصية", "personalPhotoRequired": "الصورة الشخصية مطلوبة", "travelHistory": "تاريخ السفر", "hasPreviousTravelQuestion": "هل يوجد سفرات سابقة للشخص؟", "previousTravelRequired": "يرجى اختيار ما إذا كان لديك تاريخ سفر سابق", "travelDetails": "تفاصيل السفر", "previousCountries": "دول السفر السابقة", "selectPreviousCountries": "اختر الدول السابقة", "previousCountriesRequired": "دول السفر السابقة مطلوبة", "travelPurpose": "الغرض", "selectTravelPurpose": "اخت<PERSON> الغرض", "travelPurposeRequired": "الغرض من السفر مطلوب", "work": "<PERSON><PERSON><PERSON>", "religious": "ديني", "travelFromDate": "من تاريخ", "travelFromDateRequired": "تاريخ البداية مطلوب", "travelToDate": "إلى تاريخ", "travelToDateRequired": "تاريخ النهاية مطلوب", "healthInsuranceQuestions": "الأسئلة الصحية والتأمينية", "disabilityInformation": "معلومات الإعاقة", "hasDisabilityFromAccident": "هل تعرضت لحادث تسبب لك بإعاقة؟", "medicalTreatment": "العلاج الطبي", "currentlyHospitalized": "هل يوجد حالياً حالة دخول للشخص للمستشفى أو يتلقى علاج طارئ؟", "congenitalConditions": "الحالات الخلقية", "hasCongenitalCondition": "هل تعاني من حالات ضعف أو تشوه خلقي؟", "pregnancyInformation": "معلومات الحمل", "currentlyPregnant": "ه<PERSON> يوجد حمل حالي؟", "pregnancyFromAssistance": "هل الحمل الحالي نتيجة الوسائل المساعدة للحمل؟", "pregnancyMonth": "بأي شهر من الحمل؟", "selectPregnancyMonth": "اخت<PERSON> الشهر", "month1": "الشهر الأول", "month2": "الشهر الثاني", "month3": "الشهر الثالث", "month4": "الشهر الرابع", "month5": "الشهر الخامس", "month6": "الشهر السادس", "month7": "الشهر السابع", "month8": "الشهر الثامن", "month9": "الشهر التاسع", "legalQuestions": "الأسئلة القانونية", "hasInterpolWarrant": "هل سبق وأن صدر في حق الشخص مذكرة توقيف الشخص من الشرطة الدولية (الانتربول)؟", "hasBeenDeported": "هل سبق وأن تم ترحيل أو إبعاد الشخص من أي بلد بما في ذلك مخالفة نظام الإقامة؟", "hasPassportRestrictions": "هل جواز سفر الشخص يحتوي على قيد أو شرط أو ملاحظات صالحة لسفرة واحدة فقط؟", "financialQuestions": "الأسئلة القانونية والمالية", "hasFinancialArrest": "هل تم القبض على الشخص بسبب قضايا مالية؟", "clarifications": "توضيحات", "clarificationsPlaceholder": "يرجى تقديم التوضيحات", "hasDisability": "هل لدى الشخص أي إعاقة؟", "commitToProvideVaccinations": "أتعهد بتقديم أصل التطعيمات عند الحاجة؟", "numberOfEntries": "عدد مرات الدخول", "selectNumberOfEntries": "اختر عدد مرات الدخول", "numberOfEntriesRequired": "عدد مرات الدخول مطلوب", "visaDurationMinError": "المدة يجب أن تكون يوم واحد على الأقل", "personList": {"title": "الأشخاص المضافون", "emptyState": "لم يتم إضافة أشخاص بعد", "emptyStateDescription": "انقر على 'إضافة شخص' لإضافة مسافرين إلى طلب التأشيرة هذا", "mobileNumber": "رقم الجوال", "passportNumber": "رقم جواز السفر", "name": "الاسم", "nationality": "الجنسية", "gender": "الجنس", "totalPersons": "إجمالي الأشخاص:"}, "employmentInformation": "معلومات العمل", "employmentStatus": "حالة العمل", "currentEmploymentStatus": "حالة العمل الحالية", "selectEmploymentStatus": "اختر حالة العمل", "employmentStatusRequired": "حالة العمل مطلوبة", "employed": "موظف", "selfEmployed": "يعمل لحسابه الخاص", "housewife": "ربة منزل", "jobTitle": "المسمى الوظيفي", "jobTitlePlaceholder": "أد<PERSON>ل المسمى الوظيفي", "jobTitleRequired": "المسمى الوظيفي مطلوب", "companyInformation": "معلومات الشركة", "employerName": "اسم صاحب العمل/الشركة", "employerNamePlaceholder": "أد<PERSON>ل اسم صاحب العمل/الشركة", "employerNameRequired": "اسم صاحب العمل مطلوب", "workExperience": "سنوات الخبرة في العمل", "selectWorkExperience": "اختر الخبرة", "workExperienceRequired": "سنوات الخبرة مطلوبة", "experience0to1": "0-1 سنة", "experience2to5": "2-5 سنوات", "experience6to10": "6-10 سنوات", "experience11to15": "11-15 سنة", "experience16to20": "16-20 سنة", "experience20plus": "20+ سنة", "employerAddress": "عنوان صاحب العمل", "employerAddressPlaceholder": "أدخل عنوان صاحب العمل الكامل", "employerAddressRequired": "عنوان صاحب العمل مطلوب", "financialInformation": "المعلومات المالية", "monthlyIncome": "الدخل الشهري", "selectMonthlyIncome": "اختر الدخل الشهري", "monthlyIncomeRequired": "الدخل الشهري مطلوب", "incomeBelow1000": "أقل من 1000 دولار", "income1000to3000": "1000 - 3000 دولار", "income3000to5000": "3000 - 5000 دولار", "income5000to10000": "5000 - 10000 دولار", "incomeAbove10000": "أكثر من 10000 دولار", "employmentContract": "<PERSON><PERSON><PERSON> العمل", "employmentContractNote": "ر<PERSON>ع عقد العمل أو شهادة الراتب (اختياري)", "arabicOnlyError": "هذا الحقل يجب أن يحتوي على أحرف عربية فقط", "englishOnlyError": "هذا الحقل يجب أن يحتوي على أحرف إنجليزية فقط", "arabicOrEnglishError": "هذا الحقل يجب أن يحتوي على أحرف عربية أو إنجليزية فقط", "close": "إغلاق", "visaDuration": "مدة التأشيرة (أيام)", "visaDurationPlaceholder": "أدخل مدة التأشيرة بالأيام", "visaValidityPlaceholder": "أدخل صلاحية التأشيرة", "crewType": "نوع الطاقم", "crew": "الطاقم", "visitor": "زائر", "selectCrewType": "اختر نوع الطاقم", "wantsUmrah": "يريد العمرة ؟"}}}}