<div class="logo-Ebhar-sub">
  <!-- <app-header /> -->
  <div class="mk-permit-container mk-inner-layout-container mk-create-request">
    <div class="">
      <div class="mk-inner-layout-content">
        <form name="editForm" role="form" novalidate (ngSubmit)="save()" [formGroup]="editForm">
          <div>
            <jhi-alert-error></jhi-alert-error>
            <small *ngIf="fileSizeError" class="form-text text-danger" jhiTranslate="error.fileSizeError"> file Size error</small>
            <div *ngIf="step == 1 && !wizardDisplay">
              <div class="row">
                <div class="d-flex justify-content-end">
                  <div class="card custom">
                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/user.svg" alt="flowbite_user" />
                      <div class="d-grid ms-3">
                        <span class="item-label" jhiTranslate="srsaApp.permitRequest.targetAudience">الفئة المستهدفة</span>
                        <span class="item-value" jhiTranslate="srsaApp.permitRequest.establishments">منشآت</span>
                      </div>
                    </div>

                    <hr class="mx-4" />
                    <!--                    <div class="d-flex">-->
                    <!--                      <div class="d-grid ms-3">-->
                    <!--                        <span class="item-label" jhiTranslate="global.form.frequentlyAskedQuestions"> الاسئلة الشائعة</span>-->
                    <!--                        <a-->
                    <!--                          href="Ministry-FAQ's-page-->
                    <!--                        "-->
                    <!--                          ><span class="item-value"-->
                    <!--                            >Ministry-FAQ's-page-->
                    <!--                            <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" /> </span-->
                    <!--                        ></a>-->
                    <!--                      </div>-->
                    <!--                    </div>-->
                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/call.svg" alt="call" />
                      <div class="d-grid ms-3">
                        <span class="item-label" jhiTranslate="global.form.mobileNumber.label">رقم الهاتف</span>
                        <span class="item-value">
                          930 <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" />
                        </span>
                      </div>
                    </div>

                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/mail.svg" alt="mail" />
                      <div class="d-grid ms-3">
                        <span class="item-label" jhiTranslate="global.form.email.label">البريد الإلكتروني</span>
                        <a href="mailto:<EMAIL>"
                          ><span class="item-value"
                            >{{ '<EMAIL>' }}
                            <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" /> </span
                        ></a>
                      </div>
                    </div>
                    <!--                    <div class="d-flex">-->
                    <!--                      <button class="btn btn-link" jhiTranslate="global.form.downloadUserGuide">تحميل دليل المستخدم</button>-->
                    <!--                    </div>-->
                  </div>
                </div>

                <div class="custom-card">
                  <div class="col-lg-8 d-flex-between gap-3 mb-4">
                    <div class="d-flex flex-column justify-content-start my-3 gap-3">
                      <app-page-title [title]="'srsaApp.arrivalRequest.detail.title' | translate" />

                      <div class="d-grid">
                        <!-- <span class="linces-header" jhiTranslate="srsaApp.permitRequest.detail.title">رخصة</span> -->
                        <span class="linces-header-2" jhiTranslate="srsaApp.arrivalRequest.detail.title">{{
                          isArabic() ? selectedServiceType?.nameAr : selectedServiceType?.nameEn
                        }}</span>
                      </div>
                    </div>
                    <div class="w-sm-100">
                      <!-- Button trigger modal -->
                      <button
                        type="button"
                        class="btn btn-primary px-4 px-xl-5 w-sm-100"
                        data-bs-toggle="modal"
                        data-bs-target="#marineSelectModal"
                        jhiTranslate="srsaApp.permitRequest.detail.startService"
                      >
                        بدء الخدمة
                      </button>
                    </div>
                  </div>
                  <!-- <p class="">
                      {{ isArabic() ? selectedServiceType!.descAr : selectedServiceType!.descEn }}
                    </p> -->
                </div>

                <div class="dga-tabs">
                  <p-tabView>
                    <p-tabPanel [header]="'srsaApp.permitRequest.detail.requiredDocuments' | translate">
                      <div class="tab-pane fade animate fadeInRight">
                        <ol class="mb-4 d-flex flex-column gap-4">
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.arrivalRequest.detail.mtaValidation">
                              إذا كان مقدم الطلب هو MTA، يتحقق النظام من أن MTA لديه تفويض نشط لهذه الخدمة على اليخت المحدد
                            </span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.arrivalRequest.detail.lastArrivalNotice">
                              حالة آخر إشعار وصول لليخت هي مغادرة (إذا كانت متاحة)
                            </span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.arrivalRequest.detail.validLicense">
                              يجب أن يكون لدى اليخت رخصة ملاحية صالحة
                            </span>
                          </li>
                        </ol>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="'srsaApp.permitRequest.detail.serviceTerms' | translate">
                      <div class="tab-pane fade animate fadeInRight">
                        <ol class="mb-4 d-flex flex-column gap-4">
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.detail.serviceTerm1"
                              >ان يكون السجل التجاري نشط</span
                            >
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.detail.serviceTerm3"
                              >الموافقة على الشروط والأحكام</span
                            >
                          </li>
                        </ol>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="'srsaApp.permitRequest.detail.steps' | translate">
                      <div class="tab-pane fade animate fadeInRight">
                        <ol class="mb-4 d-flex flex-column gap-4">
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.arrivalRequest.majorData"> بيانات رئيسية</span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.extraData">بيانات إضافية</span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" />
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.attachments">المرفقات</span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.preview">المعاينة</span>
                          </li>
                        </ol>
                      </div>
                    </p-tabPanel>
                  </p-tabView>
                </div>
              </div>
            </div>

            <!-- wizard steps starts from here -->
            <section *ngIf="wizardDisplay">
              <div class="row p-lg-4">
                <app-page-title [title]="'srsaApp.arrivalRequest.detail.title' | translate" />
              </div>
              <div class="row">
                <div class="d-flex p-lg-4 justify-content-between">
                  <div class="">
                    <span class="linces-header" jhiTranslate="srsaApp.arrivalRequest.home.title">رخصة</span>
                  </div>
                  <button type="button" [routerLink]="['/arrival-notifications']" class="btn btn-outline-danger-secondary">
                    <svg-icon src="content/images/svg-icons/dga/cancel.svg"></svg-icon>
                    <span jhiTranslate="srsaApp.permitRequest.cancelRequest"></span>
                  </button>
                </div>
              </div>
              <div class="row mb-4">
                <jhi-wizard-step [steps]="steps" [currentStep]="currentStep"></jhi-wizard-step>
              </div>

              <div class="card" *ngIf="currentStep === 1">
                <p data-cy="PermitRequestCreateUpdateHeading" class="headFont" jhiTranslate="srsaApp.arrivalRequest.majorData">
                  بيانات رئيسية
                </p>

                <div class="row g-4 mb-4">
                  <div class="mb-3 col-6">
                    <div class="text-secondary">
                      <span jhiTranslate="srsaApp.arrivalRequest.marineMediumNameAr"></span>
                    </div>
                    <div class="fw-bold text-primary">
                      <span>{{ selectedMarineMedium?.nameAr }}</span>
                    </div>
                  </div>
                  <div class="mb-3 col-6">
                    <div class="text-secondary">
                      <span jhiTranslate="srsaApp.arrivalRequest.marineMediumNameEn"></span>
                    </div>
                    <div class="fw-bold text-primary">
                      <span>{{ selectedMarineMedium?.nameEn }}</span>
                    </div>
                  </div>
                  <div class="mb-3 col-6">
                    <label class="form-label required" for="field_imoNumber" jhiTranslate="srsaApp.marineMedium.imoNumber"
                      >imo Number
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      name="imoNumber"
                      id="field_imoNumber"
                      data-cy="imoNumber"
                      formControlName="imoNumber"
                    />
                    @if (
                      editForm.get('imoNumber')?.touched &&
                      editForm.get('imoNumber')?.invalid &&
                      editForm.get('imoNumber')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (
                      editForm.get('imoNumber')?.touched &&
                      editForm.get('imoNumber')?.invalid &&
                      editForm.get('imoNumber')?.errors?.maxlength
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: 7 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                    @if (
                      editForm.get('imoNumber')?.touched &&
                      editForm.get('imoNumber')?.invalid &&
                      editForm.get('imoNumber')?.errors?.minlength
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 7 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }

                    @if (
                      editForm.get('imoNumber')?.touched && editForm.get('imoNumber')?.invalid && editForm.get('imoNumber')?.errors?.pattern
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.number"
                        >This field can only contain letters, digits and e-mail addresses.</small
                      >
                    }
                  </div>
                  <div class="mb-3 col-6">
                    <label
                      class="form-label required"
                      for="field_yachtManufactureYear"
                      jhiTranslate="srsaApp.arrivalRequest.yachtManufactureYear"
                      >Yacht Manufacture Date</label
                    >

                    <input
                      type="number"
                      class="form-control"
                      name="yachtManufactureYear"
                      id="field_yachtManufactureYear"
                      formControlName="yachtManufactureYear"
                      data-cy="yachtManufactureYear"
                      [min]="1900"
                      [max]="2024"
                    />
                    @if (
                      editForm.get('yachtManufactureYear')?.touched &&
                      editForm.get('yachtManufactureYear')?.invalid &&
                      editForm.get('yachtManufactureYear')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (
                      editForm.get('yachtManufactureYear')?.touched &&
                      editForm.get('yachtManufactureYear')?.invalid &&
                      editForm.get('yachtManufactureYear')?.errors?.maxlength
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: 4 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                    @if (
                      editForm.get('yachtManufactureYear')?.touched &&
                      editForm.get('yachtManufactureYear')?.invalid &&
                      editForm.get('yachtManufactureYear')?.errors?.max
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.max" [translateValues]="{ max: 2024 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                    @if (
                      editForm.get('yachtManufactureYear')?.touched &&
                      editForm.get('yachtManufactureYear')?.invalid &&
                      editForm.get('yachtManufactureYear')?.errors?.min
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minNumber" [translateValues]="{ min: 1900 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                    @if (
                      editForm.get('yachtManufactureYear')?.touched &&
                      editForm.get('yachtManufactureYear')?.invalid &&
                      editForm.get('yachtManufactureYear')?.errors?.minlength
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ min: 4 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }

                    @if (
                      editForm.get('yachtManufactureYear')?.touched &&
                      editForm.get('yachtManufactureYear')?.invalid &&
                      editForm.get('yachtManufactureYear')?.errors?.pattern
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.number"
                        >This field can only contain letters, digits and e-mail addresses.</small
                      >
                    }
                  </div>
                  <div class="mb-3 col-6">
                    <label class="form-label required" for="field_captainName" jhiTranslate="srsaApp.arrivalRequest.captainName"
                      >Captain Name</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="captainName"
                      id="field_captainName"
                      data-cy="captainName"
                      formControlName="captainName"
                    />
                    @if (
                      editForm.get('captainName')?.touched &&
                      editForm.get('captainName')?.invalid &&
                      editForm.get('captainName')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                  </div>
                  <div class="col-6 mb-3">
                    <label class="form-label required" for="field_nationality" jhiTranslate="srsaApp.arrivalRequest.nationality"
                      >Nationality</label
                    >
                    <select
                      class="form-control"
                      id="field_nationality"
                      data-cy="nationality"
                      name="nationality"
                      formControlName="nationality"
                      [compareWith]="compareArrivalRequestNationality"
                    >
                      <option [ngValue]="null"></option>
                      @for (arrivalRequestNationalityOption of arrivalRequestNationalitiesSharedCollection; track $index) {
                        <option [ngValue]="arrivalRequestNationalityOption">
                          {{ isArabic() ? arrivalRequestNationalityOption.nameAr : arrivalRequestNationalityOption.nameEn }}
                        </option>
                      }
                    </select>
                    @if (
                      editForm.get('nationality')?.touched &&
                      editForm.get('nationality')?.invalid &&
                      editForm.get('nationality')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                  </div>
                  <div class="mb-3 col-6">
                    <label
                      class="form-label required"
                      for="field_yachtLengthOverAll"
                      jhiTranslate="srsaApp.arrivalRequest.yachtLengthOverAll"
                      >Yacht Length Over All</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="yachtLengthOverAll"
                      id="field_yachtLengthOverAll"
                      data-cy="yachtLengthOverAll"
                      formControlName="yachtLengthOverAll"
                    />
                    @if (
                      editForm.get('yachtLengthOverAll')?.touched &&
                      editForm.get('yachtLengthOverAll')?.invalid &&
                      editForm.get('yachtLengthOverAll')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (
                      editForm.get('yachtLengthOverAll')?.touched &&
                      editForm.get('yachtLengthOverAll')?.invalid &&
                      editForm.get('yachtLengthOverAll')?.errors?.min
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minNumber" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>
                  <!--                  <div class="mb-3 col-6">-->
                  <!--                    <label class="form-label required" for="field_arrivalReason" jhiTranslate="srsaApp.arrivalRequest.arrivalReason"-->
                  <!--                      >Arrival Reason</label-->
                  <!--                    >-->
                  <!--                    <select-->
                  <!--                      class="form-control"-->
                  <!--                      id="field_arrivalReason"-->
                  <!--                      data-cy="arrivalReason"-->
                  <!--                      name="arrivalReason"-->
                  <!--                      formControlName="arrivalReason"-->
                  <!--                    >-->
                  <!--                      <option [ngValue]="null"></option>-->
                  <!--                      @for (arrivalReason of arrivalReasons; track $index) {-->
                  <!--                        <option [ngValue]="arrivalReason">-->
                  <!--                          {{ isArabic() ? arrivalReason.nameAr : arrivalReason.nameEn }}-->
                  <!--                        </option>-->
                  <!--                      }-->
                  <!--                    </select>-->
                  <!--                    @if (-->
                  <!--                      editForm.get('arrivalReason')?.touched &&-->
                  <!--                      editForm.get('arrivalReason')?.invalid &&-->
                  <!--                      editForm.get('arrivalReason')?.errors?.required-->
                  <!--                    ) {-->
                  <!--                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>-->
                  <!--                    }-->
                  <!--                  </div>-->
                  <div class="mb-3 col-6 d-flex gap-2">
                    <div class="">
                      <label class="form-label required" for="field_estimatedArrival" jhiTranslate="srsaApp.arrivalRequest.estimatedArrival"
                        >Estimated Arrival</label
                      >
                      <gregorian-calendar
                        name="estimatedArrival"
                        id="field_estimatedArrival"
                        formControlName="estimatedArrival"
                        (gregCalendar)="setEstimatedArrival($event)"
                        [minDateAsToday]="true"
                        [maxDateAsToday]="false"
                        [maxDate]="getDateNeededDaysFromNow(5)"
                      >
                      </gregorian-calendar>
                    </div>

                    <div class="d-flex align-items-end">
                      <div class="d-flex align-items-center">
                        <select
                          class="form-control"
                          id="estimatedArrivalMinute"
                          name="estimatedArrivalMinute"
                          [(ngModel)]="estimatedArrivalMinute"
                          (ngModelChange)="onArrivalTimeChange()"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <option *ngFor="let minute of minutes" [value]="minute">{{ minute }}</option>
                        </select>
                        <span>:</span>
                        <select
                          class="form-control"
                          id="estimatedArrivalHour"
                          name="estimatedArrivalHour"
                          [(ngModel)]="estimatedArrivalHour"
                          (ngModelChange)="onArrivalTimeChange()"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <option *ngFor="let hour of hours" [value]="hour">{{ hour }}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="mb-3 d-flex gap-2 col-6">
                    <div class="">
                      <label
                        class="form-label required"
                        for="field_estimatedDeparture"
                        jhiTranslate="srsaApp.arrivalRequest.estimatedDeparture"
                        >Estimated Departure</label
                      >
                      <gregorian-calendar
                        name="estimatedDeparture"
                        id="field_estimatedDeparture"
                        formControlName="estimatedDeparture"
                        (gregCalendar)="setEstimatedDeparture($event)"
                        [maxDateAsToday]="false"
                        [minDate]="getEstimatedArrival()"
                      >
                      </gregorian-calendar>
                    </div>

                    <div class="d-flex align-items-end">
                      <div class="d-flex align-items-center">
                        <select
                          class="form-control"
                          id="estimatedDepartureMinute"
                          name="estimatedDepartureMinute"
                          [(ngModel)]="estimatedDepartureMinute"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <option *ngFor="let minute of filteredDepartureMinutes" [value]="minute">{{ minute }}</option>
                        </select>
                        <span>:</span>
                        <select
                          class="form-control"
                          id="estimatedDepartureHour"
                          name="estimatedDepartureHour"
                          [(ngModel)]="estimatedDepartureHour"
                          (ngModelChange)="onArrivalTimeChange()"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <option *ngFor="let hour of filteredDepartureHours" [value]="hour">{{ hour }}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <p data-cy="PermitRequestCreateUpdateHeading" class="headFont" jhiTranslate="srsaApp.arrivalRequest.routes"></p>

                <div class="table-responsive table-entities">
                  <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                    <thead>
                      <tr>
                        <th scope="col" class="required" jhiTranslate="srsaApp.arrivalRequest.portType">Port Type</th>
                        <th scope="col" class="required" jhiTranslate="srsaApp.arrivalRequest.portCountry">Port Country</th>
                        <th scope="col" class="required" jhiTranslate="srsaApp.arrivalRequest.portCode">Port Code</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let route of routes; index as i; trackBy: trackRoute">
                        <td>
                          <span>{{ 'srsaApp.arrivalRequest.port.' + route?.portType | translate }}</span>
                        </td>
                        <td>
                          <!-- Static nationality for Entry/Exit with FIRST_NEXT_PORT -->
                          @if (
                            arrivalRequest?.requestType === NotificationType.ENTRY &&
                            (route.portType === 'FIRST_NEXT_PORT' || route.portType === 'CURRENT_PORT')
                          ) {
                            <span class="form-control-plaintext">
                              <!--                              // This the id for Saudi from arrival_request_nationality table-->
                              {{ getNationalityName(route) }}
                            </span>
                          } @else if (arrivalRequest?.requestType === NotificationType.EXIT && route.portType === 'LAST_PREVIOUS_PORT') {
                            <span class="form-control-plaintext">
                              {{ getNationalityName(route) }}
                            </span>
                          } @else {
                            <select
                              class="form-control"
                              data-cy="portNationality"
                              [id]="'portNationality-' + i"
                              [name]="'portNationality-' + i"
                              [(ngModel)]="route.nationalityId"
                              [ngModelOptions]="{ standalone: true }"
                              (change)="selectPortCountries(route)"
                            >
                              <option [ngValue]="null"></option>
                              @for (arrivalRequestNationalityOption of arrivalRequestNationalitiesSharedCollection; track $index) {
                                <option [ngValue]="arrivalRequestNationalityOption.id">
                                  {{ isArabic() ? arrivalRequestNationalityOption.nameAr : arrivalRequestNationalityOption.nameEn }}
                                </option>
                              }
                            </select>
                          }
                        </td>
                        <td>
                          <!-- Static port values for Entry/Exit with FIRST_NEXT_PORT -->
                          <!--                          @if (-->
                          <!--                            (arrivalRequest?.requestType === NotificationType.ENTRY ||-->
                          <!--                              arrivalRequest?.requestType === NotificationType.EXIT) &&-->
                          <!--                            route.portType === 'FIRST_NEXT_PORT'-->
                          <!--                          ) {-->
                          <!--                            <span class="form-control-plaintext">-->
                          <!--                              {{ getPortCountryName(route.portCountry) }}-->
                          <!--                            </span>-->
                          <!--                          } @else {-->
                          <select
                            class="form-control"
                            [id]="'portCountry-' + i"
                            [name]="'portCountry-' + i"
                            data-cy="portCountry"
                            [(ngModel)]="route.portCountry"
                            [ngModelOptions]="{ standalone: true }"
                          >
                            <option [ngValue]="null"></option>
                            @if (route.portType === 'LAST_PREVIOUS_PORT') {
                              <option *ngFor="let portCountry of countriesForLastPort; trackBy: trackPortCountry" [ngValue]="portCountry">
                                {{ (isArabic() ? portCountry.nameAr : portCountry.nameEn) + ' - ' + portCountry.code }}
                              </option>
                            } @else if (route.portType === 'FIRST_NEXT_PORT') {
                              <option *ngFor="let portCountry of countriesForNextPort; trackBy: trackPortCountry" [ngValue]="portCountry">
                                {{ (isArabic() ? portCountry.nameAr : portCountry.nameEn) + ' - ' + portCountry.code }}
                              </option>
                            } @else if (route.portType === 'CURRENT_PORT') {
                              <option
                                *ngFor="let portCountry of countriesForCurrentPort; trackBy: trackPortCountry"
                                [ngValue]="portCountry"
                              >
                                {{ (isArabic() ? portCountry.nameAr : portCountry.nameEn) + ' - ' + portCountry.code }}
                              </option>
                            }
                          </select>
                          <!--                          }-->
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <p data-cy="PermitRequestCreateUpdateHeading" class="headFont" jhiTranslate="srsaApp.arrivalRequest.passengers"></p>

                <div class="mb-4">
                  <button
                    class="btn btn-primary"
                    type="button"
                    (click)="addPassengers()"
                    jhiTranslate="srsaApp.arrivalRequest.addPassengers"
                  >
                    <i class="fa fa-plus"></i>
                    Add Passengers
                  </button>
                  <br />
                  <br />

                  <div class="table-responsive">
                    <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                      <thead>
                        <tr>
                          <th scope="col" class="required" jhiTranslate="srsaApp.arrivalRequest.numberOfPassengers">
                            number of passengers
                          </th>
                          <th scope="col" class="required" jhiTranslate="srsaApp.arrivalRequest.nationality">nationality</th>
                          <th scope="col" class=""></th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let passenger of passengers; index as i">
                          <td>
                            <input
                              max="9999999"
                              min="1"
                              required
                              type="number"
                              class="form-control"
                              name="noOfPassengers"
                              id="noOfPassengers"
                              data-cy="noOfPassengers"
                              [(ngModel)]="passenger.noOfPassengers"
                              [ngModelOptions]="{ standalone: true }"
                              #noOfPassengers="ngModel"
                            />
                            <div *ngIf="noOfPassengers!.invalid && (noOfPassengers!.dirty || noOfPassengers!.touched)">
                              <small
                                class="form-text text-danger"
                                *ngIf="noOfPassengers?.errors?.required"
                                jhiTranslate="entity.validation.required"
                              >
                                هذا الحقل مطلوب
                              </small>
                              <small class="form-text text-danger" *ngIf="noOfPassengers?.errors?.max">
                                {{ 'entity.validation.max' | translate: { max: 9999999 } }}
                              </small>
                              <small class="form-text text-danger" *ngIf="noOfPassengers?.errors?.min">
                                {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                              </small>
                            </div>
                          </td>
                          <td>
                            <select
                              class="form-control"
                              [id]="'passengerNationality-' + i"
                              [name]="'passengerNationality-' + i"
                              data-cy="'passengerNationality-' + i"
                              [(ngModel)]="passenger.nationality"
                              [ngModelOptions]="{ standalone: true }"
                              #passengerNationality="ngModel"
                            >
                              <option [ngValue]="null"></option>
                              @for (arrivalRequestNationalityOption of arrivalRequestNationalitiesSharedCollection; track $index) {
                                <option [ngValue]="arrivalRequestNationalityOption">
                                  {{ isArabic() ? arrivalRequestNationalityOption.nameAr : arrivalRequestNationalityOption.nameEn }}
                                </option>
                              }
                            </select>
                          </td>
                          <td>
                            <button
                              class="btn btn-danger"
                              jhiTranslate="srsaApp.arrivalRequest.removePassengers"
                              (click)="removePassengers(passenger)"
                            ></button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <br />
                  <br />
                </div>
              </div>

              <div class="card" *ngIf="currentStep === 2">
                <p data-cy="PermitRequestCreateUpdateHeading" class="headFont" jhiTranslate="srsaApp.permitRequest.extraData">
                  بيانات إضافية
                </p>

                <div class="row g-4 mb-4">
                  <!--                  <div class="col-4 mb-3">-->
                  <!--                    <label class="form-label" for="field_cargoType" jhiTranslate="srsaApp.arrivalRequest.cargoType">Cargo Type</label>-->
                  <!--                    <select-->
                  <!--                      class="form-control"-->
                  <!--                      id="field_cargoType"-->
                  <!--                      data-cy="cargoType"-->
                  <!--                      name="cargoType"-->
                  <!--                      formControlName="cargoType"-->
                  <!--                      [compareWith]="compareCargoType"-->
                  <!--                    >-->
                  <!--                      <option [ngValue]="null"></option>-->
                  <!--                      @for (cargoTypeOption of cargoTypesSharedCollection; track $index) {-->
                  <!--                        <option [ngValue]="cargoTypeOption">{{ isArabic() ? cargoTypeOption.nameAr : cargoTypeOption.nameEn }}</option>-->
                  <!--                      }-->
                  <!--                    </select>-->
                  <!--                  </div>-->

                  <div class="mb-3 col-4">
                    <label class="form-label" for="field_netRegisterTonnage" jhiTranslate="srsaApp.arrivalRequest.netRegisterTonnage"
                      >Net Register Tonnage</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="netRegisterTonnage"
                      id="field_netRegisterTonnage"
                      data-cy="netRegisterTonnage"
                      formControlName="netRegisterTonnage"
                    />
                    @if (
                      editForm.get('netRegisterTonnage')?.touched &&
                      editForm.get('netRegisterTonnage')?.invalid &&
                      editForm.get('netRegisterTonnage')?.errors?.min
                    ) {
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-label required" for="field_callSign" jhiTranslate="srsaApp.arrivalRequest.callSign">Call Sign</label>
                    <input
                      type="text"
                      class="form-control"
                      name="callSign"
                      id="field_callSign"
                      data-cy="callSign"
                      formControlName="callSign"
                    />
                    @if (
                      editForm.get('callSign')?.touched && editForm.get('callSign')?.invalid && editForm.get('callSign')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-label required" for="field_arrivalDraft" jhiTranslate="srsaApp.arrivalRequest.arrivalDraft"
                      >Arrival Draft</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="arrivalDraft"
                      id="field_arrivalDraft"
                      data-cy="arrivalDraft"
                      formControlName="arrivalDraft"
                    />
                    @if (
                      editForm.get('arrivalDraft')?.touched &&
                      editForm.get('arrivalDraft')?.invalid &&
                      editForm.get('arrivalDraft')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (
                      editForm.get('arrivalDraft')?.touched &&
                      editForm.get('arrivalDraft')?.invalid &&
                      editForm.get('arrivalDraft')?.errors?.min
                    ) {
                      <br />
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-label required" for="field_maxDraft" jhiTranslate="srsaApp.arrivalRequest.maxDraft">Max Draft</label>
                    <input
                      type="number"
                      class="form-control"
                      name="maxDraft"
                      id="field_maxDraft"
                      data-cy="maxDraft"
                      formControlName="maxDraft"
                    />
                    @if (
                      editForm.get('maxDraft')?.touched && editForm.get('maxDraft')?.invalid && editForm.get('maxDraft')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (editForm.get('maxDraft')?.touched && editForm.get('maxDraft')?.invalid && editForm.get('maxDraft')?.errors?.min) {
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-label" for="field_grossRegisterTonnage" jhiTranslate="srsaApp.arrivalRequest.grossRegisterTonnage"
                      >Gross Register Tonnage</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="grossRegisterTonnage"
                      id="field_grossRegisterTonnage"
                      data-cy="grossRegisterTonnage"
                      formControlName="grossRegisterTonnage"
                    />
                    @if (
                      editForm.get('grossRegisterTonnage')?.touched &&
                      editForm.get('grossRegisterTonnage')?.invalid &&
                      editForm.get('grossRegisterTonnage')?.errors?.min
                    ) {
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-label" for="field_deadWeightTonnage" jhiTranslate="srsaApp.arrivalRequest.deadWeightTonnage"
                      >Dead Weight Tonnage</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="deadWeightTonnage"
                      id="field_deadWeightTonnage"
                      data-cy="deadWeightTonnage"
                      formControlName="deadWeightTonnage"
                    />
                    @if (
                      editForm.get('deadWeightTonnage')?.touched &&
                      editForm.get('deadWeightTonnage')?.invalid &&
                      editForm.get('deadWeightTonnage')?.errors?.min
                    ) {
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <!--                  <div class="mb-3 col-4">-->
                  <!--                    <label class="form-label" for="field_portAuthorityName" jhiTranslate="srsaApp.arrivalRequest.portAuthorityName"-->
                  <!--                      >Port Authority Name</label-->
                  <!--                    >-->
                  <!--                    <input-->
                  <!--                      type="text"-->
                  <!--                      class="form-control"-->
                  <!--                      name="portAuthorityName"-->
                  <!--                      id="field_portAuthorityName"-->
                  <!--                      data-cy="portAuthorityName"-->
                  <!--                      formControlName="portAuthorityName"-->
                  <!--                    />-->
                  <!--                  </div>-->

                  <div class="mb-3 col-4">
                    <label
                      class="form-label required"
                      for="field_cargoTonnageWeight"
                      jhiTranslate="srsaApp.arrivalRequest.cargoTonnageWeight"
                      >Cargo Tonnage Weight</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="cargoTonnageWeight"
                      id="field_cargoTonnageWeight"
                      data-cy="cargoTonnageWeight"
                      formControlName="cargoTonnageWeight"
                    />
                    @if (
                      editForm.get('cargoTonnageWeight')?.touched &&
                      editForm.get('cargoTonnageWeight')?.invalid &&
                      editForm.get('cargoTonnageWeight')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (
                      editForm.get('cargoTonnageWeight')?.touched &&
                      editForm.get('cargoTonnageWeight')?.invalid &&
                      editForm.get('cargoTonnageWeight')?.errors?.min
                    ) {
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label
                      class="form-label required"
                      for="field_cargoWeightInCubeMeter"
                      jhiTranslate="srsaApp.arrivalRequest.cargoWeightInCubeMeter"
                      >Cargo Weight In Cube Meter</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="cargoWeightInCubeMeter"
                      id="field_cargoWeightInCubeMeter"
                      data-cy="cargoWeightInCubeMeter"
                      formControlName="cargoWeightInCubeMeter"
                    />
                    @if (
                      editForm.get('cargoWeightInCubeMeter')?.touched &&
                      editForm.get('cargoWeightInCubeMeter')?.invalid &&
                      editForm.get('cargoWeightInCubeMeter')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                    @if (
                      editForm.get('cargoWeightInCubeMeter')?.touched &&
                      editForm.get('cargoWeightInCubeMeter')?.invalid &&
                      editForm.get('cargoWeightInCubeMeter')?.errors?.min
                    ) {
                      <small class="form-text text-danger" jhiTranslate="entity.validation.minlength" [translateValues]="{ min: 1 }"
                        >هذا الحقل لا يمكن أن يكون أطول من 50 احرف.</small
                      >
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-label required" for="field_securityLevel" jhiTranslate="srsaApp.arrivalRequest.securityLevel"
                      >Security Level</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="securityLevel"
                      id="field_securityLevel"
                      data-cy="securityLevel"
                      formControlName="securityLevel"
                    />
                    @if (
                      editForm.get('securityLevel')?.touched &&
                      editForm.get('securityLevel')?.invalid &&
                      editForm.get('securityLevel')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label
                      class="form-label required"
                      for="field_securityCertificateNumber"
                      jhiTranslate="srsaApp.arrivalRequest.securityCertificateNumber"
                      >Security Certificate Number</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      name="securityCertificateNumber"
                      id="field_securityCertificateNumber"
                      data-cy="securityCertificateNumber"
                      formControlName="securityCertificateNumber"
                    />
                    @if (
                      editForm.get('securityCertificateNumber')?.touched &&
                      editForm.get('securityCertificateNumber')?.invalid &&
                      editForm.get('securityCertificateNumber')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label
                      class="form-label required"
                      for="field_securityValidPeriod"
                      jhiTranslate="srsaApp.arrivalRequest.securityValidPeriod"
                      >Security Valid Period</label
                    >
                    <input
                      type="number"
                      class="form-control"
                      name="securityValidPeriod"
                      id="field_securityValidPeriod"
                      data-cy="securityValidPeriod"
                      formControlName="securityValidPeriod"
                    />
                    @if (
                      editForm.get('securityValidPeriod')?.touched &&
                      editForm.get('securityValidPeriod')?.invalid &&
                      editForm.get('securityValidPeriod')?.errors?.required
                    ) {
                      <span class="text-danger" jhiTranslate="entity.validation.required">Name is required</span>
                    }
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-check-label" for="field_armsOrMiltaryEquip" jhiTranslate="srsaApp.arrivalRequest.armsOrMiltaryEquip"
                      >Arms Or Miltary Equip</label
                    >
                    <input
                      type="checkbox"
                      class="form-check-custom"
                      name="armsOrMiltaryEquip"
                      id="field_armsOrMiltaryEquip"
                      data-cy="armsOrMiltaryEquip"
                      formControlName="armsOrMiltaryEquip"
                    />
                  </div>

                  <div class="mb-3 col-4">
                    <label class="form-check-label" for="field_dangerGoods" jhiTranslate="srsaApp.arrivalRequest.dangerGoods"
                      >Danger Goods</label
                    >
                    <input
                      type="checkbox"
                      class="form-check-custom"
                      name="dangerGoods"
                      id="field_dangerGoods"
                      data-cy="dangerGoods"
                      formControlName="dangerGoods"
                    />
                  </div>

                  <div class="col-4 mb-3">
                    <label class="form-check-label" for="field_cargoMovementEqip" jhiTranslate="srsaApp.arrivalRequest.cargoMovementEqip"
                      >Cargo Movement Eqip</label
                    >
                    <input
                      type="checkbox"
                      class="form-check-custom"
                      name="cargoMovementEqip"
                      id="field_cargoMovementEqip"
                      data-cy="cargoMovementEqip"
                      formControlName="cargoMovementEqip"
                    />
                  </div>
                </div>
              </div>

              <div class="card" [hidden]="currentStep !== 3">
                <div class="card-custome-2">
                  <div class="p-4">
                    <div class="row">
                      <div class="col-lg-6 mk-attachments-list d-flex flex-column">
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" checked name="options-base" class="btn-check" id="btnCheck1" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck1"
                              jhiTranslate="srsaApp.arrivalRequest.lastExitDocument"
                              (click)="clickToShow1()"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <!--                          <div class="mk-attachments-item">-->
                          <!--                            <input type="radio" name="options-base" class="btn-check" id="btnCheck2" autocomplete="off" />-->
                          <!--                            <label-->
                          <!--                              class="btn btn-outline-primary rounded-4 required"-->
                          <!--                              for="btnCheck2"-->
                          <!--                              jhiTranslate="srsaApp.arrivalRequest.securityCertificate"-->
                          <!--                              (click)="clickToShow2()"-->
                          <!--                            >-->
                          <!--                            </label>-->
                          <!--                          </div>-->
                        </div>
                      </div>
                      <div class="col-lg-6 mk-custom-dropzone">
                        <div>
                          <dropzone
                            #dropzoneRef1
                            [hidden]="!hidden1"
                            id="DEP_LAST_EXIT_DOC"
                            [config]="getFileConfig()"
                            (addedFile)="onUploadFile($event, 'DEP_LAST_EXIT_DOC')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'DEP_LAST_EXIT_DOC')"
                          >
                          </dropzone>

                          <a
                            *ngIf="arrivalRequest?.id && DEP_LAST_EXIT_DOC && hidden1"
                            (click)="getAttachment(DEP_LAST_EXIT_DOC!.id)"
                            id="cr1"
                            alt="File"
                            >{{ DEP_LAST_EXIT_DOC.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            #dropzoneRef2
                            id="DEP_SECURITY_CERTIFICATE"
                            [config]="getFileConfig()"
                            [hidden]="!hidden2"
                            (addedFile)="onUploadFile($event, 'DEP_SECURITY_CERTIFICATE')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'DEP_SECURITY_CERTIFICATE')"
                          >
                          </dropzone>
                          <a
                            *ngIf="arrivalRequest?.id && DEP_SECURITY_CERTIFICATE && hidden2"
                            (click)="getAttachment(DEP_SECURITY_CERTIFICATE!.id)"
                            id="MainCR1"
                            alt="File"
                            >{{ DEP_SECURITY_CERTIFICATE.docName! }}</a
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card" *ngIf="currentStep === 4">
                <!-- Accordion -->
                <div class="accordion mk-review-step" id="accordionExample">
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/ic_round-add-card.svg"
                        alt="ic_round-add-card.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseOne"
                        aria-expanded="false"
                        aria-controls="collapseOne"
                        jhiTranslate="srsaApp.arrivalRequest.majorData"
                      ></button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem pt-0">
                        <hr />
                        <div>
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.marineMediumNameAr"></label>
                              <p class="fw-bold">
                                {{ selectedMarineMedium?.nameAr }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.marineMediumNameEn"></label>
                              <p class="fw-bold">
                                {{ selectedMarineMedium?.nameEn }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.imoNumber"></label>
                              <p class="fw-bold">
                                {{ editForm.get('imoNumber')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.yachtManufactureYear"></label>
                              <p class="fw-bold">
                                {{ editForm.get('yachtManufactureYear')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.captainName"></label>
                              <p class="fw-bold">
                                {{ editForm.get('captainName')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.nationality"> </label>
                              <p class="fw-bold">
                                {{ isArabic() ? editForm.get('nationality')!.value?.nameAr : editForm.get('nationality')!.value?.nameEn }}
                              </p>
                            </div>
                          </div>

                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.yachtLengthOverAll"> </label>
                              <p class="fw-bold">
                                {{ editForm.get('yachtLengthOverAll')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.arrivalReason"> </label>
                              <p class="fw-bold">
                                {{ isArabic() ? arrivalReasons[3].nameAr : arrivalReasons[3].nameEn }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.estimatedArrival"> </label>
                              <p class="fw-bold">
                                {{ estimatedArrival | formatMediumDatetime }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.estimatedDeparture"> </label>
                              <p class="fw-bold">
                                {{ estimatedDeparture | formatMediumDatetime }}
                              </p>
                            </div>
                          </div>

                          <div class="row g-2 mb-5">
                            <div class="table-responsive">
                              <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                                <thead>
                                  <tr>
                                    <th scope="col" class="" jhiTranslate="srsaApp.arrivalRequest.portType"></th>
                                    <th scope="col" class="" jhiTranslate="srsaApp.arrivalRequest.portCountry"></th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr class="" *ngFor="let route of routes">
                                    <td>{{ 'srsaApp.arrivalRequest.port.' + route?.portType | translate }}</td>
                                    <td>{{ isArabic() ? route.portCountry?.nameAr : route.portCountry?.nameEn }}</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>

                          <div class="row g-2 mb-5">
                            <div class="table-responsive">
                              <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                                <thead>
                                  <tr>
                                    <th scope="col" class="" jhiTranslate="srsaApp.arrivalRequest.numberOfPassengers"></th>
                                    <th scope="col" class="" jhiTranslate="srsaApp.arrivalRequest.nationality"></th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr class="" *ngFor="let passenger of passengers">
                                    <td>{{ passenger.noOfPassengers }}</td>
                                    <td>{{ isArabic() ? passenger.nationality?.nameAr : passenger.nationality?.nameEn }}</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/ic_round-add-card.svg"
                        alt="ic_round-add-card.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseTwo"
                        aria-expanded="false"
                        aria-controls="collapseTwo"
                        jhiTranslate="srsaApp.permitRequest.extraData"
                      ></button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem pt-0">
                        <hr />
                        <div>
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.cargoType"></label>
                              <p class="fw-bold">
                                {{ isArabic() ? editForm.get('cargoType')!.value?.nameAr : editForm.get('cargoType')!.value?.nameEn }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.netRegisterTonnage"></label>
                              <p class="fw-bold">
                                {{ editForm.get('netRegisterTonnage')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.callSign"></label>
                              <p class="fw-bold">
                                {{ editForm.get('callSign')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.arrivalDraft"></label>
                              <p class="fw-bold">
                                {{ editForm.get('arrivalDraft')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.maxDraft"></label>
                              <p class="fw-bold">
                                {{ editForm.get('maxDraft')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.grossRegisterTonnage"></label>
                              <p class="fw-bold">
                                {{ editForm.get('grossRegisterTonnage')!.value }}
                              </p>
                            </div>
                          </div>

                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.deadWeightTonnage"></label>
                              <p class="fw-bold">
                                {{ editForm.get('deadWeightTonnage')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.cargoTonnageWeight"></label>
                              <p class="fw-bold">
                                {{ editForm.get('cargoTonnageWeight')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.cargoWeightInCubeMeter"></label>
                              <p class="fw-bold">
                                {{ editForm.get('cargoWeightInCubeMeter')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.securityLevel"></label>
                              <p class="fw-bold">
                                {{ editForm.get('securityLevel')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.securityCertificateNumber"></label>
                              <p class="fw-bold">
                                {{ editForm.get('securityCertificateNumber')!.value }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.securityValidPeriod"></label>
                              <p class="fw-bold">
                                {{ editForm.get('securityValidPeriod')!.value }}
                              </p>
                            </div>
                          </div>
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.armsOrMiltaryEquip"></label>
                              <p class="fw-bold">
                                {{
                                  editForm.get('armsOrMiltaryEquip')!.value
                                    ? ('srsaApp.arrivalRequest.yes' | translate)
                                    : ('srsaApp.arrivalRequest.no' | translate)
                                }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.dangerGoods"></label>
                              <p class="fw-bold">
                                {{
                                  editForm.get('dangerGoods')!.value
                                    ? ('srsaApp.arrivalRequest.yes' | translate)
                                    : ('srsaApp.arrivalRequest.no' | translate)
                                }}
                              </p>
                            </div>

                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.arrivalRequest.cargoMovementEqip"></label>
                              <p class="fw-bold">
                                {{
                                  editForm.get('cargoMovementEqip')!.value
                                    ? ('srsaApp.arrivalRequest.yes' | translate)
                                    : ('srsaApp.arrivalRequest.no' | translate)
                                }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/mdi_paper-outline.svg"
                        alt="mdi_paper-outline.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseThree"
                        aria-expanded="false"
                        aria-controls="collapseThree"
                        jhiTranslate="srsaApp.permitRequest.attachments"
                      >
                        المرفقات
                      </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem pt-0">
                        <hr />
                        <div class="row g-2 mb-5">
                          <div class="col-4" *ngFor="let doc of documents">
                            <p class="form-label" jhiTranslate="srsaApp.arrivalRequest.attachment.{{ doc.key }}"></p>
                            <button type="button" disabled class="btn btn-light d-flex">
                              <span class="fw-bold doc-name">{{ doc.file.name }}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mk-wizard-actions d-flex-between gap-3 mk-wizard-btns">
                <div class="d-flex-between gap-3">
                  <button
                    type="button"
                    *ngIf="currentStep != 1"
                    (click)="wizardPrevStep()"
                    class="btn btn-outline-primary mk-btn-prev"
                    jhiTranslate="global.menu.previous"
                  >
                    السابق
                  </button>

                  <button
                    type="button"
                    (click)="wizardNextStep()"
                    *ngIf="currentStep !== steps.length"
                    [disabled]="!isCurrentWizardValid() ? 'disabled' : ''"
                    class="btn btn-outline-primary mk-btn-next"
                    jhiTranslate="global.menu.next"
                  >
                    التالي
                  </button>
                  <!-- Button trigger modal -->
                  <div *ngIf="currentStep === steps.length">
                    <button
                      type="button"
                      data-cy="entityCreateCancelButton"
                      class="btn btn-primary"
                      data-bs-toggle="modal"
                      data-bs-target="#liabilityModal"
                    >
                      <span jhiTranslate="srsaApp.permitRequest.detail.sendRequest">إرسال الطلب</span>
                    </button>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Marine Select Modal -->
<div
  class="modal fade"
  id="marineSelectModal"
  #marineSelectModal
  tabindex="-1"
  aria-labelledby="marineSelectModal"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <!--        <div class="d-flex my-3">-->
        <!--          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />-->
        <!--          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="global.termsAndConditions.reviewTermsAndConditions">-->
        <!--            الرجاء الاطلاع والموافقة على الشروط والاحكام-->
        <!--          </p>-->
        <!--        </div>-->

        <input type="radio" name="options-base" class="btn-check" id="pointOfEntry" autocomplete="off" (click)="setRequestType('ENTRY')" />
        <label class="btn btn-primary rounded-4" for="pointOfEntry" jhiTranslate="srsaApp.arrivalRequest.pointOfEntry"> </label>
        <input type="radio" name="options-base" class="btn-check" id="pointOfExit" autocomplete="off" (click)="setRequestType('EXIT')" />
        <label class="btn btn-primary rounded-4" for="pointOfExit" jhiTranslate="srsaApp.arrivalRequest.pointOfExit"> </label>

        @if (marineMediums.length > 0) {
          <div class="py-4">
            <div class="">
              <label class="form-label required" jhiTranslate="srsaApp.mta.marineMedium">الواسطات البحرية</label>

              <select
                required
                class="form-select"
                id="selectedMarineMedium"
                data-cy="selectedMarineMedium"
                [(ngModel)]="selectedMarineMedium"
                (change)="getMarineMedium(selectedMarineMedium)"
              >
                <option [ngValue]="null"></option>
                <option [ngValue]="marineMedium" *ngFor="let marineMedium of marineMediums; trackBy: trackById">
                  {{ isArabic() ? marineMedium.nameAr : marineMedium.nameEn }}
                </option>
              </select>
            </div>
          </div>
        } @else {
          <div class="alert alert-warning" id="no-result-marineMedium">
            <span jhiTranslate="srsaApp.marineMedium.error.noRecords">No Records found</span>
          </div>
        }
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!selectedMarineMedium || !arrivalRequest?.requestType"
          data-bs-toggle="modal"
          data-bs-target="#termsAndConditionsModal"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          jhiTranslate="userManagement.detail.confirm"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="termsAndConditionsModal"
  tabindex="-1"
  aria-labelledby="termsAndConditionsModalLabel"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="d-flex my-3">
          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />
          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="global.termsAndConditions.reviewTermsAndConditions">
            الرجاء الاطلاع والموافقة على الشروط والاحكام
          </p>
        </div>
        <div class="card-new-popUp">
          <div class="card-body">
            <ul class="termsAndCondition" style="overflow: auto">
              <li *ngFor="let serviceTypeTerms of serviceTypeTermsConditions; trackBy: trackIdentityTerms">
                {{ isArabic() ? serviceTypeTerms!.termsConditionsType!.nameAr : serviceTypeTerms!.termsConditionsType!.nameEn }}
              </li>
            </ul>
            <div>
              <div class="check-form d-flex my-3 gap-3">
                <input
                  class="form-check-input"
                  style="margin-top: 0.1em"
                  type="checkbox"
                  [checked]="disclaimerChecked"
                  (change)="disclaimerChecked = !disclaimerChecked"
                  id="disclaimer"
                />
                <label class="fs-5" for="disclaimer" jhiTranslate="srsaApp.permitRequest.termsAndConditions"
                  >اوافق واقر على الشروط والاحكام وسياسات الهيئة السعودية للبحر الاحمر</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!disclaimerChecked"
          (click)="showWizard()"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          jhiTranslate="global.termsAndConditions.accept"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal -->
<div
  class="modal fade"
  id="successModal"
  #successModal
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="staticBackdropLabel1"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <div class="mb-4">
          <img src="../../../../content/images/checked.svg" alt="checked" />
        </div>
        <p class="sendSubmitSub mx-2rem">
          {{ 'srsaApp.permitRequest.createdNumber' | translate: { param: arrivalRequestNumber } }}
        </p>
        <button
          (click)="previousState()"
          type="button"
          class="btn btn-primary"
          data-bs-dismiss="modal"
          jhiTranslate="srsaApp.permitRequest.detail.myRequest"
        >
          اذهب الى طلباتي
        </button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="liabilityModal"
  tabindex="-1"
  aria-labelledby="liabilityModalLabel"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="d-flex my-3">
          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />
          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="srsaApp.permitRequest.detail.obligations">
            الرجاء الاطلاع والموافقة على الشروط والاحكام
          </p>
        </div>
        <div class="card-new-popUp">
          <div class="card-body">
            <ul class="liability" style="overflow: auto">
              <li *ngFor="let serviceTypeLiability of serviceTypeLiabilityType; trackBy: trackIdentityTerms">
                {{ isArabic() ? serviceTypeLiability!.liabilityType!.nameAr : serviceTypeLiability!.liabilityType!.nameEn }}
              </li>
            </ul>
            <div>
              <div class="check-form d-flex my-3 gap-3">
                <input
                  class="form-check-input"
                  style="margin-top: 0.1em"
                  type="checkbox"
                  [checked]="liabilityChecked"
                  (change)="liabilityChecked = !liabilityChecked"
                />
                <label class="fs-5" for="disclaimer" jhiTranslate="srsaApp.permitRequest.termsAndConditions"
                  >اوافق واقر على الشروط والاحكام وسياسات الهيئة السعودية للبحر الاحمر</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!liabilityChecked"
          (click)="save()"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          jhiTranslate="global.termsAndConditions.accept"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="staticBackdropLabel1"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered mk-modal-fix">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <div class="mb-4">
          <img src="../../../../content/images/checked.svg" alt="checked" />
        </div>

        <button
          type="button"
          class="btn btn-primary"
          data-bs-dismiss="modal"
          (click)="previousState()"
          jhiTranslate="srsaApp.permitRequest.detail.myRequest"
        >
          اذهب الى طلباتي
        </button>
      </div>
    </div>
  </div>
</div>
