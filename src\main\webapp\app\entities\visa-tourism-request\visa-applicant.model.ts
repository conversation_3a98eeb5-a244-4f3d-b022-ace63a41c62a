export interface VisaApplicant {
  id?: string; // Optional unique identifier for frontend management
  fullName: string;
  passportNumber: string;
  nationality: string;
  birthDate: string;
  gender: string;
  identityType: string;
  role: string;
  jobTitle: string;
  maritalStatus: string;
  religion: string;
  email: string;
  phoneNumber: string;
  address: string;
  passportIssueDate: string;
  passportExpiryDate: string;
  passportCountry: string;
  birthCountry: string;
  residenceCountry: string;
  expectedEntryDate: string;
  numberOfEntries: string;
  visaDurationDays: number;
  wantsUmrah: boolean;
  hasOtherNationality: boolean;
  otherNationality?: string;
  hasDisability: boolean;
  tookVaccines: boolean;
}
