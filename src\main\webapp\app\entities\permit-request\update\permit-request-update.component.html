<div class="logo-Ebhar-sub">
  <!-- <app-header /> -->
  <div class="mk-permit-container mk-inner-layout-container mk-create-request">
    <div class="">
      <div class="mk-inner-layout-content">
        <form name="editForm" role="form" novalidate (ngSubmit)="save()" [formGroup]="editForm">
          <div>
            <jhi-alert-error></jhi-alert-error>
            <small *ngIf="CrExpired" class="form-text text-danger" jhiTranslate="error.CrExpired"> Cr expired</small>
            <small *ngIf="CrNeedRenewal" class="form-text text-danger" jhiTranslate="error.CrNeedRenewal"> Cr need to be renewed </small>

            <div *ngIf="step == 0 && !wizardDisplay && !CrNeedRenewal && !CrExpired">
              <p class="form-label serviceTypeFont mb-3 my-3" jhiTranslate="srsaApp.serviceType.home.title">
                الرجاء اختيار نوع الرخصة لبدأ الخدمة
              </p>
              <div class="row g-4 g-xl-5">
                <div class="col-lg-4" *ngFor="let serviceType of availableServiceTypeList">
                  <div
                    class="bg-white d-flex flex-column justify-content-between mk-permit-card p-4 rounded-4 position-relative hover-y-10 hover-shadow"
                  >
                    <div>
                      <h3 class="fs-4 item-value mb-2">{{ isArabic() ? serviceType.nameAr : serviceType.nameEn }}</h3>
                      <p class="">{{ isArabic() ? serviceType.descAr : serviceType.descEn }}</p>
                    </div>
                    <div class="d-flex justify-content-end">
                      <a
                        class="btn btn-primary px-4 stretched-link"
                        jhiTranslate="srsaApp.permitRequest.detail.startService"
                        (click)="nextStepSelected(serviceType)"
                        >بدا الخدمة</a
                      >
                    </div>
                  </div>
                </div>
              </div>
              <!-- <button type="button" id="cancel-save1" data-cy="entityCreateCancelButton" class="btn btn-primary" (click)="nextStep(1)">
            <span>التالي</span>
          </button> -->
            </div>
            <div *ngIf="step == 1 && !wizardDisplay">
              <div class="row">
                <div class="">
                  <div class="">
                    <div class="d-flex justify-content-end">
                      <div class="card custom">
                        <div class="d-flex align-items-start">
                          <img src="../../../../content/images/svg-icons/dga/side-card/user.svg" alt="flowbite_user" />
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="srsaApp.permitRequest.targetAudience">الفئة المستهدفة</span>
                            <span class="item-value" jhiTranslate="srsaApp.permitRequest.establishments">منشآت</span>
                          </div>
                        </div>
                        <div class="d-flex align-items-start">
                          <img src="../../../../content/images/svg-icons/dga/side-card/clock.svg" alt="ph_clock-bold" />
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="srsaApp.permitRequest.serviceTime">وقت تنفيذ الخدمة</span>
                            <span class="item-value"
                              >{{ selectedServiceType!.expectedExecutionDays }} {{ isArabic() ? 'يوم' : 'Days' }}</span
                            >
                          </div>
                        </div>
                        <div class="d-flex align-items-start">
                          <img src="../../../../content/images/svg-icons/dga/side-card/computer-phone-sync.svg" alt="Apps" />
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="srsaApp.permitRequest.serviceChannels">قنوات الخدمة</span>
                            <span class="item-value" jhiTranslate="srsaApp.permitRequest.webMobileApp">تطبيق الويب والجوال</span>
                          </div>
                        </div>
                        <div class="d-flex align-items-start">
                          <img src="../../../../content/images/svg-icons/dga/side-card/money-04.svg" alt="solar_wallet-money-" />
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="srsaApp.permitRequest.serviceFees">رسوم الخدمة</span>
                            <span class="item-value">{{ selectedServiceType!.fees }} {{ isArabic() ? 'ريال' : 'Riyal' }}</span>
                          </div>
                        </div>
                        <div class="d-flex">
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="srsaApp.permitRequest.paymentMethods">رسوم الخدمة</span>
                            <span class="item-value">
                              <img
                                src="../../../../content/images/svg-icons/dga/side-card/Payment-method icon-1.svg"
                                alt="payment-method"
                              />
                              <img
                                src="../../../../content/images/svg-icons/dga/side-card/Payment-method-icon-2.svg"
                                alt="payment-method"
                              />
                            </span>
                          </div>
                        </div>

                        <hr class="" />
                        <!--                        <div class="d-flex">-->
                        <!--                          <div class="d-grid ms-3">-->
                        <!--                            <span class="item-label" jhiTranslate="global.form.frequentlyAskedQuestions"> الاسئلة الشائعة</span>-->
                        <!--                            <a-->
                        <!--                              href="Ministry-FAQ's-page-->
                        <!--                            "-->
                        <!--                              ><span class="item-value"-->
                        <!--                                >Ministry-FAQ's-page-->
                        <!--                                <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" /> </span-->
                        <!--                            ></a>-->
                        <!--                          </div>-->
                        <!--                        </div>-->
                        <div class="d-flex align-items-start">
                          <img src="../../../../content/images/svg-icons/dga/side-card/call.svg" alt="call" />
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="global.form.mobileNumber.label">رقم الهاتف</span>
                            <span class="item-value">
                              920005000 <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" />
                            </span>
                          </div>
                        </div>

                        <div class="d-flex align-items-start">
                          <img src="../../../../content/images/svg-icons/dga/side-card/mail.svg" alt="mail" />
                          <div class="d-grid ms-3">
                            <span class="item-label" jhiTranslate="global.form.email.label">البريد الإلكتروني</span>
                            <a href="mailto:<EMAIL>"
                              ><span class="item-value"
                                >{{ '<EMAIL>' }}
                                <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" /> </span
                            ></a>
                          </div>
                        </div>
                        <!--                        <div class="d-flex">-->
                        <!--                          <button class="btn btn-link" jhiTranslate="global.form.downloadUserGuide">تحميل دليل المستخدم</button>-->
                        <!--                        </div>-->
                      </div>
                    </div>
                    <div class="custom-card">
                      <div class="col-lg-8 d-flex-between gap-3 mb-4">
                        <div class="d-flex flex-column justify-content-start my-3 gap-3">
                          <app-page-title [title]="'srsaApp.permitRequest.detail.title' | translate" />

                          <div class="d-grid">
                            <!-- <span class="linces-header" jhiTranslate="srsaApp.permitRequest.detail.title">رخصة</span> -->
                            <span class="linces-header-2">{{
                              isArabic() ? selectedServiceType!.nameAr : selectedServiceType!.nameEn
                            }}</span>
                          </div>
                        </div>
                        <div class="w-sm-100">
                          <!-- Button trigger modal -->
                          <button
                            type="button"
                            class="btn btn-primary px-4 px-xl-5 w-sm-100"
                            data-bs-toggle="modal"
                            data-bs-target="#exampleModal"
                            jhiTranslate="srsaApp.permitRequest.detail.startService"
                          >
                            بدء الخدمة
                          </button>
                        </div>
                      </div>
                      <p class="">
                        {{ isArabic() ? selectedServiceType!.descAr : selectedServiceType!.descEn }}
                      </p>
                    </div>

                    <div class="dga-tabs">
                      <p-tabView>
                        <p-tabPanel [header]="'srsaApp.permitRequest.detail.steps' | translate">
                          <div class="tab-pane fade animate fadeInRight">
                            <ol class="mb-4 d-flex flex-column gap-4">
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.cRDetail">بيانات السجل التجاري</span>
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.nationalAddress"
                                  >بيانات العنوان الوطني</span
                                >
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.permitData">بيانات الرخصة</span>
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.extraData">بيانات إضافية</span>
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.attachments">المرفقات</span>
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.preview">المعاينة</span>
                              </li>
                            </ol>
                          </div>
                        </p-tabPanel>
                        <p-tabPanel [header]="'srsaApp.permitRequest.detail.serviceTerms' | translate">
                          <div class="tab-pane fade animate fadeInRight">
                            <ol class="mb-4 d-flex flex-column gap-4">
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.detail.serviceTerm1"
                                  >ان يكون السجل التجاري نشط</span
                                >
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1">{{
                                  'srsaApp.permitRequest.detail.serviceTerm2'
                                    | translate: { param: isArabic() ? selectedServiceType!.activityAr : selectedServiceType!.activityEn }
                                }}</span>
                              </li>
                              <li class="gap-3 align-items-start">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.detail.serviceTerm3"
                                  >الموافقة على الشروط والأحكام</span
                                >
                              </li>
                            </ol>
                          </div>
                        </p-tabPanel>
                        <p-tabPanel [header]="'srsaApp.permitRequest.detail.requiredDocuments' | translate">
                          <div class="tab-pane fade animate fadeInRight">
                            <ol class="mb-4 d-flex flex-column gap-4" *ngIf="selectedServiceType?.code == 'MO'">
                              <li class="gap-3 align-items-start" *ngFor="let requirement of requiredDocuments.marinaOperator">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1">{{ isArabic() ? requirement.val_ar : requirement.val_en }} </span>
                              </li>
                            </ol>
                            <ol class="mb-4 d-flex flex-column gap-4" *ngIf="selectedServiceType?.code == 'YC'">
                              <li class="gap-3 align-items-start" *ngFor="let requirement of requiredDocuments.yachtCharteringLicense">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1">{{ isArabic() ? requirement.val_ar : requirement.val_en }} </span>
                              </li>
                            </ol>
                            <ol class="mb-4 d-flex flex-column gap-4" *ngIf="selectedServiceType?.code == 'MA'">
                              <li class="gap-3 align-items-start" *ngFor="let requirement of requiredDocuments.maritimeTourismAgent">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1">{{ isArabic() ? requirement.val_ar : requirement.val_en }} </span>
                              </li>
                            </ol>
                            <ol class="mb-4 d-flex flex-column gap-4" *ngIf="selectedServiceType?.code == 'BO'">
                              <li class="gap-3 align-items-start" *ngFor="let requirement of requiredDocuments.beachOperator">
                                <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                                <span class="tabs-data pt-1">{{ isArabic() ? requirement.val_ar : requirement.val_en }} </span>
                              </li>
                            </ol>
                          </div>
                        </p-tabPanel>
                      </p-tabView>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- wizard steps starts from here -->
            <section *ngIf="wizardDisplay">
              <div class="row p-lg-4">
                <app-page-title [title]="'srsaApp.permitRequest.detail.title' | translate" />
              </div>
              <div class="row">
                <div class="d-flex p-lg-4 justify-content-between">
                  <div class="">
                    <span class="linces-header-2">{{ isArabic() ? selectedServiceType!.nameAr : selectedServiceType!.nameEn }}</span>
                  </div>
                  <button type="button" [routerLink]="['/permit-request']" class="btn btn-outline-danger-secondary">
                    <svg-icon src="content/images/svg-icons/dga/cancel.svg"></svg-icon>
                    <span jhiTranslate="srsaApp.permitRequest.cancelRequest"></span>
                  </button>
                </div>
              </div>
              <div class="row mb-4">
                <jhi-wizard-step [steps]="steps" [currentStep]="currentStep"></jhi-wizard-step>
              </div>

              <div *ngIf="currentStep === 1">
                <div class="card p-4 mb-5">
                  <div class="row g-3">
                    <div class="align-items-center col-12 d-flex gap-3">
                      <label class="form-label m-0" jhiTranslate="srsaApp.company.mainCR">mainCR</label>
                      <div class="fs-6 item-value">{{ isArabic() ? registeredCompany!.crName : registeredCompany!.crName }}</div>
                    </div>
                    <div class="col-lg-5 col-md-8">
                      <label class="form-label" jhiTranslate="srsaApp.permitRequest.detail.subsidiaryCommercialRegistrationSelect"
                        >يرجى اختيار السجل التجاري الفرعي للمنشأة</label
                      >
                      <select
                        class="form-select"
                        id="relatedCompany"
                        data-cy="relatedCompany"
                        [(ngModel)]="subCompany"
                        #relatedCompany
                        [ngModelOptions]="{ standalone: true }"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="relatedCompany" *ngFor="let relatedCompany of relatedCompanies">
                          {{ isArabic() ? relatedCompany.nameAr : relatedCompany.nameEn }}
                        </option>
                      </select>
                    </div>
                    <div class="align-items-end col-md-4 col-lg-7 d-flex justify-content-end">
                      <button
                        type="button"
                        id="cancel-save"
                        data-cy="entityCreateCancelButton"
                        class="btn btn-primary w-sm-100 px-5"
                        (click)="onSelectedCR(relatedCompany.value); showCrDetails = true"
                      >
                        <span jhiTranslate="srsaApp.permitRequest.detail.confirmation">تاكيد</span>
                      </button>
                    </div>
                  </div>
                  <p id="jhi-permitRq5" data-cy="PermitRequestCreateUpdateHeading" class="headFont">
                    {{ isArabic() ? 'بيانات المنشآت الفرعيه' : 'Branch Establishments Data' }}
                  </p>
                  <hr />
                  <div class="mb-5">
                    <div class="table-responsive">
                      <table class="table table-striped dga-table" aria-describedby="relatedCompaniesTable">
                        <thead>
                          <tr>
                            <th scope="col" class="text-start">{{ isArabic() ? 'اسم المنشأة' : 'Establishment Name' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'الرقم الموحد' : 'CR National Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'رقم السجل التجاري' : 'CR Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'الرقم الموحد الرئيسي' : 'Main CR National Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'رقم السجل التجاري الرئيسي' : 'Main CR Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'نوع الكيان' : 'Entity Type' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'رقم الإصدار' : 'Version No' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'نوع الارتباط' : 'Relationship Type' }}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let branch of relatedCompanies; trackBy: trackIdentity">
                            <td class="text-start">{{ isArabic() ? branch.nameAr : branch.nameEn }}</td>
                            <td class="text-start">{{ branch.crNationalNumber }}</td>
                            <td class="text-start">{{ branch.crNumber || '-' }}</td>
                            <td class="text-start">{{ branch.mainCrNationalNumber }}</td>
                            <td class="text-start">{{ branch.mainCrNumber || '-' }}</td>
                            <td class="text-start">{{ isArabic() ? branch.entityTypeAr || '-' : branch.entityTypeEn || '-' }}</td>
                            <td class="text-start">{{ branch.versionNo }}</td>
                            <td class="text-start">
                              {{ isArabic() ? (branch.main ? 'رئيسي' : 'فرعي') : branch.main ? 'Main' : 'Branch' }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div class="mk-cr-details" *ngIf="showCrDetails">
                  <p
                    id="jhi-permit4"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.establishmentData"
                  >
                    بيانات المنشأه
                  </p>
                  <hr />
                  <jhi-company-details-tables [crNumber]="registeredCompany?.crNumber || ''"> </jhi-company-details-tables>

                  <p
                    *ngIf="requiredCrField"
                    id="jhi-permitRq7"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.investmentLicense"
                  >
                    رخصة الاستثمار
                  </p>
                  <hr />
                  <div class="row g-2" *ngIf="requiredCrField">
                    <div class="form-field">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.investmentType"> نوع الاستثمار</label>

                      <select
                        required
                        class="form-select"
                        id="selectedInvestmentType"
                        name="selectedInvestmentType"
                        [(ngModel)]="invTypeId"
                        ngModel
                        [ngModelOptions]="{ standalone: true }"
                        (change)="onSelectedInvestmentType()"
                        #invType="ngModel"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="investmentType.id" *ngFor="let investmentType of investmentTypes">
                          {{ isArabic() ? investmentType.nameAr : investmentType.nameEn }}
                        </option>
                      </select>
                      <div *ngIf="invType!.invalid && (invType!.dirty || invType!.touched)">
                        <small class="form-text text-danger" *ngIf="invType?.errors?.required" jhiTranslate="entity.validation.required">
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="form-field" *ngIf="invTypeId === 1">
                      <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensingAuthority"> الجهة المرخصة</label>
                      <select
                        #selectedLicenseProv
                        class="form-select"
                        id="selectedLicenseProviderNational"
                        data-cy="selectedLicenseProvider"
                        formControlName="licenseProvider"
                        (ngModelChange)="onSelectedLicenseProvider($event)"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="licenseProviderItem" *ngFor="let licenseProviderItem of licenseProvidersByCategory">
                          {{ isArabic() ? licenseProviderItem.nameAr : licenseProviderItem.nameEn }}
                        </option>
                      </select>
                    </div>
                    <div class="form-field" *ngIf="invTypeId !== 1">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.licensingAuthority"> الجهة المرخصة</label>

                      <select
                        #selectedLicenseProv
                        class="form-select"
                        id="selectedLicenseProvider"
                        data-cy="selectedLicenseProvider"
                        formControlName="licenseProvider"
                        (ngModelChange)="onSelectedLicenseProvider($event)"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="licenseProviderItem" *ngFor="let licenseProviderItem of licenseProvidersByCategory">
                          {{ isArabic() ? licenseProviderItem.nameAr : licenseProviderItem.nameEn }}
                        </option>
                      </select>
                    </div>
                    <div class="form-field" *ngIf="requiredCrField">
                      <div *ngIf="selectedLicenseProvider?.code == 'MISA' && invTypeId !== 1">
                        <label
                          class="form-label required"
                          for="field_misaLicenseNumber"
                          jhiTranslate="srsaApp.permitRequest.misaLicenseNumber"
                          >Misa License Number</label
                        >
                        <input
                          required
                          type="text"
                          class="form-control"
                          name="misaLicenseNumber"
                          id="field_misaLicenseNumber"
                          data-cy="misaLicenseNumber"
                          formControlName="misaLicenseNumber"
                          minlength="2"
                          maxlength="20"
                        />
                        <div
                          *ngIf="
                            editForm.get('misaLicenseNumber')!.invalid &&
                            (editForm.get('misaLicenseNumber')!.dirty || editForm.get('misaLicenseNumber')!.touched)
                          "
                        >
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('misaLicenseNumber')?.errors?.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                      <div *ngIf="selectedLicenseProvider?.code == 'ECZA' && invTypeId !== 1">
                        <label
                          class="form-label required"
                          for="field_eczaLicenseNumberRequired"
                          jhiTranslate="srsaApp.permitRequest.eczaLicenseNumber"
                          >Ecza License Number</label
                        >

                        <input
                          required
                          type="text"
                          class="form-control"
                          name="eczaLicenseNumberRequired"
                          id="field_eczaLicenseNumberRequired"
                          data-cy="eczaLicenseNumber"
                          formControlName="eczaLicenseNumber"
                          minlength="2"
                          maxlength="20"
                        />
                        <div
                          *ngIf="
                            editForm.get('eczaLicenseNumber')!.invalid &&
                            (editForm.get('eczaLicenseNumber')!.dirty || editForm.get('eczaLicenseNumber')!.touched)
                          "
                        >
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('eczaLicenseNumber')?.errors?.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                      <div *ngIf="selectedLicenseProvider?.code == 'ECZA' && invTypeId === 1">
                        <label class="form-label" for="field_eczaLicenseNumber" jhiTranslate="srsaApp.permitRequest.eczaLicenseNumber"
                          >Ecza License Number</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="eczaLicenseNumber"
                          id="field_eczaLicenseNumber"
                          data-cy="eczaLicenseNumber"
                          formControlName="eczaLicenseNumber"
                          minlength="2"
                          maxlength="20"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card" *ngIf="currentStep === 2">
                <p
                  id="jhi-permitRq4"
                  data-cy="PermitRequestCreateUpdateHeading"
                  class="headFont"
                  jhiTranslate="srsaApp.nationalAddress.detail.title"
                >
                  العنوان الوطني
                </p>
                <hr />
                <div class="jhi-row g-2 mb-5">
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.street">الشارع</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.streetName! }}</p>
                  </div>
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.district">الحي</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.district! }}</p>
                  </div>
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.zipCode">الرمز البريدي</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.zipcode! }}</p>
                  </div>
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.buildingNumber">رقم المبني</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.buildingNumber! }}</p>
                  </div>
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.city">المدينة</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.city! }}</p>
                  </div>
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.additionalNumber">الرقم الإضافي</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.additionalNumber! }}</p>
                  </div>
                  <div class="item">
                    <label class="form-label" jhiTranslate="srsaApp.nationalAddress.shortNumber">الرقم المختصر</label>
                    <p class="item-value">{{ registeredCompany!.nationalAddress!.additionalNumber! }}</p>
                  </div>
                </div>

                <p
                  id="jhi-permitRq4"
                  data-cy="PermitRequestCreateUpdateHeading"
                  class="headFont mt-3"
                  jhiTranslate="srsaApp.permitRequest.workLocation"
                >
                  مقر العمل
                  <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.detail.googleMapSelect"
                    >(الرجاء الضغط على الخريطة أدناه لتحديد الموقع )
                  </span>
                </p>
                <hr />
                <div class="row">
                  <div class="col-12">
                    <google-map
                      #myGoogleMap
                      height="350px"
                      width="100%"
                      [zoom]="zoom"
                      [center]="center"
                      [options]="options"
                      (mapClick)="eventHandler($event, 'mapClick')"
                    >
                      <map-marker
                        #markerElem="mapMarker"
                        *ngFor="let marker of markers"
                        [position]="marker.position"
                        [label]="marker.label"
                        [title]="marker.title"
                        [options]="markerOptions"
                      >
                      </map-marker>
                    </google-map>
                  </div>
                </div>
              </div>
              <div *ngIf="currentStep === 3">
                <div *ngIf="selectedServiceType!.code == 'MA' || selectedServiceType!.code == 'MO'">
                  <p id="jhi-permitRq4" data-cy="PermitRequestCreateUpdateHeading" class="headFont">
                    <span jhiTranslate="srsaApp.permitRequest.maritimeMediation">الواساطات البحرية</span>
                    <span class="text-danger">*</span>
                    <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.selectAllApply"
                      >(الرجاء على اختيار كل ما ينطبق)</span
                    >
                  </p>
                  <hr />
                  <div class="row g-4 mb-5">
                    @for (maritimeMedium of maritimeMediums; track trackMaritimeMedium) {
                      <div
                        class="col-md-6 col-lg-4"
                        *ngIf="
                          ((maritimeMedium.code == 'CruiseShips' || maritimeMedium.code == 'Yachts') &&
                            selectedServiceType!.code == 'MA') ||
                          (maritimeMedium.code != 'CruiseShips' && selectedServiceType!.code == 'MO')
                        "
                      >
                        <input
                          required
                          type="checkbox"
                          class="btn-check"
                          id="btn-check-{{ maritimeMedium.id }}"
                          autocomplete="off"
                          [value]="maritimeMedium.id"
                          [checked]="selectedMaritimeMediumsId!.indexOf(maritimeMedium.id) > -1"
                          (change)="updateCheckedMaritimeMediums(maritimeMedium, $event)"
                        />
                        <label
                          class="align-items-center btn d-flex flex-jhi-row fs-5 gap-3 new-custom py-4 text-primary"
                          for="btn-check-{{ maritimeMedium.id }}"
                        >
                          <img src="../../../../content/images/{{ maritimeMedium.imageName }}" alt="{{ maritimeMedium.imageName }}" />
                          <span class="item-value text-start">{{ isArabic() ? maritimeMedium.nameAr : maritimeMedium.nameEn }}</span>
                        </label>
                      </div>
                    }
                    <div class="row col-4">
                      <p *ngIf="displayOtherTextField" id="jhi-permitRq4" data-cy="PermitRequestCreateUpdateHeading" class="headFont mt-4">
                        <span jhiTranslate="srsaApp.permitRequest.detail.otherVal"> أدخل البيانات الأخرى </span>
                      </p>
                      <input
                        *ngIf="displayOtherTextField"
                        required
                        type="text"
                        class="form-control"
                        name="otherTextValue"
                        id="otherTextValue"
                        data-cy="otherTextValue"
                        [(ngModel)]="otherTextValue"
                        [ngModelOptions]="{ standalone: true }"
                        minlength="2"
                        maxlength="20"
                      />
                    </div>
                  </div>
                </div>
                <div *ngIf="selectedServiceType!.code == 'MA'">
                  <p id="jhi-permitRq4" data-cy="PermitRequestCreateUpdateHeading" class="headFont mt-4">
                    <span jhiTranslate="srsaApp.permitRequest.servicesList">قائمة الخدمات</span>
                    <span class="text-danger small">*</span>
                    <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.selectAllApply"
                      >(الرجاء اختيار كل ما ينطبق)
                    </span>
                  </p>
                  <hr />
                  <div class="row g-4">
                    <div class="col-md-6 col-lg-6 p-0">
                      <!--                      <div *ngIf="maritimeTourismServices && maritimeTourismServices.length > 0">-->
                      <div>
                        <p-multiSelect
                          required
                          [options]="maritimeTourismServices"
                          emptyFilterMessage=" "
                          emptyMessage=" "
                          optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                          dataKey="id"
                          display="chip"
                          appendTo="body"
                          optionDisabled="itemDisabled"
                          [(ngModel)]="selectedMaritimeTourismServices"
                          [ngModelOptions]="{ standalone: true }"
                          #maritimeTourismService="ngModel"
                        ></p-multiSelect>
                        <div *ngIf="maritimeTourismService!.invalid && (maritimeTourismService!.dirty || maritimeTourismService!.touched)">
                          <small
                            class="form-text text-danger"
                            *ngIf="maritimeTourismService!.errors!.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngIf="selectedServiceType!.code == 'BO'">
                  <p id="jhi-permitRq4" data-cy="PermitRequestCreateUpdateHeading" class="headFont mt-4">
                    <span jhiTranslate="srsaApp.permitRequest.beachActivities">انواع الانشطة</span>
                    <span class="text-danger small">*</span>
                    <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.selectAllApply"
                      >(الرجاء اختيار كل ما ينطبق)
                    </span>
                  </p>
                  <hr />
                  <div class="row g-4">
                    <div class="col-md-6 col-lg-6 p-0">
                      <!--                      <div *ngIf="maritimeTourismServices && maritimeTourismServices.length > 0">-->
                      <div>
                        <p-multiSelect
                          required
                          [options]="beachActivities"
                          emptyFilterMessage=" "
                          emptyMessage=" "
                          optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                          dataKey="id"
                          display="chip"
                          appendTo="body"
                          optionDisabled="itemDisabled"
                          [(ngModel)]="selectedBeachActivities"
                          [ngModelOptions]="{ standalone: true }"
                          #beachActivity="ngModel"
                        ></p-multiSelect>
                        <div *ngIf="beachActivity!.invalid && (beachActivity!.dirty || beachActivity!.touched)">
                          <small
                            class="form-text text-danger"
                            *ngIf="beachActivity!.errors!.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p id="jhi-permitRq4" data-cy="PermitRequestCreateUpdateHeading" class="headFont mt-4">
                    <span jhiTranslate="srsaApp.permitRequest.beachEquipment">المعدات التي تستخدم أو سوف تستخدم</span>
                    <span class="text-danger small">*</span>
                    <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.selectAllApply"
                      >(الرجاء اختيار كل ما ينطبق)
                    </span>
                  </p>
                  <div class="row g-4">
                    <div class="col-md-6 col-lg-6 p-0">
                      <!--                      <div *ngIf="maritimeTourismServices && maritimeTourismServices.length > 0">-->
                      <div>
                        <p-multiSelect
                          required
                          [options]="beachEquipments"
                          emptyFilterMessage=" "
                          emptyMessage=" "
                          optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                          dataKey="id"
                          display="chip"
                          appendTo="body"
                          optionDisabled="itemDisabled"
                          [(ngModel)]="selectedBeachEquipments"
                          [ngModelOptions]="{ standalone: true }"
                          #beachEquipment="ngModel"
                        ></p-multiSelect>
                        <div *ngIf="beachEquipment!.invalid && (beachEquipment!.dirty || beachEquipment!.touched)">
                          <small
                            class="form-text text-danger"
                            *ngIf="beachEquipment!.errors!.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngIf="selectedServiceType!.code == 'MO' || selectedServiceType!.code == 'BO'">
                  <p
                    *ngIf="selectedServiceType!.code !== 'BO'"
                    id="jhi-permitRq4"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont mt-4"
                    jhiTranslate="srsaApp.permitRequest.marineLocation"
                  >
                    موقع المارينا
                    <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.deatil.googleMapSelect"
                      >(الرجاء اختيار الموقع من الخريطه)
                    </span>
                  </p>
                  <p
                    *ngIf="selectedServiceType!.code == 'BO'"
                    id="jhi-permitRq4"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont mt-4"
                    jhiTranslate="srsaApp.permitRequest.beachLocation"
                  >
                    موقع الشاطئ
                    <span class="text-muted fw-light small mx-2" jhiTranslate="srsaApp.permitRequest.deatil.googleMapSelect"
                      >(الرجاء اختيار الموقع من الخريطه)
                    </span>
                  </p>
                  <hr />
                  <div class="">
                    <google-map
                      #myGoogleMap
                      height="350px"
                      width="100%"
                      [zoom]="zoom"
                      [center]="centerMarine"
                      [options]="options"
                      (mapClick)="eventHandlerMarine($event, 'mapClick')"
                    >
                      <map-marker
                        #markerElem="mapMarker"
                        *ngFor="let marker of marinaMarkers"
                        [position]="marker.position"
                        [label]="marker.label"
                        [title]="marker.title"
                        [options]="markerOptions"
                      >
                      </map-marker>
                    </google-map>
                  </div>
                </div>
                <div
                  class="row mt-3 g-4 custom-row"
                  *ngIf="
                    selectedServiceType!.code == 'MA' ||
                    selectedServiceType!.code == 'TO' ||
                    selectedServiceType!.code == 'MO' ||
                    selectedServiceType!.code == 'CO' ||
                    selectedServiceType!.code == 'BO' ||
                    selectedServiceType!.code == 'YC'
                  "
                >
                  <div
                    class="col-md-6 col-lg-4"
                    *ngIf="
                      selectedServiceType!.code == 'MA' ||
                      selectedServiceType!.code == 'TO' ||
                      selectedServiceType!.code == 'MO' ||
                      selectedServiceType!.code == 'CO' ||
                      selectedServiceType!.code == 'BO' ||
                      selectedServiceType!.code == 'YC'
                    "
                  >
                    <label class="form-label required" jhiTranslate="srsaApp.permitRequest.licensesDuration">مدة التراخيص المطلوبة</label>

                    <select
                      required
                      class="form-select"
                      id="selectedlicenseDurationValues"
                      data-cy="selectedlicenseDurationValues"
                      [(ngModel)]="selectedlicenseDurationValues"
                      [ngModelOptions]="{ standalone: true }"
                      #licenseDuration="ngModel"
                    >
                      <option [ngValue]="null"></option>
                      <option
                        [ngValue]="licenseDurationValue"
                        *ngFor="let licenseDurationValue of licenseDurationValues"
                        [jhiTranslate]="'srsaApp.permitRequest.licenseDuration.' + licenseDurationValue"
                      ></option>
                    </select>
                    <div *ngIf="licenseDuration!.invalid && (licenseDuration!.dirty || licenseDuration!.touched)">
                      <small
                        class="form-text text-danger"
                        *ngIf="licenseDuration?.errors?.required"
                        jhiTranslate="entity.validation.required"
                      >
                        هذا الحقل مطلوب
                      </small>
                    </div>
                  </div>
                  <div
                    class="col-md-6 col-lg-4"
                    *ngIf="
                      selectedServiceType!.code == 'MA' ||
                      selectedServiceType!.code == 'TO' ||
                      selectedServiceType!.code == 'MO' ||
                      selectedServiceType!.code == 'CO' ||
                      selectedServiceType!.code == 'YC'
                    "
                  >
                    <label class="form-label required" jhiTranslate="srsaApp.permitRequest.providedLanguages">اللغات المقدمة</label>
                    <div *ngIf="providedLanguages && providedLanguages.length > 0">
                      <p-multiSelect
                        required
                        [options]="providedLanguages"
                        optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                        display="chip"
                        appendTo="body"
                        [(ngModel)]="selectedProvidedLanguages"
                        [ngModelOptions]="{ standalone: true }"
                        dataKey="id"
                        #providedLanguage="ngModel"
                      ></p-multiSelect>
                      <div *ngIf="providedLanguage!.invalid && (providedLanguage!.dirty || providedLanguage!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="providedLanguage?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6 col-lg-4">
                    <label class="required" jhiTranslate="srsaApp.permitRequest.previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                      >previously Operated Beach Inside Or Outside Saudi Arabia</label
                    >
                    <select
                      required
                      class="form-control form-select"
                      name="previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                      id="previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                      data-cy="previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                      [(ngModel)]="permitRequest!.licenseRequest!.previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                      [ngModelOptions]="{ standalone: true }"
                      #previouslyOperatedBeachInsideOrOutsideSaudiArabia="ngModel"
                    >
                      <option [ngValue]="true" jhiTranslate="global.form.yes">Yes</option>
                      <option [ngValue]="false" jhiTranslate="global.form.no">No</option>
                    </select>
                    <div
                      *ngIf="
                        previouslyOperatedBeachInsideOrOutsideSaudiArabia!.invalid &&
                        (previouslyOperatedBeachInsideOrOutsideSaudiArabia!.dirty ||
                          previouslyOperatedBeachInsideOrOutsideSaudiArabia!.touched)
                      "
                    >
                      <small
                        class="form-text text-danger"
                        *ngIf="previouslyOperatedBeachInsideOrOutsideSaudiArabia?.errors?.required"
                        jhiTranslate="entity.validation.required"
                      >
                        هذا الحقل مطلوب
                      </small>
                    </div>
                  </div>
                  <div
                    class="col-md-6 col-lg-4"
                    *ngIf="
                      selectedServiceType!.code == 'MA' ||
                      selectedServiceType!.code == 'TO' ||
                      selectedServiceType!.code == 'MO' ||
                      selectedServiceType!.code == 'CO' ||
                      (selectedServiceType!.code == 'BO' &&
                        permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia === true) ||
                      selectedServiceType!.code == 'YC'
                    "
                  >
                    <label class="form-label required" jhiTranslate="srsaApp.permitRequest.operationCountries"
                      >الدول التي عملت بها الشركة</label
                    >

                    <div *ngIf="countries && countries.length > 0">
                      <p-multiSelect
                        required
                        [options]="countries"
                        optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                        display="chip"
                        appendTo="body"
                        dataKey="id"
                        [(ngModel)]="selectedCountries"
                        [ngModelOptions]="{ standalone: true }"
                        #country="ngModel"
                      ></p-multiSelect>
                      <div *ngIf="country!.invalid && (country!.dirty || country!.touched)">
                        <small class="form-text text-danger" *ngIf="country?.errors?.required" jhiTranslate="entity.validation.required">
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                  </div>
                  <div
                    class="col-md-6 col-lg-4"
                    *ngIf="
                      selectedServiceType!.code == 'MA' ||
                      selectedServiceType!.code == 'TO' ||
                      selectedServiceType!.code == 'MO' ||
                      selectedServiceType!.code == 'CO' ||
                      selectedServiceType!.code == 'YC'
                    "
                  >
                    <label class="form-label required" jhiTranslate="srsaApp.permitRequest.totalFundingOfProject"
                      >totalFundingOfProject</label
                    >
                    <input
                      max="9999999999999999999"
                      min="1"
                      required
                      type="number"
                      class="form-control"
                      name="totalFundingOfProject"
                      id="totalFundingOfProject"
                      data-cy="totalFundingOfProject"
                      [(ngModel)]="permitRequest!.totalFundingOfProject"
                      [ngModelOptions]="{ standalone: true }"
                      #totalFundingOfProject="ngModel"
                    />
                    <div *ngIf="totalFundingOfProject!.invalid && (totalFundingOfProject!.dirty || totalFundingOfProject!.touched)">
                      <small
                        class="form-text text-danger"
                        *ngIf="totalFundingOfProject?.errors?.required"
                        jhiTranslate="entity.validation.required"
                      >
                        هذا الحقل مطلوب
                      </small>
                      <small class="form-text text-danger" *ngIf="totalFundingOfProject?.errors?.max">
                        {{ 'entity.validation.max' | translate: { max: 99999 } }}
                      </small>
                      <small class="form-text text-danger" *ngIf="totalFundingOfProject?.errors?.min">
                        {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                      </small>
                    </div>
                  </div>

                  <div class="col-4" *ngIf="selectedServiceType!.code == 'CO' || selectedServiceType!.code == 'YC'">
                    <label class="form-label required" jhiTranslate="srsaApp.permitRequest.maritimeTourAgent">الوكيل الملاحي السياحي</label>

                    <select
                      required
                      class="form-select"
                      id="selectedMaritimeTourismAgent"
                      data-cy="selectedMaritimeTourismAgent"
                      [compareWith]="compareMaritimeTourismAgent"
                      [(ngModel)]="selectedMaritimeTourismAgent"
                      [ngModelOptions]="{ standalone: true }"
                      #MaritimeTourismAgent="ngModel"
                    >
                      <option [ngValue]="null"></option>
                      <option [ngValue]="maritimeTourismAgent" *ngFor="let maritimeTourismAgent of maritimeTourismAgentCompanies">
                        {{ maritimeTourismAgent.crName }}
                      </option>
                    </select>
                    <div *ngIf="MaritimeTourismAgent!.invalid && (MaritimeTourismAgent!.dirty || MaritimeTourismAgent!.touched)">
                      <small
                        class="form-text text-danger"
                        *ngIf="MaritimeTourismAgent?.errors?.required"
                        jhiTranslate="entity.validation.required"
                      >
                        هذا الحقل مطلوب
                      </small>
                    </div>
                  </div>
                  <div
                    class="col-md-6 col-lg-4"
                    *ngIf="
                      selectedServiceType!.code == 'MA' ||
                      selectedServiceType!.code == 'TO' ||
                      selectedServiceType!.code == 'MO' ||
                      selectedServiceType!.code == 'CO' ||
                      selectedServiceType!.code == 'YC'
                    "
                  >
                    <label class="form-label required" jhiTranslate="srsaApp.permitRequest.yearsOfExperience"
                      >عدد سنوات الخبره في النشاط</label
                    >

                    <input
                      max="999"
                      min="1"
                      required
                      type="number"
                      class="form-control"
                      name="noOfYearsExperience"
                      id="noOfYearsExperience"
                      data-cy="noOfYearsExperience"
                      [(ngModel)]="permitRequest!.licenseRequest!.noOfYearsExperience"
                      [ngModelOptions]="{ standalone: true }"
                      #noOfYearsExperience="ngModel"
                    />
                    <div *ngIf="noOfYearsExperience!.invalid && (noOfYearsExperience!.dirty || noOfYearsExperience!.touched)">
                      <small
                        class="form-text text-danger"
                        *ngIf="noOfYearsExperience?.errors?.required"
                        jhiTranslate="entity.validation.required"
                      >
                        هذا الحقل مطلوب
                      </small>
                      <small class="form-text text-danger" *ngIf="noOfYearsExperience?.errors?.max">
                        {{ 'entity.validation.max' | translate: { max: 999 } }}
                      </small>
                      <small class="form-text text-danger" *ngIf="noOfYearsExperience?.errors?.min">
                        {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                      </small>
                    </div>
                  </div>
                  <div class="col-md-6 col-lg-4" *ngIf="selectedServiceType!.code == 'MO' || selectedServiceType!.code == 'YC'">
                    <label
                      class="form-label"
                      [title]="'srsaApp.permitRequest.noOfOwnedMaritimeMediumsWorld' | translate"
                      jhiTranslate="srsaApp.permitRequest.noOfOwnedMaritimeMediumsWorld"
                      >عدد الوسائط البحرية</label
                    >

                    <input
                      max="999"
                      min="1"
                      type="number"
                      class="form-control"
                      name="noOfOwnedMaritimeMediumsWorld"
                      id="noOfOwnedMaritimeMediumsWorld"
                      data-cy="noOfOwnedMaritimeMediumsWorld"
                      [(ngModel)]="permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsWorld"
                      [ngModelOptions]="{ standalone: true }"
                      #noOfOwnedMaritimeMediumsWorld="ngModel"
                    />
                    <div
                      *ngIf="
                        noOfOwnedMaritimeMediumsWorld!.invalid &&
                        (noOfOwnedMaritimeMediumsWorld!.dirty || noOfOwnedMaritimeMediumsWorld!.touched)
                      "
                    >
                      <small class="form-text text-danger" *ngIf="noOfOwnedMaritimeMediumsWorld?.errors?.max">
                        {{ 'entity.validation.max' | translate: { max: 999 } }}
                      </small>
                      <small class="form-text text-danger" *ngIf="noOfOwnedMaritimeMediumsWorld?.errors?.min">
                        {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                      </small>
                    </div>
                  </div>
                  <div class="col-md-6 col-lg-4" *ngIf="selectedServiceType!.code == 'MO' || selectedServiceType!.code == 'YC'">
                    <label
                      class="form-label"
                      [title]="'srsaApp.permitRequest.noOfOwnedMaritimeMediumsKsa' | translate"
                      jhiTranslate="srsaApp.permitRequest.noOfOwnedMaritimeMediumsKsa"
                      >عدد الوسائط البحرية</label
                    >

                    <input
                      max="999"
                      min="1"
                      type="number"
                      class="form-control"
                      name="noOfOwnedMaritimeMediumsKsa"
                      id="noOfOwnedMaritimeMediumsKsa"
                      data-cy="noOfOwnedMaritimeMediumsKsa"
                      [(ngModel)]="permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsKsa"
                      [ngModelOptions]="{ standalone: true }"
                      #noOfOwnedMaritimeMediumsKsa="ngModel"
                    />
                    <div
                      *ngIf="
                        noOfOwnedMaritimeMediumsKsa!.invalid && (noOfOwnedMaritimeMediumsKsa!.dirty || noOfOwnedMaritimeMediumsKsa!.touched)
                      "
                    >
                      <small class="form-text text-danger" *ngIf="noOfOwnedMaritimeMediumsKsa?.errors?.max">
                        {{ 'entity.validation.max' | translate: { max: 999 } }}
                      </small>
                      <small class="form-text text-danger" *ngIf="noOfOwnedMaritimeMediumsKsa?.errors?.min">
                        {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                      </small>
                    </div>
                  </div>
                  <div
                    class="col-md-6 col-lg-4"
                    *ngIf="
                      selectedServiceType!.code == 'MA' ||
                      selectedServiceType!.code == 'TO' ||
                      selectedServiceType!.code == 'MO' ||
                      selectedServiceType!.code == 'CO' ||
                      selectedServiceType!.code == 'YC'
                    "
                  >
                    <label
                      class="form-label"
                      [title]="'srsaApp.permitRequest.noOfManagedMaritimeMediumsGeneral' | translate"
                      jhiTranslate="srsaApp.permitRequest.noOfManagedMaritimeMediumsGeneral"
                      >عدد الوسائط البحرية المدارة من الشركه بشكل عام</label
                    >
                    <input
                      max="99999999"
                      min="1"
                      type="number"
                      class="form-control"
                      name="noOfManagedMaritimeMediumsGeneral"
                      id="noOfManagedMaritimeMediumsGeneral"
                      data-cy="noOfManagedMaritimeMediumsGeneral"
                      [(ngModel)]="permitRequest!.licenseRequest!.noOfManagedMaritimeMediumsGeneral"
                      [ngModelOptions]="{ standalone: true }"
                      #noOfManagedMaritimeMediumsGeneral="ngModel"
                    />
                    <div
                      *ngIf="
                        noOfManagedMaritimeMediumsGeneral!.invalid &&
                        (noOfManagedMaritimeMediumsGeneral!.dirty || noOfManagedMaritimeMediumsGeneral!.touched)
                      "
                    >
                      <small class="form-text text-danger" *ngIf="noOfManagedMaritimeMediumsGeneral?.errors?.max">
                        {{ 'entity.validation.max' | translate: { max: 99999999 } }}
                      </small>
                      <small class="form-text text-danger" *ngIf="noOfManagedMaritimeMediumsGeneral?.errors?.min">
                        {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                      </small>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="currentStep === 4">
                <section class="mb-5">
                  <p
                    id="jhi-permit8"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.workforceData"
                  >
                    بيانات ��لقوى العاملة
                  </p>
                  <hr />
                  <div class="row g-4 mb-4 custom-row">
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.ministryOfHumanResourcesNumber"
                        >رقم المنشأة لدى وزارة الموارد البشرية</label
                      >

                      <input
                        required
                        type="number"
                        class="form-control"
                        name="ministryOfHumanResourcesNumber"
                        id="field_ministryOfHumanResourcesNumber"
                        data-cy="ministryOfHumanResourcesNumber"
                        formControlName="ministryOfHumanResourcesNumber"
                        min="1"
                        max="99999999999999999999"
                      />
                      <div
                        *ngIf="
                          editForm.get('ministryOfHumanResourcesNumber')!.invalid &&
                          (editForm.get('ministryOfHumanResourcesNumber')!.dirty || editForm.get('ministryOfHumanResourcesNumber')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('ministryOfHumanResourcesNumber')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>

                        <small class="form-text text-danger" *ngIf="editForm.get('ministryOfHumanResourcesNumber')!.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 999 } }}
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('ministryOfHumanResourcesNumber')!.errors?.min">
                          {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.saudiLaborCount">عدد العاملين السعوديين</label>

                      <input
                        max="9999999"
                        min="0"
                        required
                        type="number"
                        class="form-control"
                        name="saudiLaborCount"
                        id="field_saudiLaborCount"
                        data-cy="saudiLaborCount"
                        formControlName="saudiLaborCount"
                      />
                      <div
                        *ngIf="
                          editForm.get('saudiLaborCount')!.invalid &&
                          (editForm.get('saudiLaborCount')!.dirty || editForm.get('saudiLaborCount')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('saudiLaborCount')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('saudiLaborCount')!.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999999 } }}
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('saudiLaborCount')!.errors?.min">
                          {{ 'entity.validation.minNumber' | translate: { min: 0 } }}
                        </small>
                        <small class="form-text text-danger" *ngIf="totalWorkersError()">
                          {{ 'entity.validation.workersError' | translate }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.foreignersLaborCount"
                        >عدد العاملين الأجانب</label
                      >

                      <input
                        max="9999999"
                        required
                        type="number"
                        class="form-control"
                        name="foreignersLaborCount"
                        id="field_foreignersLaborCount"
                        data-cy="foreignersLaborCount"
                        formControlName="foreignersLaborCount"
                        min="0"
                      />
                      <div
                        *ngIf="
                          editForm.get('foreignersLaborCount')!.invalid &&
                          (editForm.get('foreignersLaborCount')!.dirty || editForm.get('foreignersLaborCount')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('foreignersLaborCount')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('foreignersLaborCount')?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999999 } }}
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('foreignersLaborCount')!.errors?.min">
                          {{ 'entity.validation.minNumber' | translate: { min: 0 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfMaleEmployees"
                        >عدد العاملين الذكور</label
                      >

                      <input
                        [max]="getMaxMales()"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfMaleEmployees"
                        id="field_numberOfMaleEmployees"
                        data-cy="numberOfMaleEmployees"
                        formControlName="numberOfMaleEmployees"
                        min="0"
                      />
                      <div
                        *ngIf="
                          editForm.get('numberOfMaleEmployees')!.invalid &&
                          (editForm.get('numberOfMaleEmployees')!.dirty || editForm.get('numberOfMaleEmployees')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('numberOfMaleEmployees')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('numberOfMaleEmployees')!.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: getMaxMales() } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfFemaleEmployees"
                        >عدد العاملين الاناث</label
                      >

                      <input
                        [max]="getMaxFemales()"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfFemaleEmployees"
                        id="field_numberOfFemaleEmployees"
                        data-cy="numberOfFemaleEmployees"
                        formControlName="numberOfFemaleEmployees"
                        min="0"
                      />
                      <div
                        *ngIf="
                          editForm.get('numberOfFemaleEmployees')!.invalid &&
                          (editForm.get('numberOfFemaleEmployees')!.dirty || editForm.get('numberOfFemaleEmployees')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('numberOfFemaleEmployees')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('numberOfFemaleEmployees')!.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: getMaxFemales() } }}
                        </small>
                      </div>
                    </div>
                    <small
                      class="form-text text-danger mt-3"
                      *ngIf="
                        maleFemaleTotalError() &&
                        ((editForm.get('numberOfMaleEmployees')!.touched && editForm.get('numberOfFemaleEmployees')!.touched) || editMode)
                      "
                    >
                      {{ 'srsaApp.permitRequest.maleFemaleTotalError' | translate }}
                    </small>
                  </div>
                  <div class="row g-4 mb-4 custom-row">
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.saudizationLevel">مستوي التوطين</label>

                      <select
                        required
                        class="form-select"
                        id="selectedSaudizationLevel"
                        formControlName="saudizationLevel"
                        data-cy="selectedSaudizationLevel"
                      >
                        <option [ngValue]="null"></option>
                        <option
                          [ngValue]="saudizationLevelValue"
                          *ngFor="let saudizationLevelValue of saudizationLevelValues"
                          [jhiTranslate]="'srsaApp.permitRequest.saudiLevel.' + saudizationLevelValue"
                        ></option>
                      </select>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.rateRange"> معدل النطاق %</label>

                      <input
                        max="100"
                        required
                        type="number"
                        class="form-control"
                        name="rateRange"
                        id="field_rateRange"
                        data-cy="rateRange"
                        formControlName="rateRange"
                        min="1"
                      />
                      <div
                        *ngIf="
                          editForm.get('rateRange')!.invalid && (editForm.get('rateRange')!.dirty || editForm.get('rateRange')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('rateRange')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('rateRange')!.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 100 } }}
                        </small>
                        <small class="form-text text-danger" *ngIf="editForm.get('rateRange')!.errors?.min">
                          {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                        </small>
                      </div>
                    </div>
                  </div>
                </section>
                <section class="mb-5">
                  <p
                    id="jhi-permit8"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.contactInformation"
                  >
                    بيانات التواصل
                  </p>
                  <hr />
                  <div class="row g-4 custom-row">
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.contactOfficerName">اسم ضابط الإتصال</label>

                      <input
                        required
                        type="text"
                        class="form-control"
                        name="contactOfficerName"
                        id="field_contactOfficerName"
                        data-cy="contactOfficerName"
                        formControlName="contactOfficerName"
                        minlength="2"
                        maxlength="10"
                      />
                      <div
                        *ngIf="
                          editForm.get('contactOfficerName')!.invalid &&
                          (editForm.get('contactOfficerName')!.dirty || editForm.get('contactOfficerName')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerName')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.contactOfficerEmailAddress"
                        >البريد الإلكتروني</label
                      >

                      <input
                        required
                        type="email"
                        class="form-control"
                        name="contactOfficerEmailAddress"
                        id="field_contactOfficerEmailAddress"
                        data-cy="contactOfficerEmailAddress"
                        formControlName="contactOfficerEmailAddress"
                        minlength="5"
                        maxlength="30"
                        email
                        pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$"
                      />
                      <div
                        *ngIf="
                          editForm.get('contactOfficerEmailAddress')!.invalid &&
                          (editForm.get('contactOfficerEmailAddress')!.dirty || editForm.get('contactOfficerEmailAddress')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerEmailAddress')?.errors?.required"
                          jhiTranslate="global.messages.validate.email.required"
                        >
                          البريد الإلكتروني الخاصة بك مطلوب.
                        </small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            editForm.get('contactOfficerEmailAddress')?.errors?.invalid ||
                            editForm.get('contactOfficerEmailAddress')?.errors?.pattern
                          "
                          jhiTranslate="global.messages.validate.email.invalid"
                        >
                          بريدك الالكتروني خاطئ.
                        </small>
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerEmailAddress')?.errors?.minlength"
                          jhiTranslate="global.messages.validate.email.minlength"
                        >
                          البريد اﻹلكتروني لا يمكن أن يكون اقل 4 احرف.
                        </small>
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerEmailAddress')?.errors?.maxlength"
                          jhiTranslate="global.messages.validate.email.maxlength"
                        >
                          البريد اﻹلكتروني لا يمكن أن يكون اكثر من 50 حرفا.
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.contactOfficerMobileNumber">رقم الجوال</label>

                      <input
                        required
                        type="text"
                        class="form-control"
                        name="contactOfficerMobileNumber"
                        id="field_contactOfficerMobileNumber"
                        data-cy="contactOfficerMobileNumber"
                        formControlName="contactOfficerMobileNumber"
                        minlength="9"
                        maxlength="14"
                        pattern="(9665)(5|0|3|6|4|9|1|8|7)([0-9]{7})$"
                      />
                      <div
                        *ngIf="
                          editForm.get('contactOfficerMobileNumber')!.invalid &&
                          (editForm.get('contactOfficerMobileNumber')!.dirty || editForm.get('contactOfficerMobileNumber')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerMobileNumber')?.errors?.required"
                          jhiTranslate="register.messages.validate.mobileNumber.required"
                        >
                          اسم المستخدم الخاصة بك مطلوب.
                        </small>
                        <br />
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerMobileNumber')?.errors?.pattern"
                          jhiTranslate="register.messages.validate.mobileNumber.pattern"
                        >
                          رقم الجوال غير صحيح يجب أن يبدأ ب 9665.
                        </small>
                        <br />
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerMobileNumber')?.errors?.minlength"
                          jhiTranslate="register.messages.validate.mobileNumber.minlength"
                        >
                          اسم المستخدم الخاصة بك مطلوب ليكون على الأقل 1 حرف.
                        </small>
                        <br />
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('contactOfficerMobileNumber')?.errors?.maxlength"
                          jhiTranslate="register.messages.validate.mobileNumber.maxlength"
                        >
                          اسم المستخدم الخاصة بك لا يمكن أن يكون أطول من 50 حرفا.
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label" jhiTranslate="srsaApp.permitRequest.vatRegistrationNumber">الرقم الضريبي (إن وجد)</label>
                      <input
                        pattern="^\d{15}$"
                        [maxLength]="15"
                        type="text"
                        class="form-control"
                        name="vatRegistrationNumber"
                        id="field_vatRegistrationNumber"
                        data-cy="vatRegistrationNumber"
                        formControlName="vatRegistrationNumber"
                      />
                      <!--                  *ngIf="editForm.get('vatRegistrationNumber')!.invalid && (editForm.get('vatRegistrationNumber')!.dirty || editForm.get('vatRegistrationNumber')!.touched)">-->

                      <small class="form-text text-danger" *ngIf="isVatNumberValid()">
                        {{ 'entity.validation.minlength' | translate: { min: 15 } }}
                      </small>
                    </div>
                  </div>
                </section>
                <section class="mb-5" *ngIf="selectedServiceType!.code == 'MO' || selectedServiceType!.code == 'BO'">
                  <p
                    id="jhi-permit11"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.extraData"
                  >
                    بيانات إضافيه
                  </p>
                  <hr />

                  <div class="row g-4 custom-row" *ngIf="selectedServiceType!.code == 'BO'">
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.beachOwnerName">اسم مالك الشاطئ</label>
                      <input
                        required
                        type="text"
                        class="form-control"
                        name="beachOwnerName"
                        id="beachOwnerName"
                        data-cy="beachOwnerName"
                        [(ngModel)]="permitRequest!.licenseRequest!.beachOwnerName"
                        [ngModelOptions]="{ standalone: true }"
                        #beachOwnerName="ngModel"
                        minlength="2"
                        maxlength="60"
                      />
                      <div *ngIf="beachOwnerName!.invalid && (beachOwnerName!.dirty || beachOwnerName!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="beachOwnerName?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <!--                        <small class="form-text text-danger" [hidden]="!beachOwnerName!.errors?.pattern">-->
                        <!--                          {{ 'entity.validation.patternAR' | translate }}-->
                        <!--                        </small>-->
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.beachTradeName">الاسم التجاري للشاطئ</label>
                      <input
                        required
                        type="text"
                        class="form-control"
                        name="beachTradeName"
                        id="beachTradeName"
                        data-cy="beachTradeName"
                        [(ngModel)]="permitRequest!.licenseRequest!.beachTradeName"
                        [ngModelOptions]="{ standalone: true }"
                        #beachTradeName="ngModel"
                        minlength="2"
                        maxlength="90"
                      />
                      <div *ngIf="beachTradeName!.invalid && (beachTradeName!.dirty || beachTradeName!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="beachTradeName?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <!--                        <small class="form-text text-danger" [hidden]="!beachTradeName!.errors?.pattern">-->
                        <!--                          {{ 'entity.validation.patternEN' | translate }}-->
                        <!--                        </small>-->
                      </div>
                    </div>

                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.beachOwnerCommercialRegistrationNumber"
                        >رقم السجل التجاري للشاطئ</label
                      >

                      <input
                        required
                        type="text"
                        class="form-control"
                        name="beachOwnerCommercialRegistrationNumber"
                        id="beachOwnerCommercialRegistrationNumber"
                        data-cy="beachOwnerCommercialRegistrationNumber"
                        [(ngModel)]="permitRequest!.licenseRequest!.beachOwnerCommercialRegistrationNumber"
                        [ngModelOptions]="{ standalone: true }"
                        #beachOwnerCommercialRegistrationNumber="ngModel"
                        minlength="2"
                        maxlength="30"
                      />
                      <div
                        *ngIf="
                          beachOwnerCommercialRegistrationNumber!.invalid &&
                          (beachOwnerCommercialRegistrationNumber!.dirty || beachOwnerCommercialRegistrationNumber!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="beachOwnerCommercialRegistrationNumber?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>

                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfRestrooms">No Of Restrooms</label>

                      <input
                        max="9999"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfRestrooms"
                        id="numberOfRestrooms"
                        data-cy="numberOfRestrooms"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfRestrooms"
                        [ngModelOptions]="{ standalone: true }"
                        #numberOfRestrooms="ngModel"
                      />
                      <div *ngIf="numberOfRestrooms!.invalid && (numberOfRestrooms!.dirty || numberOfRestrooms!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="numberOfRestrooms?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="numberOfRestrooms?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfLifeguards">No Of Lifeguards</label>
                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfLifeguards"
                        id="numberOfLifeguards"
                        data-cy="numberOfLifeguards"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfLifeguards"
                        [ngModelOptions]="{ standalone: true }"
                        #numberOfLifeguards="ngModel"
                      />
                      <div *ngIf="numberOfLifeguards!.invalid && (numberOfLifeguards!.dirty || numberOfLifeguards!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="numberOfLifeguards?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="numberOfLifeguards?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.isThereCabinsForRent"
                        >is There Cabins For Rent</label
                      >
                      <select
                        required
                        class="form-control form-select"
                        name="isThereCabinsForRent"
                        id="isThereCabinsForRent"
                        data-cy="isThereCabinsForRent"
                        [(ngModel)]="permitRequest!.licenseRequest!.isThereCabinsForRent"
                        [ngModelOptions]="{ standalone: true }"
                        #isThereCabinsForRent="ngModel"
                      >
                        <option [ngValue]="true" jhiTranslate="global.form.yes">Yes</option>
                        <option [ngValue]="false" jhiTranslate="global.form.no">No</option>
                      </select>
                      <div *ngIf="isThereCabinsForRent!.invalid && (isThereCabinsForRent!.dirty || isThereCabinsForRent!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="isThereCabinsForRent?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4" *ngIf="permitRequest!.licenseRequest!.isThereCabinsForRent === true">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfCabinsAndRooms"
                        >No Of Cabins And Rooms</label
                      >
                      <input
                        max="99999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfCabinsAndRooms"
                        id="numberOfCabinsAndRooms"
                        data-cy="noOfMarinaFacilities"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfCabinsAndRooms"
                        [ngModelOptions]="{ standalone: true }"
                        #numberOfCabinsAndRooms="ngModel"
                      />
                      <div *ngIf="numberOfCabinsAndRooms!.invalid && (numberOfCabinsAndRooms!.dirty || numberOfCabinsAndRooms!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="numberOfCabinsAndRooms?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="numberOfCabinsAndRooms?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 99999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfFoodTruckParking"
                        >number Of Food Truck Parking</label
                      >
                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfFoodTruckParking"
                        id="numberOfFoodTruckParking"
                        data-cy="numberOfFoodTruckParking"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfFoodTruckParking"
                        [ngModelOptions]="{ standalone: true }"
                        #numberOfFoodTruckParking="ngModel"
                      />
                      <div
                        *ngIf="numberOfFoodTruckParking!.invalid && (numberOfFoodTruckParking!.dirty || numberOfFoodTruckParking!.touched)"
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="numberOfFoodTruckParking?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="numberOfFoodTruckParking?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.visitorCapacity">visitor Capacity</label>

                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="visitorCapacity"
                        id="visitorCapacity"
                        data-cy="visitorCapacity"
                        [(ngModel)]="permitRequest!.licenseRequest!.visitorCapacity"
                        [ngModelOptions]="{ standalone: true }"
                        #visitorCapacity="ngModel"
                      />
                      <div *ngIf="visitorCapacity!.invalid && (visitorCapacity!.dirty || visitorCapacity!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="visitorCapacity?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="visitorCapacity?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.isThereMarineVessel"
                        >is There Marine Vessel</label
                      >
                      <select
                        required
                        class="form-control form-select"
                        name="isThereMarineVessel"
                        id="isThereMarineVessel"
                        data-cy="isThereMarineVessel"
                        [(ngModel)]="permitRequest!.licenseRequest!.isThereMarineVessel"
                        [ngModelOptions]="{ standalone: true }"
                        #isThereMarineVessel="ngModel"
                      >
                        <option [ngValue]="true" jhiTranslate="global.form.yes">Yes</option>
                        <option [ngValue]="false" jhiTranslate="global.form.no">No</option>
                      </select>
                      <div *ngIf="isThereMarineVessel!.invalid && (isThereMarineVessel!.dirty || isThereMarineVessel!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="isThereMarineVessel?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4" *ngIf="permitRequest!.licenseRequest!.isThereMarineVessel === true">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfOtherMarineVessel"
                        >number Of Other Marine Vessel</label
                      >
                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfOtherMarineVessel"
                        id="numberOfOtherMarineVessel"
                        data-cy="numberOfOtherMarineVessel"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfOtherMarineVessel"
                        [ngModelOptions]="{ standalone: true }"
                        #numberOfOtherMarineVessel="ngModel"
                      />
                      <div
                        *ngIf="
                          numberOfOtherMarineVessel!.invalid && (numberOfOtherMarineVessel!.dirty || numberOfOtherMarineVessel!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="numberOfOtherMarineVessel?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="numberOfOtherMarineVessel?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>

                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.noOfWaterStations">No Of Water Stations</label>

                      <input
                        max="9999"
                        required
                        type="number"
                        class="form-control"
                        name="noOfWaterStations"
                        id="noOfWaterStationsBeach"
                        data-cy="noOfWaterStations"
                        [(ngModel)]="permitRequest!.licenseRequest!.noOfWaterStations"
                        [ngModelOptions]="{ standalone: true }"
                        #noOfWaterStations="ngModel"
                      />
                      <div *ngIf="noOfWaterStations!.invalid && (noOfWaterStations!.dirty || noOfWaterStations!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="noOfWaterStations?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="noOfWaterStations?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                  </div>
                  <div class="row g-4 custom-row" *ngIf="selectedServiceType!.code == 'MO'">
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.marinaOwnerName">اسم مالك المارينا</label>
                      <input
                        required
                        type="text"
                        class="form-control"
                        name="marinaOwnerName"
                        pattern="^[\u0621-\u064A 0-9]*$"
                        id="marinaOwnerName"
                        data-cy="marinaOwnerName"
                        [(ngModel)]="permitRequest!.licenseRequest!.marinaOwnerNameAr"
                        [ngModelOptions]="{ standalone: true }"
                        #marinaOwnerName="ngModel"
                        minlength="2"
                        maxlength="30"
                      />
                      <div *ngIf="marinaOwnerName!.invalid && (marinaOwnerName!.dirty || marinaOwnerName!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="marinaOwnerName?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" [hidden]="!marinaOwnerName!.errors?.pattern">
                          {{ 'entity.validation.patternAR' | translate }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.marinaOwnerNameEn">اسم مالك المارينا</label>
                      <input
                        required
                        type="text"
                        class="form-control"
                        pattern="^[a-zA-Z 0-9]*$"
                        name="marinaOwnerNameEn"
                        id="marinaOwnerNameEn"
                        data-cy="marinaOwnerNameEn"
                        [(ngModel)]="permitRequest!.licenseRequest!.marinaOwnerNameEn"
                        [ngModelOptions]="{ standalone: true }"
                        #marinaOwnerNameEn="ngModel"
                        minlength="2"
                        maxlength="30"
                      />
                      <div *ngIf="marinaOwnerNameEn!.invalid && (marinaOwnerNameEn!.dirty || marinaOwnerNameEn!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="marinaOwnerNameEn?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" [hidden]="!marinaOwnerNameEn!.errors?.pattern">
                          {{ 'entity.validation.patternEN' | translate }}
                        </small>
                      </div>
                    </div>

                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.marinaCrNumber"
                        >رقم السجل التجاري للمارينا</label
                      >

                      <input
                        required
                        type="text"
                        class="form-control"
                        name="marinaCrNumber"
                        id="marinaCrNumber"
                        data-cy="marinaCrNumber"
                        [(ngModel)]="permitRequest!.licenseRequest!.marinaCrNumber"
                        [ngModelOptions]="{ standalone: true }"
                        #marinaCrNumber="ngModel"
                        minlength="2"
                        maxlength="20"
                      />
                      <div *ngIf="marinaCrNumber!.invalid && (marinaCrNumber!.dirty || marinaCrNumber!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="marinaCrNumber?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.marinaCrNameAr">
                        (العربي) الاسم التجاري للمارينا</label
                      >

                      <input
                        required
                        type="text"
                        class="form-control"
                        name="marinaCrNameAr"
                        id="marinaCrNameAr"
                        data-cy="marinaCrNameAr"
                        pattern="^[\u0621-\u064A 0-9]*$"
                        [(ngModel)]="permitRequest!.licenseRequest!.marinaCrNameAr"
                        [ngModelOptions]="{ standalone: true }"
                        #marinaCrNameAr="ngModel"
                        minlength="2"
                        maxlength="20"
                      />

                      <div *ngIf="marinaCrNameAr!.invalid && (marinaCrNameAr!.dirty || marinaCrNameAr!.touched)">
                        <small class="form-text text-danger" [hidden]="!marinaCrNameAr!.errors?.pattern">
                          {{ 'entity.validation.patternAR' | translate }}
                        </small>

                        <small
                          class="form-text text-danger"
                          *ngIf="marinaCrNameAr?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>

                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.noOfFuelStations">No Of Fuel Stations</label>

                      <input
                        max="9999"
                        required
                        type="number"
                        class="form-control"
                        name="noOfFuelStations"
                        id="noOfFuelStations"
                        data-cy="noOfFuelStations"
                        (change)="changeFuelCount()"
                        [(ngModel)]="permitRequest!.licenseRequest!.noOfFuelStations"
                        [ngModelOptions]="{ standalone: true }"
                        #noOfFuelStations="ngModel"
                      />
                      <div *ngIf="noOfFuelStations!.invalid && (noOfFuelStations!.dirty || noOfFuelStations!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="noOfFuelStations?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="noOfFuelStations?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.noOfControlTowers">No Of Control Towers</label>
                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="noOfControlTowers"
                        id="noOfControlTowers"
                        data-cy="noOfControlTowers"
                        [(ngModel)]="permitRequest!.licenseRequest!.noOfControlTowers"
                        [ngModelOptions]="{ standalone: true }"
                        #noOfControlTowers="ngModel"
                      />
                      <div *ngIf="noOfControlTowers!.invalid && (noOfControlTowers!.dirty || noOfControlTowers!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="noOfControlTowers?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="noOfControlTowers?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.noOfMarinaFacilities"
                        >No Of Marina Facilities</label
                      >
                      <input
                        max="99999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="noOfMarinaFacilities"
                        id="noOfMarinaFacilities"
                        data-cy="noOfMarinaFacilities"
                        [(ngModel)]="permitRequest!.licenseRequest!.noOfMarinaFacilities"
                        [ngModelOptions]="{ standalone: true }"
                        #noOfMarinaFacilities="ngModel"
                      />
                      <div *ngIf="noOfMarinaFacilities!.invalid && (noOfMarinaFacilities!.dirty || noOfMarinaFacilities!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="noOfMarinaFacilities?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="noOfMarinaFacilities?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 99999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.boatsCapacity">Boats Capacity</label>
                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="boatsCapacity"
                        id="boatsCapacity"
                        data-cy="boatsCapacity"
                        [(ngModel)]="permitRequest!.licenseRequest!.boatsCapacity"
                        [ngModelOptions]="{ standalone: true }"
                        #boatsCapacity="ngModel"
                      />
                      <div *ngIf="boatsCapacity!.invalid && (boatsCapacity!.dirty || boatsCapacity!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="boatsCapacity?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="boatsCapacity?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.yachtsCapacity">Yachts Capacity</label>

                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="yachtsCapacity"
                        id="yachtsCapacity"
                        data-cy="yachtsCapacity"
                        [(ngModel)]="permitRequest!.licenseRequest!.yachtsCapacity"
                        [ngModelOptions]="{ standalone: true }"
                        #yachtsCapacity="ngModel"
                      />
                      <div *ngIf="yachtsCapacity!.invalid && (yachtsCapacity!.dirty || yachtsCapacity!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="yachtsCapacity?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="yachtsCapacity?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.otherMaritimeMediumCapacity"
                        >Other Maritime Medium Capacity</label
                      >
                      <input
                        max="9999"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="otherMaritimeMediumCapacity"
                        id="otherMaritimeMediumCapacity"
                        data-cy="otherMaritimeMediumCapacity"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfOtherMarineVessels"
                        [ngModelOptions]="{ standalone: true }"
                        #otherMaritimeMediumCapacity="ngModel"
                      />
                      <div
                        *ngIf="
                          otherMaritimeMediumCapacity!.invalid &&
                          (otherMaritimeMediumCapacity!.dirty || otherMaritimeMediumCapacity!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="otherMaritimeMediumCapacity?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="otherMaritimeMediumCapacity?.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 9999 } }}
                        </small>
                      </div>
                    </div>
                    <div
                      class="col-md-6 col-lg-4"
                      *ngIf="permitRequest!.licenseRequest!.noOfFuelStations && permitRequest!.licenseRequest!.noOfFuelStations >= 1"
                    >
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.fuelTypes">الوقود المستخدم</label>

                      <div *ngIf="fuelTypes && fuelTypes.length > 0">
                        <p-multiSelect
                          [options]="fuelTypes"
                          optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                          display="chip"
                          appendTo="body"
                          dataKey="id"
                          [(ngModel)]="selectedFuelTypes"
                          [ngModelOptions]="{ standalone: true }"
                          required
                          #fuelType="ngModel"
                        >
                        </p-multiSelect>
                        <div *ngIf="fuelType!.invalid && (fuelType!.dirty || fuelType!.touched)">
                          <small class="form-text text-danger" *ngIf="fuelType?.errors?.required" jhiTranslate="entity.validation.required">
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                      <!--                      <select-->
                      <!--                        multiple-->
                      <!--                        class="form-select"-->
                      <!--                        id="selectedFuelTypes"-->
                      <!--                        data-cy="selectedFuelTypes"-->
                      <!--                        dataKey="id"-->
                      <!--                        [(ngModel)]="selectedFuelTypes"-->
                      <!--                        [ngModelOptions]="{ standalone: true }"-->
                      <!--                        required-->
                      <!--                        #fuelType="ngModel"-->
                      <!--                      >-->
                      <!--                        <option [ngValue]="null"></option>-->
                      <!--                        <option [ngValue]="fuel" *ngFor="let fuel of fuelTypes">-->
                      <!--                          {{ isArabic() ? fuel.nameAr : fuel.nameEn }}-->
                      <!--                        </option>-->
                      <!--                      </select>-->
                    </div>
                    <div class="col-md-6 col-lg-4">
                      <label class="form-label" jhiTranslate="srsaApp.permitRequest.marineTourOperatorServices"
                        >Marine Operator Services</label
                      >
                      <div *ngIf="marineTourOperatorServices && marineTourOperatorServices.length > 0">
                        <p-multiSelect
                          [options]="marineTourOperatorServices"
                          optionLabel="{{ isArabic() ? 'nameAr' : 'nameEn' }}"
                          display="chip"
                          appendTo="body"
                          dataKey="id"
                          [(ngModel)]="selectedMarineTourOperatorServices"
                          [ngModelOptions]="{ standalone: true }"
                        >
                        </p-multiSelect>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-6">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.marinaCrNameEn"
                        >(الإنجليزي) الاسم التجاري للمارينا</label
                      >

                      <input
                        required
                        type="text"
                        class="form-control"
                        name="marinaCrNameEn"
                        id="marinaCrNameEn"
                        pattern="^[a-zA-Z 0-9]*$"
                        data-cy="marinaCrNameEn"
                        [(ngModel)]="permitRequest!.licenseRequest!.marinaCrNameEn"
                        [ngModelOptions]="{ standalone: true }"
                        #marinaCrNameEn="ngModel"
                        minlength="2"
                        maxlength="20"
                      />
                      <div *ngIf="marinaCrNameEn!.invalid && (marinaCrNameEn!.dirty || marinaCrNameEn!.touched)">
                        <small class="form-text text-danger" [hidden]="!marinaCrNameEn!.errors?.pattern">
                          {{ 'entity.validation.patternEN' | translate }}
                        </small>

                        <small
                          class="form-text text-danger"
                          *ngIf="marinaCrNameEn?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="col-md-6 col-lg-6">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.numberOfDocks">number Of Docks</label>
                      <input
                        max="10"
                        min="1"
                        required
                        type="number"
                        class="form-control"
                        name="numberOfDocks"
                        id="numberOfDocks"
                        data-cy="numberOfDocks"
                        [(ngModel)]="permitRequest!.licenseRequest!.numberOfDocks"
                        (ngModelChange)="docksCount($event)"
                        [ngModelOptions]="{ standalone: true }"
                        #numberOfDocks="ngModel"
                      />
                      <div *ngIf="numberOfDocks!.invalid && (numberOfDocks!.dirty || numberOfDocks!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="numberOfDocks?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                        <small class="form-text text-danger" *ngIf="numberOfDocks!.errors?.max">
                          {{ 'entity.validation.max' | translate: { max: 10 } }}
                        </small>
                      </div>
                    </div>
                  </div>
                  <div class="row g-4">
                    @if (permitRequest!.licenseRequest!.numberOfDocks && permitRequest!.licenseRequest!.numberOfDocks > 0) {
                      <div class="table-responsive p-0 mt-5 col-md-12">
                        <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                          <thead>
                            <tr>
                              <th scope="col" class="text-center required" jhiTranslate="srsaApp.permitRequest.dockLength"></th>
                              <th scope="col" class="text-center required" jhiTranslate="srsaApp.permitRequest.dockDepth"></th>
                              <th scope="col" class="text-center required" jhiTranslate="srsaApp.permitRequest.dockWidth"></th>
                              <th scope="col" class="text-center required" jhiTranslate="srsaApp.permitRequest.noOfWaterStations"></th>
                              <th
                                scope="col"
                                class="text-center required"
                                jhiTranslate="srsaApp.permitRequest.noOfElectricChargingStations"
                              ></th>
                              <th scope="col" class="text-center required" jhiTranslate="srsaApp.permitRequest.noOfWasteStations"></th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let dock of Docks">
                              <td>
                                <input
                                  max="9999999"
                                  min="1"
                                  required
                                  type="number"
                                  class="form-control"
                                  name="dockLength"
                                  id="dockLength"
                                  data-cy="dockLength"
                                  [(ngModel)]="dock.dockLength"
                                  (ngModelChange)="checkDocks()"
                                  [ngModelOptions]="{ standalone: true }"
                                  #dockLength="ngModel"
                                  [placeholder]="'CM'"
                                />
                                <div *ngIf="dockLength!.invalid && (dockLength!.dirty || dockLength!.touched)">
                                  <small
                                    class="form-text text-danger"
                                    *ngIf="dockLength?.errors?.required"
                                    jhiTranslate="entity.validation.required"
                                  >
                                    هذا الحقل مطلوب
                                  </small>
                                  <small class="form-text text-danger" *ngIf="dockLength?.errors?.max">
                                    {{ 'entity.validation.max' | translate: { max: 9999999 } }}
                                  </small>
                                  <small class="form-text text-danger" *ngIf="dockLength?.errors?.min">
                                    {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                                  </small>
                                </div>
                              </td>
                              <td>
                                <input
                                  max="99999"
                                  min="1"
                                  required
                                  type="number"
                                  class="form-control"
                                  name="dockDepth"
                                  id="dockDepth"
                                  data-cy="dockDepth"
                                  [(ngModel)]="dock.dockDepth"
                                  (ngModelChange)="checkDocks()"
                                  [ngModelOptions]="{ standalone: true }"
                                  #dockDepth="ngModel"
                                  [placeholder]="'CM'"
                                />
                                <div *ngIf="dockDepth!.invalid && (dockDepth!.dirty || dockDepth!.touched)">
                                  <small
                                    class="form-text text-danger"
                                    *ngIf="dockDepth?.errors?.required"
                                    jhiTranslate="entity.validation.required"
                                  >
                                    هذا الحقل مطلوب
                                  </small>
                                  <small class="form-text text-danger" *ngIf="dockDepth?.errors?.max">
                                    {{ 'entity.validation.max' | translate: { max: 99999 } }}
                                  </small>
                                  <small class="form-text text-danger" *ngIf="dockDepth?.errors?.min">
                                    {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                                  </small>
                                </div>
                              </td>

                              <td class="text-center">
                                <input
                                  max="99999"
                                  min="1"
                                  required
                                  type="number"
                                  class="form-control"
                                  name="dockWidth"
                                  id="dockWidth"
                                  data-cy="dockWidth"
                                  [(ngModel)]="dock.dockWidth"
                                  (ngModelChange)="checkDocks()"
                                  [ngModelOptions]="{ standalone: true }"
                                  #dockWidth="ngModel"
                                />
                                <div *ngIf="dockWidth!.invalid && (dockWidth!.dirty || dockWidth!.touched)">
                                  <small
                                    class="form-text text-danger"
                                    *ngIf="dockWidth?.errors?.required"
                                    jhiTranslate="entity.validation.required"
                                  >
                                    هذا الحقل مطلوب
                                  </small>
                                  <small class="form-text text-danger" *ngIf="dockWidth?.errors?.max">
                                    {{ 'entity.validation.max' | translate: { max: 99999 } }}
                                  </small>
                                  <small class="form-text text-danger" *ngIf="dockWidth?.errors?.min">
                                    {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                                  </small>
                                </div>
                              </td>
                              <td class="text-center">
                                <input
                                  max="9999"
                                  min="1"
                                  required
                                  type="number"
                                  class="form-control"
                                  name="noOfWaterStations"
                                  id="noOfWaterStations"
                                  data-cy="noOfWaterStations"
                                  [(ngModel)]="dock.noOfWaterStations"
                                  (ngModelChange)="checkDocks()"
                                  [ngModelOptions]="{ standalone: true }"
                                  #noOfWaterStations="ngModel"
                                />
                                <div *ngIf="noOfWaterStations!.invalid && (noOfWaterStations!.dirty || noOfWaterStations!.touched)">
                                  <small
                                    class="form-text text-danger"
                                    *ngIf="noOfWaterStations?.errors?.required"
                                    jhiTranslate="entity.validation.required"
                                  >
                                    هذا الحقل مطلوب
                                  </small>
                                  <small class="form-text text-danger" *ngIf="noOfWaterStations?.errors?.max">
                                    {{ 'entity.validation.max' | translate: { max: 9999 } }}
                                  </small>
                                  <small class="form-text text-danger" *ngIf="noOfWaterStations?.errors?.min">
                                    {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                                  </small>
                                </div>
                              </td>
                              <td>
                                <input
                                  max="9999"
                                  min="1"
                                  required
                                  type="number"
                                  class="form-control"
                                  name="noOfElectricChargingStations"
                                  id="noOfElectricChargingStations"
                                  data-cy="noOfElectricChargingStations"
                                  [(ngModel)]="dock.noOfElectricChargingStations"
                                  (ngModelChange)="checkDocks()"
                                  [ngModelOptions]="{ standalone: true }"
                                  #noOfElectricChargingStations="ngModel"
                                />
                                <div
                                  *ngIf="
                                    noOfElectricChargingStations!.invalid &&
                                    (noOfElectricChargingStations!.dirty || noOfElectricChargingStations!.touched)
                                  "
                                >
                                  <small
                                    class="form-text text-danger"
                                    *ngIf="noOfElectricChargingStations?.errors?.required"
                                    jhiTranslate="entity.validation.required"
                                  >
                                    هذا الحقل مطلوب
                                  </small>
                                  <small class="form-text text-danger" *ngIf="noOfElectricChargingStations?.errors?.max">
                                    {{ 'entity.validation.max' | translate: { max: 9999 } }}
                                  </small>
                                  <small class="form-text text-danger" *ngIf="noOfElectricChargingStations?.errors?.min">
                                    {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                                  </small>
                                </div>
                              </td>
                              <td>
                                <input
                                  max="9999"
                                  min="1"
                                  required
                                  type="number"
                                  class="form-control"
                                  name="noOfWasteStations"
                                  id="noOfWasteStations"
                                  data-cy="noOfWasteStations"
                                  [(ngModel)]="dock.noOfWasteStations"
                                  (ngModelChange)="checkDocks()"
                                  [ngModelOptions]="{ standalone: true }"
                                  #noOfWasteStations="ngModel"
                                />
                                <div *ngIf="noOfWasteStations!.invalid && (noOfWasteStations!.dirty || noOfWasteStations!.touched)">
                                  <small
                                    class="form-text text-danger"
                                    *ngIf="noOfWasteStations?.errors?.required"
                                    jhiTranslate="entity.validation.required"
                                  >
                                    هذا الحقل مطلوب
                                  </small>
                                  <small class="form-text text-danger" *ngIf="noOfWasteStations?.errors?.max">
                                    {{ 'entity.validation.max' | translate: { max: 9999 } }}
                                  </small>
                                  <small class="form-text text-danger" *ngIf="noOfWasteStations?.errors?.min">
                                    {{ 'entity.validation.minNumber' | translate: { min: 1 } }}
                                  </small>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    }
                  </div>
                </section>
              </div>
              <div [hidden]="currentStep !== 5">
                <div class="card-custome-2">
                  <div class="p-4">
                    <div class="row">
                      <div class="col-lg-6 mk-attachments-list d-flex flex-column">
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" checked name="options-base" class="btn-check" id="btnCheck1" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck1"
                              jhiTranslate="srsaApp.permitRequest.cR"
                              (click)="clickToShow1()"
                            >
                            </label>
                          </div>
                        </div>
                        <div *ngIf="selectedSubCompany && selectedServiceType?.code !== 'BO'">
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck2" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck2"
                              jhiTranslate="srsaApp.permitRequest.MainCr"
                              (click)="clickToShow2()"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType?.code !== 'BO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck3" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck3"
                              (click)="clickToShow3()"
                              jhiTranslate="srsaApp.permitRequest.nationalAddress"
                            >
                            </label>
                          </div>
                        </div>
                        <div *ngIf="false">
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck4" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck4"
                              jhiTranslate="srsaApp.permitRequest.workforceMinistryHR"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <!--                        Investment License Mandatory if investment type is foreign or joint .-->
                          <div class="mk-attachments-item" *ngIf="invTypeId !== 0 && invTypeId !== 1">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck5" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck5"
                              (click)="clickToShow5()"
                              jhiTranslate="srsaApp.permitRequest.investmentLicense"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType!.code === 'MA'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck6" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck6"
                              (click)="clickToShow6()"
                              jhiTranslate="srsaApp.permitRequest.BankGuarantee"
                            ></label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="false">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck7" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck7"
                              (click)="clickToShow7()"
                              jhiTranslate="srsaApp.permitRequest.zakatLicense"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType!.code === 'YC'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck8" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck8"
                              (click)="clickToShow8()"
                              jhiTranslate="srsaApp.permitRequest.InternationalSafetyManagement"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType!.code === 'TO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck9" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck9"
                              (click)="clickToShow9()"
                              jhiTranslate="srsaApp.permitRequest.COMPANY_FILE"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="false">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck10" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck10"
                              (click)="clickToShow10()"
                              jhiTranslate="srsaApp.permitRequest.ListMarineOwned"
                            ></label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType?.code !== 'BO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck11" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck11"
                              (click)="clickToShow11()"
                              jhiTranslate="srsaApp.permitRequest.AdditionalAttachment"
                            >
                            </label>
                          </div>
                        </div>
                        <div
                          *ngIf="(!haveMaritimeTourismAgent && selectedServiceType!.code === 'YC') || selectedServiceType!.code === 'CO'"
                        >
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck12" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck12"
                              (click)="clickToShow12()"
                              jhiTranslate="srsaApp.permitRequest.ContractAuthority"
                              [title]="'srsaApp.permitRequest.ContractAuthorityTooltip' | translate"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType!.code === 'MO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck13" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck13"
                              (click)="clickToShow13()"
                              jhiTranslate="srsaApp.permitRequest.CONSTRUCTION_PERMIT"
                            ></label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType!.code === 'MO' || selectedServiceType!.code === 'BO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck14" autocomplete="off" />
                            <label
                              *ngIf="selectedServiceType!.code !== 'BO'"
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck14"
                              (click)="clickToShow14()"
                              jhiTranslate="srsaApp.permitRequest.OperatingContract"
                            >
                            </label>

                            <label
                              *ngIf="selectedServiceType!.code === 'BO'"
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck14"
                              (click)="clickToShow14()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.OperatingContract"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType!.code === 'MO' || selectedServiceType!.code === 'BO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck15" autocomplete="off" />
                            <label
                              *ngIf="selectedServiceType!.code !== 'BO'"
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck15"
                              (click)="clickToShow15()"
                              jhiTranslate="srsaApp.permitRequest.InsuranceCoverage"
                            >
                            </label>
                            <label
                              *ngIf="selectedServiceType!.code === 'BO'"
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck15"
                              (click)="clickToShow15()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.InsuranceCoverage"
                            >
                            </label>
                          </div>
                        </div>
                        <!--                      required only if service is of type "Marina Operator License"-->
                        <div *ngIf="selectedServiceType!.code === 'MO'">
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck16" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck16"
                              (click)="clickToShow16()"
                              jhiTranslate="srsaApp.permitRequest.ENVIRONMENTAL_LICENSE"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item" *ngIf="selectedServiceType?.code !== 'BO'">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck19" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck19"
                              (click)="clickToShowCyberSecurityCertificateAttachment()"
                              jhiTranslate="srsaApp.permitRequest.CYBER_SECURITY_CERTIFICATE"
                            >
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck20" autocomplete="off" />
                            <label
                              *ngIf="selectedServiceType!.code !== 'BO'"
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck20"
                              (click)="clickToShowCompanyProfileAttachment()"
                              jhiTranslate="srsaApp.permitRequest.COMPANY_PROFILE"
                            >
                            </label>
                            <label
                              *ngIf="selectedServiceType!.code === 'BO'"
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck20"
                              (click)="clickToShowCompanyProfileAttachment()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.COMPANY_PROFILE"
                            >
                            </label>
                          </div>
                        </div>
                        <div *ngIf="selectedServiceType!.code === 'MO' || selectedServiceType!.code === 'YC'">
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck17" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck17"
                              (click)="clickToShowProofOfOwnershipOfTheMarinaAttachment()"
                              jhiTranslate="srsaApp.permitRequest.PROOF_OF_OWNERSHIP_OF_THE_MARINA"
                            >
                            </label>
                          </div>
                        </div>
                        <div *ngIf="selectedServiceType!.code === 'MO' || selectedServiceType!.code === 'YC'">
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck18" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck18"
                              (click)="clickToShowIdentityOfTheOwnerOfTheMarinaAttachment()"
                              jhiTranslate="srsaApp.permitRequest.IDENTITY_OF_THE_OWNER_OF_THE_MARINA"
                            >
                            </label>
                          </div>
                        </div>
                        <div *ngIf="selectedServiceType!.code === 'BO'">
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck30" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck30"
                              (click)="clickToShowCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck21" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck21"
                              (click)="clickToShowSiteVerification()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.SITE_VERIFICATION"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck22" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck22"
                              (click)="clickToShowOccupancyCertificate()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.OCCUPANCY_CERTIFICATE"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck23" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck23"
                              (click)="clickToShowMaritimeSpacePlanning()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.MARITIME_SPACE_PLANNING"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck24" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck24"
                              (click)="clickToShowLifeguardList()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.LIFEGUARDS_LIST"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck25" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck25"
                              (click)="clickToShowEnvironmentalPermit()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.ENVIRONMENTAL_PERMIT"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck26" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck26"
                              (click)="clickToShowSafetyManagementPlan()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.SAFETY_MANAGEMENT_PLAN"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck27" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck27"
                              (click)="clickToShowWaterSafetyEquipment()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.WATER_SAFETY_EQUIPMENT"
                            >
                            </label>
                          </div>

                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck28" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck28"
                              (click)="clickToShowMarineVehiclesAndActivities()"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.MARINE_VEHICLES_AND_ACTIVITIES"
                            >
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-6 mk-custom-dropzone">
                        <div>
                          <dropzone
                            [hidden]="!hidden1"
                            id="CR"
                            [config]="getFileConfig()"
                            (addedFile)="onUploadFileSuccess($event, 'CR')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'CR')"
                          >
                          </dropzone>

                          <a
                            *ngIf="permitRequest?.id && CrAttachment && hidden1"
                            (click)="getAttachment(CrAttachment!.id)"
                            id="cr1"
                            alt="File"
                            >{{ CrAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="MainCR"
                            [config]="getFileConfig()"
                            [hidden]="!hidden2"
                            (addedFile)="onUploadFileSuccess($event, 'MainCR')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'MainCR')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && MainCRAttachment && hidden2"
                            (click)="getAttachment(MainCRAttachment!.id)"
                            id="MainCR1"
                            alt="File"
                            >{{ MainCRAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="CompanyNationalAddress"
                            [config]="getFileConfig()"
                            [hidden]="!hidden3"
                            (addedFile)="onUploadFileSuccess($event, 'CompanyNationalAddress')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'CompanyNationalAddress')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && CompanyNationalAddressAttachment && hidden3"
                            (click)="getAttachment(CompanyNationalAddressAttachment!.id)"
                            id="CompanyNationalAddress1"
                            alt="File"
                            >{{ CompanyNationalAddressAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="CopyWorkforceMinistryHR"
                            [config]="getFileConfig()"
                            [hidden]="!hidden4"
                            (addedFile)="onUploadFileSuccess($event, 'CopyWorkforceMinistryHR')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'CopyWorkforceMinistryHR')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && CopyWorkforceMinistryHRAttachment && hidden4"
                            (click)="getAttachment(CopyWorkforceMinistryHRAttachment!.id)"
                            id="CopyWorkforceMinistryHR1"
                            alt="File"
                            >{{ CopyWorkforceMinistryHRAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="InvestmentLicense"
                            [config]="getFileConfig()"
                            [hidden]="!hidden5"
                            (addedFile)="onUploadFileSuccess($event, 'InvestmentLicense')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'InvestmentLicense')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && InvestmentLicenseAttachment && hidden5"
                            (click)="getAttachment(InvestmentLicenseAttachment!.id)"
                            id="InvestmentLicense1"
                            alt="File"
                            >{{ InvestmentLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="BankGuarantee"
                            [config]="getFileConfig()"
                            [hidden]="!hidden6"
                            (addedFile)="onUploadFileSuccess($event, 'BankGuarantee')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'BankGuarantee')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && BankGuaranteeAttachment && hidden6"
                            (click)="getAttachment(BankGuaranteeAttachment!.id)"
                            id="BankGuarantee1"
                            alt="File"
                            >{{ BankGuaranteeAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="ZakatLicense"
                            [config]="getFileConfig()"
                            [hidden]="!hidden7"
                            (addedFile)="onUploadFileSuccess($event, 'ZakatLicense')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'ZakatLicense')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && ZakatLicenseAttachment && hidden7"
                            (click)="getAttachment(ZakatLicenseAttachment!.id)"
                            id="ZakatLicense1"
                            alt="File"
                            >{{ ZakatLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="InternationalSafetyManagement"
                            [config]="getFileConfig()"
                            [hidden]="!hidden8"
                            (addedFile)="onUploadFileSuccess($event, 'InternationalSafetyManagement')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'InternationalSafetyManagement')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && InternationalSafetyManagementAttachment && hidden8"
                            (click)="getAttachment(InternationalSafetyManagementAttachment!.id)"
                            id="InternationalSafetyManagement1"
                            alt="File"
                            >{{ InternationalSafetyManagementAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="COMPANY_FILE"
                            [config]="getFileConfig()"
                            [hidden]="!hidden9"
                            (addedFile)="onUploadFileSuccess($event, 'COMPANY_FILE')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'COMPANY_FILE')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && CompanyFileAttachment && hidden9"
                            (click)="getAttachment(CompanyFileAttachment!.id)"
                            id="COMPANY_FILE1"
                            alt="File"
                            >{{ CompanyFileAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="ListMarineOwned"
                            [config]="getFileConfig()"
                            [hidden]="!hidden10"
                            (addedFile)="onUploadFileSuccess($event, 'ListMarineOwned')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'ListMarineOwned')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && ListMarineOwnedAttachment && hidden10"
                            (click)="getAttachment(ListMarineOwnedAttachment!.id)"
                            id="ListMarineOwned1"
                            alt="File"
                            >{{ ListMarineOwnedAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="AdditionalAttachment"
                            [config]="getOtherFilesConfig()"
                            [hidden]="!hidden11"
                            (addedFile)="onUploadFileSuccess($event, 'AdditionalAttachment')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'AdditionalAttachment')"
                          >
                          </dropzone>
                          <div *ngIf="permitRequest?.id && AdditionalAttachment[0] && hidden11">
                            <div *ngFor="let attachment of AdditionalAttachment">
                              <a (click)="getAttachment(attachment.id)" id="AdditionalAttachment1" alt="File">{{ attachment!.docName }} </a>
                              <button type="button" (click)="deleteSavedFile(attachment)">Remove file</button>
                            </div>
                          </div>
                        </div>
                        <div>
                          <dropzone
                            required
                            id="ContractAuthority"
                            [config]="getFileConfig()"
                            [hidden]="!hidden12"
                            (addedFile)="onUploadFileSuccess($event, 'ContractAuthority')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'ContractAuthority')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && ContractAuthorityAttachment && hidden12"
                            (click)="getAttachment(ContractAuthorityAttachment!.id)"
                            id="ContractAuthority1"
                            alt="File"
                            >{{ ContractAuthorityAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="CONSTRUCTION_PERMIT"
                            [config]="getFileConfig()"
                            [hidden]="!hidden13"
                            (addedFile)="onUploadFileSuccess($event, 'CONSTRUCTION_PERMIT')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'CONSTRUCTION_PERMIT')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && ConstructionPermitAttachment && hidden13"
                            (click)="getAttachment(ConstructionPermitAttachment!.id)"
                            id="CONSTRUCTION_PERMIT1"
                            alt="File"
                            >{{ ConstructionPermitAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="OperatingContract"
                            [config]="getFileConfig()"
                            [hidden]="!hidden14"
                            (addedFile)="onUploadFileSuccess($event, 'OperatingContract')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'OperatingContract')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && OperatingContractAttachment && hidden14"
                            (click)="getAttachment(OperatingContractAttachment.id)"
                            id="OperatingContract1"
                            alt="File"
                            >{{ OperatingContractAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="InsuranceCoverage"
                            [config]="getFileConfig()"
                            [hidden]="!hidden15"
                            (addedFile)="onUploadFileSuccess($event, 'InsuranceCoverage')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'InsuranceCoverage')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && InsuranceCoverageAttachment && hidden15"
                            (click)="getAttachment(InsuranceCoverageAttachment!.id)"
                            id="InsuranceCoverage1"
                            alt="File"
                            >{{ InsuranceCoverageAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="ENVIRONMENTAL_LICENSE"
                            [config]="getFileConfig()"
                            [hidden]="!hidden16"
                            (addedFile)="onUploadFileSuccess($event, 'ENVIRONMENTAL_LICENSE')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'ENVIRONMENTAL_LICENSE')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && EnvironmentalLicenseAttachment && hidden16"
                            (click)="getAttachment(EnvironmentalLicenseAttachment!.id)"
                            id="ENVIRONMENTAL_LICENSE1"
                            alt="File"
                            >{{ EnvironmentalLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="COMPANY_PROFILE"
                            [config]="getFileConfig()"
                            [hidden]="!hiddeCompanyProfileAttachment"
                            (addedFile)="onUploadFileSuccess($event, 'COMPANY_PROFILE')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'COMPANY_PROFILE')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && CompanyProfileAttachment && hiddeCompanyProfileAttachment"
                            (click)="getAttachment(CompanyProfileAttachment!.id)"
                            id="COMPANY_PROFILE1"
                            alt="File"
                            >{{ CompanyProfileAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="CYBER_SECURITY_CERTIFICATE"
                            [config]="getFileConfig()"
                            [hidden]="!hiddeCyberSecurityCertificateAttachment"
                            (addedFile)="onUploadFileSuccess($event, 'CYBER_SECURITY_CERTIFICATE')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'CYBER_SECURITY_CERTIFICATE')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && CyberSecurityCertificateAttachment && hiddeCyberSecurityCertificateAttachment"
                            (click)="getAttachment(CyberSecurityCertificateAttachment!.id)"
                            id="CYBER_SECURITY_CERTIFICATE1"
                            alt="File"
                            >{{ CyberSecurityCertificateAttachment.docName! }}</a
                          >
                        </div>
                        <div *ngIf="selectedServiceType!.code == 'MO' || selectedServiceType!.code == 'YC'">
                          <dropzone
                            id="PROOF_OF_OWNERSHIP_OF_THE_MARINA"
                            [config]="getFileConfig()"
                            [hidden]="!hiddeProofOfOwnershipOfTheMarinaAttachment"
                            (addedFile)="onUploadFileSuccess($event, 'PROOF_OF_OWNERSHIP_OF_THE_MARINA')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'PROOF_OF_OWNERSHIP_OF_THE_MARINA')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && ProofOfOwnershipOfTheMarinaAttachment && hiddeProofOfOwnershipOfTheMarinaAttachment"
                            (click)="getAttachment(ProofOfOwnershipOfTheMarinaAttachment!.id)"
                            id="PROOF_OF_OWNERSHIP_OF_THE_MARINA1"
                            alt="File"
                            >{{ ProofOfOwnershipOfTheMarinaAttachment.docName! }}</a
                          >
                        </div>
                        <div *ngIf="selectedServiceType!.code == 'MO' || selectedServiceType!.code == 'YC'">
                          <dropzone
                            id="IDENTITY_OF_THE_OWNER_OF_THE_MARINA"
                            [config]="getFileConfig()"
                            [hidden]="!hiddeAIdentityOfTheOwnerOfTheMarinaAttachment"
                            (addedFile)="onUploadFileSuccess($event, 'IDENTITY_OF_THE_OWNER_OF_THE_MARINA')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'IDENTITY_OF_THE_OWNER_OF_THE_MARINA')"
                          >
                          </dropzone>
                          <a
                            *ngIf="
                              permitRequest?.id && AIdentityOfTheOwnerOfTheMarinaAttachment && hiddeAIdentityOfTheOwnerOfTheMarinaAttachment
                            "
                            (click)="getAttachment(AIdentityOfTheOwnerOfTheMarinaAttachment!.id)"
                            id="IDENTITY_OF_THE_OWNER_OF_THE_MARINA1"
                            alt="File"
                            >{{ AIdentityOfTheOwnerOfTheMarinaAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach"
                            (addedFile)="onUploadFileSuccess($event, 'COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH')"
                          >
                          </dropzone>
                          <a
                            *ngIf="
                              permitRequest?.id &&
                              CommercialRegistrationOrIdentityOfTheOwnerOfTheBeachAttachment &&
                              hiddenCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach
                            "
                            (click)="getAttachment(CommercialRegistrationOrIdentityOfTheOwnerOfTheBeachAttachment!.id)"
                            id="CommercialRegistrationOrIdentityOfTheOwnerOfTheBeachAttachment1"
                            alt="File"
                            >{{ CommercialRegistrationOrIdentityOfTheOwnerOfTheBeachAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="SITE_VERIFICATION"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenSiteVerification"
                            (addedFile)="onUploadFileSuccess($event, 'SITE_VERIFICATION')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'SITE_VERIFICATION')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && SiteVerificationAttachment && hiddenSiteVerification"
                            (click)="getAttachment(SiteVerificationAttachment!.id)"
                            id="SITE_VERIFICATION1"
                            alt="File"
                            >{{ SiteVerificationAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="OCCUPANCY_CERTIFICATE"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenOccupancyCertificate"
                            (addedFile)="onUploadFileSuccess($event, 'OCCUPANCY_CERTIFICATE')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'OCCUPANCY_CERTIFICATE')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && OccupancyCertificateAttachment && hiddenOccupancyCertificate"
                            (click)="getAttachment(OccupancyCertificateAttachment!.id)"
                            id="OCCUPANCY_CERTIFICATE1"
                            alt="File"
                            >{{ OccupancyCertificateAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="MARITIME_SPACE_PLANNING"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenMaritimeSpacePlanning"
                            (addedFile)="onUploadFileSuccess($event, 'MARITIME_SPACE_PLANNING')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'MARITIME_SPACE_PLANNING')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && MaritimeSpacePlanningAttachment && hiddenMaritimeSpacePlanning"
                            (click)="getAttachment(MaritimeSpacePlanningAttachment!.id)"
                            id="MARITIME_SPACE_PLANNING1"
                            alt="File"
                            >{{ MaritimeSpacePlanningAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="LIFEGUARDS_LIST"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenLifeguardList"
                            (addedFile)="onUploadFileSuccess($event, 'LIFEGUARDS_LIST')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'LIFEGUARDS_LIST')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && LifeguardListAttachment && hiddenLifeguardList"
                            (click)="getAttachment(LifeguardListAttachment!.id)"
                            id="LIFEGUARDS_LIST1"
                            alt="File"
                            >{{ LifeguardListAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="ENVIRONMENTAL_PERMIT"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenEnvironmentalPermit"
                            (addedFile)="onUploadFileSuccess($event, 'ENVIRONMENTAL_PERMIT')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'ENVIRONMENTAL_PERMIT')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && EnvironmentalPermitAttachment && hiddenEnvironmentalPermit"
                            (click)="getAttachment(EnvironmentalPermitAttachment!.id)"
                            id="ENVIRONMENTAL_PERMIT1"
                            alt="File"
                            >{{ EnvironmentalPermitAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="SAFETY_MANAGEMENT_PLAN"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenSafetyManagementPlan"
                            (addedFile)="onUploadFileSuccess($event, 'SAFETY_MANAGEMENT_PLAN')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'SAFETY_MANAGEMENT_PLAN')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && SafetyManagementPlanAttachment && hiddenSafetyManagementPlan"
                            (click)="getAttachment(SafetyManagementPlanAttachment!.id)"
                            id="SAFETY_MANAGEMENT_PLAN1"
                            alt="File"
                            >{{ SafetyManagementPlanAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="WATER_SAFETY_EQUIPMENT"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenWaterSafetyEquipment"
                            (addedFile)="onUploadFileSuccess($event, 'WATER_SAFETY_EQUIPMENT')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'WATER_SAFETY_EQUIPMENT')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && WaterSafetyEquipmentAttachment && hiddenWaterSafetyEquipment"
                            (click)="getAttachment(WaterSafetyEquipmentAttachment!.id)"
                            id="WATER_SAFETY_EQUIPMENT1"
                            alt="File"
                            >{{ WaterSafetyEquipmentAttachment.docName! }}</a
                          >
                        </div>

                        <div>
                          <dropzone
                            id="MARINE_VEHICLES_AND_ACTIVITIES"
                            [config]="getFileConfig()"
                            [hidden]="!hiddenMarineVehiclesAndActivities"
                            (addedFile)="onUploadFileSuccess($event, 'MARINE_VEHICLES_AND_ACTIVITIES')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'MARINE_VEHICLES_AND_ACTIVITIES')"
                          >
                          </dropzone>
                          <a
                            *ngIf="permitRequest?.id && MarineVehiclesAndActivitiesAttachment && hiddenMarineVehiclesAndActivities"
                            (click)="getAttachment(MarineVehiclesAndActivitiesAttachment!.id)"
                            id="MARINE_VEHICLES_AND_ACTIVITIES1"
                            alt="File"
                            >{{ MarineVehiclesAndActivitiesAttachment.docName! }}</a
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="currentStep === 6">
                <!-- Accordion -->
                <div class="accordion mk-review-step" id="accordionExample">
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img src="../../../../content/images/request-view/ph_building-bold.svg" alt="ph_building-bold." class="mx-4 me-3" />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseOne"
                        aria-expanded="true"
                        aria-controls="collapseOne"
                        jhiTranslate="srsaApp.permitRequest.establishmentData"
                      >
                        بيانات المنشأة
                      </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <jhi-company-details-tables [crNumber]="registeredCompany?.crNumber || ''"> </jhi-company-details-tables>
                        <!--                        <p-->
                        <!--                          id="jhi-permitRq4"-->
                        <!--                          data-cy="PermitRequestCreateUpdateHeading"-->
                        <!--                          class="accordion-subtitle"-->
                        <!--                          jhiTranslate="srsaApp.permitRequest.cRDetail"-->
                        <!--                        >-->
                        <!--                          العنوان الوطني-->
                        <!--                        </p>-->
                        <!--                        <hr />-->
                        <!--                        <div class="jhi-row g-2 mb-5">-->
                        <!--                          <div class="item">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.establishmentName">CR</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.crName }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.crEntityNumber">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.cr700Number">الحي</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.crEntityNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.cR">الرمز البريدي</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.crNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.crIssueDate">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.issueDate">رقم المبني</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.crIssueDate }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.crExpiryDate">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.expiryDate">المدينة</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.crExpiryDate }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.companyStatus">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.detail.crStatus">حالة السجل التجاري</label>-->
                        <!--                            <p-->
                        <!--                              class="item-value"-->
                        <!--                              [ngClass]="{ class_one: step1 === true, class_two: step2 === true, class_three: step3 === true }"-->
                        <!--                            >-->
                        <!--                              {{ registeredCompany!.companyStatus }}-->
                        <!--                            </p>-->
                        <!--                          </div>-->
                        <!--                        </div>-->
                        <!--                        <div class="jhi-row g-2 mb-5">-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.moiNumber">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.moiNumber">الحي</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.moiNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          &lt;!&ndash;                          <div class="item" *ngIf="registeredCompany!.crCapital">&ndash;&gt;-->
                        <!--                          &lt;!&ndash;                            <label class="form-label" jhiTranslate="srsaApp.company.capital">الحي</label>&ndash;&gt;-->
                        <!--                          &lt;!&ndash;                            <p class="item-value">{{ registeredCompany!.crCapital }}</p>&ndash;&gt;-->
                        <!--                          &lt;!&ndash;                          </div>&ndash;&gt;-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.businessType?.name">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.entity">الحي</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.businessType!.name }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.mobileNumber">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.mobileNumber">الحي</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.mobileNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="registeredCompany!.crActivities">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.activityName">الحي</label>-->
                        <!--                            <p class="item-value">{{ registeredCompany!.crActivities }}</p>-->
                        <!--                          </div>-->
                        <!--                        </div>-->
                      </div>
                      <div class="accordion-body mx-2rem" *ngIf="selectedSubCompany">
                        <p
                          id="jhi-permitRq4"
                          data-cy="PermitRequestCreateUpdateHeading"
                          class="accordion-subtitle"
                          jhiTranslate="srsaApp.company.subCR"
                        >
                          subCR
                        </p>
                        <hr />
                        <div class="row mt-4" *ngIf="subCompany">
                          <div class="col-md-6 p-4" *ngFor="let field of subCompanyDetailsFields">
                            <strong>{{ field.label }}:</strong>
                            <span>{{ field.value }}</span>
                          </div>
                        </div>

                        <!--                        <div class="jhi-row g-2 mb-5">-->
                        <!--                          <div class="item">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.establishmentName">CR</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.crName }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.crEntityNumber">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.cr700Number">الحي</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.crEntityNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.cR">الرمز البريدي</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.crNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.crIssueDate">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.issueDate">رقم المبني</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.crIssueDate }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.crExpiryDate">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.expiryDate">المدينة</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.crExpiryDate }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.companyStatus">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.detail.crStatus">حالة السجل التجاري</label>-->
                        <!--                            <p-->
                        <!--                              class="item-value"-->
                        <!--                              [ngClass]="{ class_one: step1 === true, class_two: step2 === true, class_three: step3 === true }"-->
                        <!--                            >-->
                        <!--                              {{ selectedSubCompany!.companyStatus }}-->
                        <!--                            </p>-->
                        <!--                          </div>-->
                        <!--                        </div>-->
                        <!--                        <div class="jhi-row g-2 mb-5">-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.moiNumber">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.moiNumber">الحي</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.moiNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          &lt;!&ndash;                          <div class="item" *ngIf="registeredCompany!.crCapital">&ndash;&gt;-->
                        <!--                          &lt;!&ndash;                            <label class="form-label" jhiTranslate="srsaApp.company.capital">الحي</label>&ndash;&gt;-->
                        <!--                          &lt;!&ndash;                            <p class="item-value">{{ registeredCompany!.crCapital }}</p>&ndash;&gt;-->
                        <!--                          &lt;!&ndash;                          </div>&ndash;&gt;-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.businessType?.name">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.entity">الحي</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.businessType!.name }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.mobileNumber">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.mobileNumber">الحي</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.mobileNumber }}</p>-->
                        <!--                          </div>-->
                        <!--                          <div class="item" *ngIf="selectedSubCompany!.crActivities">-->
                        <!--                            <label class="form-label" jhiTranslate="srsaApp.company.activityName">الحي</label>-->
                        <!--                            <p class="item-value">{{ selectedSubCompany!.crActivities }}</p>-->
                        <!--                          </div>-->
                        <!--                        </div>-->
                      </div>

                      <div class="accordion-body mx-2rem" *ngIf="invTypeId !== 0">
                        <p
                          id="jhi-permitRq4"
                          data-cy="PermitRequestCreateUpdateHeading"
                          class="accordion-subtitle"
                          jhiTranslate="srsaApp.permitRequest.investmentLicense"
                        >
                          العنوان الوطني
                        </p>
                        <hr />
                        <div class="jhi-row g-2 mb-5">
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.investmentType">CR</label>
                            <p class="item-value">{{ registeredCompany!.crNumber }}</p>
                          </div>
                          <div class="item" *ngIf="selectedLicenseProvider">
                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensingAuthority">الحي</label>
                            <p class="item-value">{{ isArabic() ? selectedLicenseProvider.nameAr : selectedLicenseProvider.nameEn }}</p>
                          </div>
                          <div class="item" *ngIf="selectedLicenseProvider?.code == 'ECZA'">
                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.id">الرمز البريدي</label>
                            <p class="item-value">{{ editForm.get('eczaLicenseNumber')!.value }}</p>
                          </div>
                          <div class="item" *ngIf="selectedLicenseProvider?.code == 'MISA'">
                            <label class="form-label" jhiTranslate="srsaApp.permitRequest.id">الرمز البريدي</label>
                            <p class="item-value">{{ editForm.get('misaLicenseNumber')!.value }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img src="../../../../content/images/Group.png" alt="Group.png" class="mx-4 me-3" />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseTwo"
                        aria-expanded="false"
                        aria-controls="collapseTwo"
                        jhiTranslate="srsaApp.nationalAddress.detail.nationalAddressData"
                      >
                        بيانات العنوان الوطني
                      </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <p
                          id="jhi-permitRq4"
                          data-cy="PermitRequestCreateUpdateHeading"
                          class="accordion-subtitle"
                          jhiTranslate="srsaApp.nationalAddress.detail.title"
                        >
                          العنوان الوطني
                        </p>
                        <hr />
                        <div class="jhi-row g-2 mb-5">
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.street">الشارع</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.streetName! }}</p>
                          </div>
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.district">الحي</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.district! }}</p>
                          </div>
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.zipCode">الرمز البريدي</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.zipcode! }}</p>
                          </div>
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.buildingNumber">رقم المبني</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.buildingNumber! }}</p>
                          </div>
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.city">المدينة</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.city! }}</p>
                          </div>
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.additionalNumber">الرقم الإضافي</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.additionalNumber! }}</p>
                          </div>
                          <div class="item">
                            <label class="form-label" jhiTranslate="srsaApp.nationalAddress.shortNumber">الرقم المختصر</label>
                            <p class="item-value">{{ registeredCompany!.nationalAddress!.additionalNumber! }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/fluent_card-ui-24-regular.svg"
                        alt="fluent_card-ui-24-regular.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseThree"
                        aria-expanded="false"
                        aria-controls="collapseThree"
                        jhiTranslate="srsaApp.permitRequest.permitData"
                      >
                        بيانات الرخصة
                      </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <div *ngIf="selectedMaritimeMediums && selectedMaritimeMediums.length > 0">
                          <p
                            id="jhi-permitRq4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.maritimeMediation"
                          >
                            الواسطات البحرية
                          </p>
                          <hr />
                          <div
                            class="row g-4 mb-5 custom-row"
                            [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestMaritimeMediums }"
                          >
                            @for (maritimeMedium of maritimeMediums; track trackMaritimeMedium) {
                              <div
                                class="col-md-6 col-lg-4"
                                *ngIf="
                                  ((maritimeMedium.code == 'CruiseShips' || maritimeMedium.code == 'Yachts') &&
                                    selectedServiceType!.code == 'MA') ||
                                  (maritimeMedium.code != 'CruiseShips' && selectedServiceType!.code == 'MO')
                                "
                              >
                                <input
                                  required
                                  type="checkbox"
                                  class="btn-check"
                                  id="btn-check-{{ maritimeMedium.id }}"
                                  autocomplete="off"
                                  [value]="maritimeMedium.id"
                                  [checked]="selectedMaritimeMediumsId!.indexOf(maritimeMedium!.id) > -1"
                                  (change)="updateCheckedMaritimeMediums(maritimeMedium, $event)"
                                  disabled
                                />
                                <label
                                  class="align-items-center btn d-flex flex-row fs-5 gap-3 new-custom py-4 text-primary background-selected"
                                  for="btn-check-{{ maritimeMedium.id }}"
                                >
                                  <img
                                    src="../../../../content/images/{{ maritimeMedium.imageName }}"
                                    alt="{{ maritimeMedium.imageName }}"
                                  />
                                  <span class="item-value text-start">{{
                                    isArabic() ? maritimeMedium.nameAr : maritimeMedium.nameEn
                                  }}</span>
                                </label>
                              </div>
                            }
                          </div>
                        </div>

                        <div>
                          <p
                            id="jhi-permitRq4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.servicesList"
                            *ngIf="selectedMaritimeTourismServices && selectedMaritimeTourismServices.length > 0"
                          >
                            قائمة الخدمات
                          </p>
                          <hr />
                          <div class="bd-example tooltip-demo mb-5">
                            <div class="bd-example-tooltips">
                              <div
                                class="form-check form-check-inline"
                                [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestMaritimeServices }"
                                *ngFor="let selectedMaritimeTourismService of selectedMaritimeTourismServices"
                              >
                                <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="" disabled checked />
                                <label class="form-check-label pt-1 ps-2" for="inlineCheckbox1">
                                  {{ isArabic() ? selectedMaritimeTourismService.nameAr : selectedMaritimeTourismService.nameEn }}</label
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- Beach Activities Preview -->
                        <div>
                          <p
                            id="jhi-beachActivities"
                            data-cy="BeachActivitiesPreviewHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.beachActivities"
                            *ngIf="selectedBeachActivities && selectedBeachActivities.length > 0"
                          >
                            قائمة أنشطة الشاطئ
                          </p>
                          <hr />
                          <div class="bd-example tooltip-demo mb-5">
                            <div class="bd-example-tooltips">
                              <div
                                class="form-check form-check-inline"
                                [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestBeachActivities }"
                                *ngFor="let selectedBeachActivity of selectedBeachActivities"
                              >
                                <input
                                  class="form-check-input"
                                  type="checkbox"
                                  id="beachActivity{{ selectedBeachActivity.id }}"
                                  value=""
                                  disabled
                                  checked
                                />
                                <label class="form-check-label pt-1 ps-2" for="beachActivity{{ selectedBeachActivity.id }}">
                                  {{ isArabic() ? selectedBeachActivity.nameAr : selectedBeachActivity.nameEn }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Beach Equipment Preview -->
                        <div>
                          <p
                            id="jhi-beachEquipment"
                            data-cy="BeachEquipmentPreviewHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.beachEquipment"
                            *ngIf="selectedBeachEquipments && selectedBeachEquipments.length > 0"
                          >
                            قائمة معدات الشاطئ
                          </p>
                          <hr />
                          <div class="bd-example tooltip-demo mb-5">
                            <div class="bd-example-tooltips">
                              <div
                                class="form-check form-check-inline"
                                [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestBeachEquipment }"
                                *ngFor="let selectedBeachEquipment of selectedBeachEquipments"
                              >
                                <input
                                  class="form-check-input"
                                  type="checkbox"
                                  id="beachEquipment{{ selectedBeachEquipment.id }}"
                                  value=""
                                  disabled
                                  checked
                                />
                                <label class="form-check-label pt-1 ps-2" for="beachEquipment{{ selectedBeachEquipment.id }}">
                                  {{ isArabic() ? selectedBeachEquipment.nameAr : selectedBeachEquipment.nameEn }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <p
                            id="jhi-permitRq4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.permitData"
                          >
                            بيانات الرخصة
                          </p>
                          <hr />
                          <div class="jhi-row mb-5">
                            <div class="col" *ngIf="selectedlicenseDurationValues">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensesDuration">مدة الترخيص المطلوبة</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.RequestedLicenseDuration }">
                                {{ 'srsaApp.permitRequest.licenseDuration.' + selectedlicenseDurationValues | translate }}
                              </p>
                            </div>
                            <div class="col" *ngIf="selectedProvidedLanguages">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.providedLanguages">الرمز البريدي</label>
                              <div
                                *ngFor="let language of selectedProvidedLanguages"
                                [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestProvidedLanguages }"
                              >
                                <p class="item-value">{{ isArabic() ? language.nameAr : language.nameEn }}</p>
                              </div>
                            </div>
                            <div class="col" *ngIf="permitRequest!.totalFundingOfProject">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.totalFundingOfProject">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.TotalFundingOfProject }">
                                {{ permitRequest!.totalFundingOfProject }}
                              </p>
                            </div>
                            <div class="col" *ngIf="permitRequest!.licenseRequest!.noOfYearsExperience">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.yearsOfExperience">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfYearsExperience }">
                                {{ permitRequest!.licenseRequest!.noOfYearsExperience }}
                              </p>
                            </div>
                            <!-- Previously Operated Beach -->
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="selectedServiceType!.code === 'BO'">
                              <label
                                class="form-label"
                                jhiTranslate="srsaApp.permitRequest.previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                                >هل سبق وان تم تشغيل شاطئ قبل ذلك داخل او خارج المملكة العربية السعودية</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.PreviouslyOperatedBeach }">
                                <span
                                  *ngIf="permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia === true"
                                  jhiTranslate="global.form.yes"
                                  >نعم</span
                                >
                                <span
                                  *ngIf="permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia === false"
                                  jhiTranslate="global.form.no"
                                  >لا</span
                                >
                              </p>
                            </div>

                            <div class="col" *ngIf="selectedCountries">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.operationCountries">رقم المبني</label>
                              <div
                                [ngClass]="{ newChangesLabel: permitChanges?.NoOfCountryCompanyOpr }"
                                *ngFor="let country of selectedCountries"
                              >
                                <p class="item-value">{{ isArabic() ? country.nameAr : country.nameEn }}</p>
                              </div>
                            </div>
                          </div>
                          <div class="jhi-row mb-5">
                            <div class="col" *ngIf="permitRequest!.licenseRequest!.noOfManagedMaritimeMediumsGeneral">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfManagedMaritimeMediumsGeneral"
                                >الرقم المختصر</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfManagedMaritimeMediumsGeneral }">
                                {{ permitRequest!.licenseRequest!.noOfManagedMaritimeMediumsGeneral }}
                              </p>
                            </div>
                            <div class="col" *ngIf="permitRequest!.licenseRequest!.noOfCountryCompanyOpr">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfCountryCompanyOpr">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfCountryCompanyOpr }">
                                {{ permitRequest!.licenseRequest!.noOfCountryCompanyOpr }}
                              </p>
                            </div>
                            <div
                              [style]="'max-width: fit-content;'"
                              class="col"
                              *ngIf="permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsWorld"
                            >
                              <label
                                class="form-label"
                                jhiTranslate="srsaApp.permitRequest.noOfOwnedMaritimeMediumsWorld"
                                [title]="'srsaApp.permitRequest.noOfOwnedMaritimeMediumsWorld' | translate"
                                >المدينة</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfOwnedMaritimeMediumsWorld }">
                                {{ permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsWorld }}
                              </p>
                            </div>
                            <div class="col" *ngIf="permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsKsa">
                              <label
                                class="form-label"
                                jhiTranslate="srsaApp.permitRequest.noOfOwnedMaritimeMediumsKsa"
                                [title]="'srsaApp.permitRequest.noOfOwnedMaritimeMediumsKsa' | translate"
                                >المدينة</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfOwnedMaritimeMediumsKsa }">
                                {{ permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsKsa }}
                              </p>
                            </div>
                            <div class="col" *ngIf="selectedMaritimeTourismAgent">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.maritimeTourAgent">الرقم الإضافي</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.MaritimeTourismAgent }">
                                {{ isArabic() ? selectedMaritimeTourismAgent.crName : selectedMaritimeTourismAgent.crName }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/ic_round-add-card.svg"
                        alt="ic_round-add-card.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseFour"
                        aria-expanded="false"
                        aria-controls="collapseFour"
                        jhiTranslate="srsaApp.permitRequest.extraData"
                      >
                        بيانات إضافية
                      </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <div>
                          <p
                            id="jhi-permitRq4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.workforceData"
                          >
                            بيانات القوى العاملة
                          </p>
                          <hr />
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('ministryOfHumanResourcesNumber')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.ministryOfHumanResourcesNumber">CR</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.MinistryOfHumanResourcesNumber }">
                                {{ editForm.get('ministryOfHumanResourcesNumber')!.value }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('saudiLaborCount')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.saudiLaborCount">الحي</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.SaudiLaborCount }">
                                {{ editForm.get('saudiLaborCount')!.value }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('foreignersLaborCount')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.foreignersLaborCount">الرمز البريدي</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.ForeignersLaborCount }">
                                {{ editForm.get('foreignersLaborCount')!.value }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('numberOfMaleEmployees')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfMaleEmployees">رقم المبني</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfMaleEmployees }">
                                {{ editForm.get('numberOfMaleEmployees')!.value }}
                              </p>
                            </div>
                          </div>
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('numberOfFemaleEmployees')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfFemaleEmployees">المدينة</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfFemaleEmployees }">
                                {{ editForm.get('numberOfFemaleEmployees')!.value }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('saudizationLevel')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.saudizationLevel">الرقم الإضافي</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.SaudizationLevel }">
                                {{ 'srsaApp.permitRequest.saudiLevel.' + editForm.get('saudizationLevel')!.value | translate }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('rateRange')!.value">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.rateRange">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.RateRange }">
                                {{ editForm.get('rateRange')!.value }}
                              </p>
                            </div>
                          </div>
                          <div>
                            <p
                              id="jhi-permitRq4"
                              data-cy="PermitRequestCreateUpdateHeading"
                              class="accordion-subtitle"
                              jhiTranslate="srsaApp.permitRequest.contactInformation"
                            >
                              العنوان الوطني
                            </p>
                            <hr />
                            <div class="row g-2 mb-5">
                              <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('contactOfficerName')!.value">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.contactOfficerName">الرقم المختصر</label>
                                <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.ContactOfficerName }">
                                  {{ editForm.get('contactOfficerName')!.value }}
                                </p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('contactOfficerMobileNumber')!.value">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.contactOfficerMobileNumber"
                                  >الرقم المختصر</label
                                >
                                <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.ContactOfficerMobileNumber }">
                                  {{ editForm.get('contactOfficerMobileNumber')!.value }}
                                </p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('contactOfficerEmailAddress')!.value">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.contactOfficerEmailAddress"
                                  >الرقم المختصر</label
                                >
                                <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.ContactOfficerEmailAddress }">
                                  {{ editForm.get('contactOfficerEmailAddress')!.value }}
                                </p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="editForm.get('vatRegistrationNumber')!.value">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.vatRegistrationNumber">الرقم المختصر</label>
                                <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.VatRegistrationNumber }">
                                  {{ editForm.get('vatRegistrationNumber')!.value }}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div *ngIf="selectedServiceType!.code == 'MO'">
                          <p
                            id="jhi-permitRq4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.extraData"
                          >
                            معلومات إضافية
                          </p>
                          <hr />
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaOwnerName">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.MarinaOwnerNameAr }">
                                {{ permitRequest!.licenseRequest!.marinaOwnerNameAr }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaCrNumber">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.MarinaCrNumber }">
                                {{ permitRequest!.licenseRequest!.marinaCrNumber }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaCrNameAr">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.MarinaCrNameAr }">
                                {{ permitRequest!.licenseRequest!.marinaCrNameAr }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaCrNameEn">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.MarinaCrNameEn }">
                                {{ permitRequest!.licenseRequest!.marinaCrNameEn }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfDocks">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfDocks }">
                                {{ permitRequest!.licenseRequest!.numberOfDocks }}
                              </p>
                            </div>
                            <div class="col-12 mt-3" *ngIf="Docks!.length > 0">
                              <div class="table-responsive" [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestDocks }">
                                <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                                  <thead>
                                    <tr class="bg-light">
                                      <th scope="col" class="text-center fw-medium" jhiTranslate="srsaApp.permitRequest.dockLength"></th>
                                      <th scope="col" class="text-center fw-medium" jhiTranslate="srsaApp.permitRequest.dockDepth"></th>
                                      <th scope="col" class="text-center fw-medium" jhiTranslate="srsaApp.permitRequest.dockWidth"></th>
                                      <th
                                        scope="col"
                                        class="text-center fw-medium"
                                        jhiTranslate="srsaApp.permitRequest.noOfWaterStations"
                                      ></th>
                                      <th
                                        scope="col"
                                        class="text-center fw-medium"
                                        jhiTranslate="srsaApp.permitRequest.noOfElectricChargingStations"
                                      ></th>
                                      <th
                                        scope="col"
                                        class="text-center fw-medium"
                                        jhiTranslate="srsaApp.permitRequest.noOfWasteStations"
                                      ></th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr class="text-center" *ngFor="let dock of Docks!">
                                      <td>{{ dock.dockLength }}</td>
                                      <td>{{ dock.dockDepth }}</td>
                                      <td>{{ dock.dockWidth }}</td>
                                      <td>{{ dock.noOfWaterStations }}</td>
                                      <td>{{ dock.noOfElectricChargingStations }}</td>
                                      <td>{{ dock.noOfWasteStations }}</td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfControlTowers">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfControlTowers }">
                                {{ permitRequest!.licenseRequest!.noOfControlTowers }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfMarinaFacilities">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfMarinaFacilities }">
                                {{ permitRequest!.licenseRequest!.noOfMarinaFacilities }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.boatsCapacity">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.BoatsCapacity }">
                                {{ permitRequest!.licenseRequest!.boatsCapacity }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.yachtsCapacity">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.YachtsCapacity }">
                                {{ permitRequest!.licenseRequest!.yachtsCapacity }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.otherMaritimeMediumCapacity"
                                >الرقم المختصر</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.OtherMaritimeMediumCapacity }">
                                {{ permitRequest!.licenseRequest!.numberOfOtherMarineVessels }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.marineTourOperatorServices"
                                >الرقم المختصر</label
                              >
                              <div *ngFor="let marineTourOperatorService of selectedMarineTourOperatorServices; let x = index">
                                <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestMarineTourServices }">
                                  {{ isArabic() ? marineTourOperatorService.nameAr : marineTourOperatorService.nameEn }}
                                </p>
                              </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="permitRequest!.licenseRequest!.noOfFuelStations">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfFuelStations">الرقم المختصر</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfFuelStations }">
                                {{ permitRequest!.licenseRequest!.noOfFuelStations }}
                              </p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="selectedFuelTypes">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.fuelTypes">الرقم المختصر</label>
                              <div *ngFor="let fuelType of selectedFuelTypes; let x = index">
                                <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestFuelTypes }">
                                  {{ isArabic() ? fuelType.nameAr : fuelType.nameEn }}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div *ngIf="selectedServiceType!.code == 'BO'">
                          <p
                            id="jhi-permitRq5"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.extraData"
                          >
                            معلومات إضافية
                          </p>
                          <hr />

                          <!-- Beach Operator Information Preview -->
                          <div class="row" *ngIf="selectedServiceType?.code === 'BO'">
                            <!-- Beach Owner Name -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.beachOwnerName">اسم مالك الشاطئ</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.BeachOwnerName }">
                                {{ permitRequest?.licenseRequest?.beachOwnerName }}
                              </p>
                            </div>

                            <!-- Beach Trade Name -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.beachTradeName">الاسم التجاري للشاطئ</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.BeachTradeName }">
                                {{ permitRequest?.licenseRequest?.beachTradeName }}
                              </p>
                            </div>

                            <!-- Commercial Registration Number -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.beachOwnerCommercialRegistrationNumber"
                                >رقم السجل التجاري التابع لمالك الشاطئ</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.BeachOwnerCommercialRegistrationNumber }">
                                {{ permitRequest?.licenseRequest?.beachOwnerCommercialRegistrationNumber }}
                              </p>
                            </div>

                            <!-- Number of Restrooms -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfRestrooms"
                                >كم عدد دورات المياه الموجودة</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfRestrooms }">
                                {{ permitRequest?.licenseRequest?.numberOfRestrooms }}
                              </p>
                            </div>

                            <!-- Number of Lifeguards -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfLifeguards"
                                >كم عدد المنقذين الموجودة</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfLifeguards }">
                                {{ permitRequest?.licenseRequest?.numberOfLifeguards }}
                              </p>
                            </div>

                            <!-- Food Truck Parking -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfFoodTruckParking"
                                >عدد مواقف الفوود تراك</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfFoodTruckParking }">
                                {{ permitRequest?.licenseRequest?.numberOfFoodTruckParking }}
                              </p>
                            </div>

                            <!-- Visitor Capacity -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.visitorCapacity"
                                >عدد الطاقة الاستيعابية للزوار</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.VisitorCapacity }">
                                {{ permitRequest?.licenseRequest?.visitorCapacity }}
                              </p>
                            </div>

                            <!-- Number of Water Stations -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfWaterStations"
                                >كم عدد محطات المياه الموجودة</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfWaterStations }">
                                {{ permitRequest?.licenseRequest?.noOfWaterStations }}
                              </p>
                            </div>

                            <!-- Cabins for Rent -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.isThereCabinsForRent"
                                >هل يوجد غرف او كبائن للأيجار</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.IsThereCabinsForRent }">
                                <span *ngIf="permitRequest?.licenseRequest?.isThereCabinsForRent === true" jhiTranslate="global.form.yes"
                                  >نعم</span
                                >
                                <span *ngIf="permitRequest?.licenseRequest?.isThereCabinsForRent === false" jhiTranslate="global.form.no"
                                  >لا</span
                                >
                              </p>
                            </div>

                            <!-- Number of Cabins (Conditional) -->
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="permitRequest?.licenseRequest?.isThereCabinsForRent === true">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfCabinsAndRooms"
                                >عدد الكبائن والغرف</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfCabinsAndRooms }">
                                {{ permitRequest?.licenseRequest?.numberOfCabinsAndRooms }}
                              </p>
                            </div>

                            <!-- Marine Vessel -->
                            <div class="col-md-4 col-lg-3 col-xl-3">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.isThereMarineVessel">هل يوجد وسائط بحرية</label>
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.IsThereMarineVessel }">
                                <span *ngIf="permitRequest?.licenseRequest?.isThereMarineVessel === true" jhiTranslate="global.form.yes"
                                  >نعم</span
                                >
                                <span *ngIf="permitRequest?.licenseRequest?.isThereMarineVessel === false" jhiTranslate="global.form.no"
                                  >لا</span
                                >
                              </p>
                            </div>

                            <!-- Number of Marine Vessels (Conditional) -->
                            <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="permitRequest?.licenseRequest?.isThereMarineVessel === true">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfOtherMarineVessel"
                                >كم عدد الوسائط البحرية</label
                              >
                              <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfOtherMarineVessel }">
                                {{ permitRequest?.licenseRequest?.numberOfOtherMarineVessel }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/mdi_paper-outline.svg"
                        alt="mdi_paper-outline.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseFive"
                        aria-expanded="false"
                        aria-controls="collapseFive"
                        jhiTranslate="srsaApp.permitRequest.attachments"
                      >
                        المرفقات
                      </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem pt-0">
                        <hr />
                        <div class="row g-2 mb-5" [ngClass]="{ newChangesInput: permitChanges?.Attachments }">
                          <div class="col-4" *ngFor="let docType of documentTypes">
                            <p
                              *ngIf="selectedServiceType!.code !== 'BO'"
                              class="form-label"
                              jhiTranslate="srsaApp.permitRequest.{{ docType }}"
                            ></p>
                            <p
                              *ngIf="selectedServiceType!.code === 'BO'"
                              class="form-label"
                              jhiTranslate="srsaApp.permitRequest.beachOperator.{{ docType }}"
                            ></p>

                            <span *ngFor="let doc of documents">
                              <button type="button" *ngIf="doc.key == docType" disabled class="btn btn-light d-flex">
                                <span class="item-value doc-name">{{ doc.file.name }}</span>
                                <!--                              <input type="image" id="myimage" src="../../../../content/images/fi_download.svg" class="btn btn-gray" />-->
                              </button>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mk-wizard-actions d-flex-between gap-3 mk-wizard-btns">
                <!--                <button-->
                <!--                  *ngIf="permitRequest!.requestStatus == 'DRAFT' || permitRequest!.requestStatus == null"-->
                <!--                  type="submit"-->
                <!--                  id="save-entity"-->
                <!--                  data-cy="entityCreateSaveButton"-->
                <!--                  class="btn btn-outline-primary"-->
                <!--                  (click)="prepareRequestStatus('DRAFT')"-->
                <!--                  jhiTranslate="srsaApp.permitRequest.home.saveAsDraft"-->
                <!--                >-->
                <!--                  <span> حفظ كمسودة </span>-->
                <!--                </button>-->

                <div class="d-flex-between gap-3">
                  <a
                    href="javascript:void(0)"
                    (click)="wizardPrevStep()"
                    *ngIf="
                      currentStep !== 1 &&
                      !(
                        (permitRequest!.requestStatus == 'RETURNED_LICENSING_MANAGER' ||
                          permitRequest!.requestStatus == 'RETURNED_LICENSING_OFFICER' ||
                          permitRequest!.requestStatus == 'RETURNED_INSPECTION_OFFICER') &&
                        currentStep == 3
                      )
                    "
                    class="btn btn-outline-primary mk-btn-prev"
                    jhiTranslate="global.menu.previous"
                    >السابق</a
                  >

                  <a
                    href="javascript:void(0)"
                    (click)="wizardNextStep()"
                    [ngClass]="(currentStep === 1 && !showCrDetails) || isCurrentWizardInvalid() ? 'disabled' : ''"
                    *ngIf="currentStep !== steps.length"
                    class="btn btn-outline-primary mk-btn-next"
                    jhiTranslate="global.menu.next"
                    >التالي</a
                  >
                  <!--                  (click)="prepareRequestStatus('PENDING_REVIEW')"-->
                  <!-- Button trigger modal -->
                  <div *ngIf="currentStep === steps.length">
                    <button
                      *ngIf="permitRequest!.requestStatus == 'DRAFT' || permitRequest!.requestStatus == null"
                      type="button"
                      data-cy="entityCreateCancelButton"
                      class="btn btn-primary"
                      data-bs-toggle="modal"
                      data-bs-target="#liabilityModal"
                    >
                      <span jhiTranslate="srsaApp.permitRequest.detail.sendRequest">إرسال الطلب</span>
                    </button>
                    <button
                      *ngIf="
                        permitRequest!.requestStatus == 'RETURNED_LICENSING_MANAGER' ||
                        permitRequest!.requestStatus == 'RETURNED_LICENSING_OFFICER' ||
                        permitRequest!.requestStatus == 'RETURNED_INSPECTION_OFFICER'
                      "
                      type="submit"
                      data-cy="entityCreateCancelButton"
                      class="btn btn-primary"
                      (click)="prepareRequestStatus('RETURNED')"
                      jhiTranslate="srsaApp.permitRequest.detail.resendRequest"
                    >
                      إعادة إرسال الطلب
                    </button>
                  </div>
                </div>
              </div>
            </section>
          </div>

          <!-- Modal -->
          <div
            class="modal fade"
            id="staticBackdrop1"
            data-bs-backdrop="static"
            data-bs-keyboard="false"
            tabindex="-1"
            aria-labelledby="staticBackdropLabel1"
            aria-hidden="true"
          >
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div *ngIf="permitNumber != undefined" class="modal-body text-center mt-4 p-4">
                  <div>
                    <img src="../../../../content/images/checked.svg" alt="checked" />
                  </div>
                  <p class="sendSubmit mt-4">
                    {{ 'srsaApp.permitRequest.created' | translate: { param: permitNumber } }}
                  </p>
                  <p class="sendSubmitSub mx-2rem">
                    {{ 'srsaApp.permitRequest.createdNumber' | translate: { param: permitNumber } }}
                  </p>
                  <button
                    (click)="previousState()"
                    type="button"
                    class="btn btn-primary"
                    data-bs-dismiss="modal"
                    jhiTranslate="srsaApp.permitRequest.detail.myRequest"
                  >
                    اذهب الى طلباتي
                  </button>
                </div>
                <div *ngIf="!isSuccess" class="modal-body text-center mt-4 p-4">
                  <div>
                    <img src="../../../../content/images/redChecked.png" alt="checked" />
                  </div>
                  <p class="sendSubmit mt-4">
                    {{ 'error.systemException' | translate }}
                  </p>
                  <button
                    (click)="previousState()"
                    type="button"
                    class="btn btn-primary"
                    data-bs-dismiss="modal"
                    jhiTranslate="srsaApp.permitRequest.detail.myRequest"
                  >
                    اذهب الى طلباتي
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="exampleModal"
  tabindex="-1"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="d-flex my-3">
          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />
          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="global.termsAndConditions.reviewTermsAndConditions">
            الرجاء الاطلاع والموافقة على الشروط والاحكام
          </p>
        </div>
        <div class="card-new-popUp">
          <div class="card-body">
            <jhi-terms-conditions *ngIf="selectedServiceType?.id! == 1" [type]="'MaritimeTorismAgentLicense'"></jhi-terms-conditions>
            <jhi-terms-conditions *ngIf="selectedServiceType?.id! == 3" [type]="'TorismMarineOpreatorLicense'"></jhi-terms-conditions>
            <jhi-terms-conditions *ngIf="selectedServiceType?.id! == 2" [type]="'YachtsCharterLicense'"></jhi-terms-conditions>
            <jhi-terms-conditions *ngIf="selectedServiceType?.code == 'BO'" [type]="'BeachOperatorLicense'"></jhi-terms-conditions>
            <div>
              <div class="check-form d-flex my-3 gap-3">
                <input
                  class="form-check-input"
                  style="margin-top: 0.1em"
                  type="checkbox"
                  [checked]="disclaimerChecked"
                  (change)="disclaimerChecked = !disclaimerChecked"
                  id="disclaimer"
                />
                <label class="fs-5" for="disclaimer" jhiTranslate="srsaApp.permitRequest.termsAndConditions"
                  >اوافق واقر على الشروط والاحكام وسياسات الهيئة السعودية للبحر الاحمر</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          id="cancel-save"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!disclaimerChecked"
          (click)="showWizard()"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          jhiTranslate="global.termsAndConditions.accept"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="liabilityModal"
  tabindex="-1"
  aria-labelledby="liabilityModalLabel"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="d-flex my-3">
          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />
          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="srsaApp.permitRequest.detail.obligations">
            الرجاء الاطلاع والموافقة على الشروط والاحكام
          </p>
        </div>
        <div class="card-new-popUp">
          <div class="card-body">
            <jhi-controls-obligations
              *ngIf="selectedServiceType?.id! == 1"
              [type]="'MaritimeTorismAgentLicense'"
            ></jhi-controls-obligations>
            <jhi-controls-obligations
              *ngIf="selectedServiceType?.id! == 3"
              [type]="'TorismMarineOpreatorLicense'"
            ></jhi-controls-obligations>
            <jhi-controls-obligations *ngIf="selectedServiceType?.id! == 2" [type]="'YachtsCharterLicense'"></jhi-controls-obligations>
            <jhi-controls-obligations *ngIf="selectedServiceType?.code == 'BO'" [type]="'BeachOperatorLicense'"></jhi-controls-obligations>
            <div>
              <div class="check-form d-flex my-3 gap-3">
                <input
                  class="form-check-input"
                  style="margin-top: 0.1em"
                  type="checkbox"
                  [checked]="liabilityChecked"
                  (change)="liabilityChecked = !liabilityChecked"
                  id="disclaimer"
                />
                <label class="fs-5" for="disclaimer" jhiTranslate="srsaApp.permitRequest.obligations"
                  >اوافق واقر على الالتزامات وسياسات الهيئة السعودية للبحر الاحمر</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          id="cancel-save"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!liabilityChecked"
          (click)="prepareRequestStatus('PENDING_REVIEW'); save()"
          data-bs-toggle="modal"
          data-bs-target="#staticBackdrop1"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          jhiTranslate="global.termsAndConditions.accept"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="staticBackdrop21"
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="staticBackdropLabel1"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered mk-modal-fix">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <div>
          <img src="../../../../content/images/checked.svg" alt="checked" />
        </div>
        <p class="sendSubmit mt-4">
          {{ 'srsaApp.permitRequest.created' | translate: { param: permitNumber } }}
        </p>
        <p class="sendSubmitSub mx-2rem">
          {{ 'srsaApp.permitRequest.createdNumber' | translate: { param: permitNumber } }}
        </p>
        <button
          type="button"
          class="btn btn-primary"
          data-bs-dismiss="modal"
          (click)="previousState()"
          jhiTranslate="srsaApp.permitRequest.detail.myRequest"
        >
          اذهب الى طلباتي
        </button>
      </div>
    </div>
  </div>
</div>
