package com.elm.srsa.service;

import com.elm.srsa.config.ApplicationProperties;
import com.elm.srsa.domain.*;
import com.elm.srsa.domain.enumeration.LicenseDuration;
import com.elm.srsa.domain.enumeration.StatusType;
import com.elm.srsa.domain.enumeration.VisitStatus;
import com.elm.srsa.repository.*;
import com.elm.srsa.security.AuthoritiesConstants;
import com.elm.srsa.security.RsaUser;
import com.elm.srsa.security.SecurityUtils;
import com.elm.srsa.service.dto.AdminUserDTO;
import com.elm.srsa.service.dto.AttachmentDTO;
import com.elm.srsa.service.dto.PermitRequestDTO;
import com.elm.srsa.service.enums.EmailTemplate;
import com.elm.srsa.service.mapper.AttachmentMapper;
import com.elm.srsa.service.mapper.PermitRequestListMapper;
import com.elm.srsa.service.mapper.PermitRequestMapper;
import com.elm.srsa.service.mapper.UserMapper;
import com.elm.srsa.util.*;
import com.elm.srsa.wathq.rest.client.response.newResponse.Activity;
import com.elm.srsa.wathq.rest.client.response.newResponse.CrInfoWathqResponse;
import com.google.zxing.WriterException;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

/**
 * Service Implementation for managing {@link PermitRequest}.
 */
@Service
@Transactional
public class PermitRequestService {

    private final Logger log = LoggerFactory.getLogger(PermitRequestService.class);
    private final PaymentRequestRepository paymentRequestRepository;
    private final BeachActivitiesRepository beachActivitiesRepository;
    private final BeachEquipmentRepository beachEquipmentRepository;

    @Value("${srsa.scheduler.enabled}")
    private Boolean schedulerEnabled;

    @Value("${srsa.payment.cronJob.enabled}")
    private Boolean paymentSchedulerEnabled;

    @Value("${srsa.payment.gracePeriodInHours}")
    private Integer gracePeriodInHours;

    private final MailService mailService;
    private final PermitRequestRepository permitRequestRepository;
    private final PermitRequestStatusRepository permitRequestStatusRepository;
    private final PermitRequestHistoryRepository permitRequestHistoryRepository;
    private final CompanyService companyService;
    private final CompanyOwnerService companyOwnerService;
    private final CompanyOwnerRepository companyOwnerRepository;
    private final NationalAddressService nationalAddressService;
    private final WathqApiResponseService wathqApiResponseService;
    private final PermitRequestMapper permitRequestMapper;
    private final PermitRequestListMapper permitRequestListMapper;
    private final UserRepository userRepository;
    private final RandomUtil randomUtil;
    private final ServiceTypeRepository serviceTypeRepository;
    private final LicenseRequestMaritimeMediumRepository licenseRequestMaritimeMediumRepository;
    private final LicenseRequestProvidedLanguagesRepository licenseRequestProvidedLanguagesRepository;
    private final LicenseRequestMaritimeServicesRepository licenseRequestMaritimeServicesRepository;
    private final LicenseRequestMarineTourServicesRepository licenseRequestMarineTourServicesRepository;
    private final LicenseRequestCountryRepository licenseRequestCountryRepository;
    private final LicenseRequestFuelTypesRepository licenseRequestFuelTypesRepository;
    private final ApplicationProperties.HtmlToPdf htmlToPdf;
    private final AttachmentService attachmentService;
    private final AttachmentMapper attachmentMapper;
    private final AttachmentRepository attachmentRepository;

    private final PermitRequestQueryService permitRequestQueryService;

    private final PermitVisitRepository permitVisitRepository;

    private final VisitAttachmentRepository visitAttachmentRepository;
    private final FileDownloadUtil downloadUtil;
    private final UserMapper userMapper;
    private final CompanyActivityService companyActivityService;
    private final DocksRepository docksRepository;
    private final LicenseRequestDocksRepository licenseRequestDocksRepository;

    private final PermitChangesRepository permitChangesRepository;

    private static final String RETURN_TO_LICENSING_SUPERVISOR_OPTION = "LICENSING_SUPERVISOR";

    public PermitRequestService(
        MailService mailService,
        PermitRequestRepository permitRequestRepository,
        PermitRequestStatusRepository permitRequestStatusRepository,
        PermitRequestHistoryRepository permitRequestHistoryRepository,
        CompanyService companyService,
        CompanyOwnerService companyOwnerService,
        CompanyOwnerRepository companyOwnerRepository,
        NationalAddressService nationalAddressService,
        WathqApiResponseService wathqApiResponseService,
        PermitRequestMapper permitRequestMapper,
        PermitRequestListMapper permitRequestListMapper,
        UserRepository userRepository,
        RandomUtil randomUtil,
        ServiceTypeRepository serviceTypeRepository,
        LicenseRequestMaritimeMediumRepository licenseRequestMaritimeMediumRepository,
        LicenseRequestProvidedLanguagesRepository licenseRequestProvidedLanguagesRepository,
        LicenseRequestMaritimeServicesRepository licenseRequestMaritimeServicesRepository,
        LicenseRequestMarineTourServicesRepository licenseRequestMarineTourServicesRepository,
        LicenseRequestCountryRepository licenseRequestCountryRepository,
        LicenseRequestFuelTypesRepository licenseRequestFuelTypesRepository,
        ApplicationProperties appProperties,
        PermitRequestQueryService permitRequestQueryService,
        UserMapper userMapper,
        AttachmentService attachmentService,
        AttachmentMapper attachmentMapper,
        AttachmentRepository attachmentRepository,
        PermitVisitRepository permitVisitRepository,
        VisitAttachmentRepository visitAttachmentRepository,
        FileDownloadUtil downloadUtil,
        PaymentRequestRepository paymentRequestRepository,
        CompanyActivityService companyActivityService,
        DocksRepository docksRepository,
        LicenseRequestDocksRepository licenseRequestDocksRepository,
        PermitChangesRepository permitChangesRepository,
        BeachActivitiesRepository beachActivitiesRepository,
        BeachEquipmentRepository beachEquipmentRepository
    ) {
        this.mailService = mailService;
        this.permitRequestRepository = permitRequestRepository;
        this.permitRequestStatusRepository = permitRequestStatusRepository;
        this.permitRequestHistoryRepository = permitRequestHistoryRepository;
        this.companyService = companyService;
        this.companyOwnerService = companyOwnerService;
        this.companyOwnerRepository = companyOwnerRepository;
        this.nationalAddressService = nationalAddressService;
        this.wathqApiResponseService = wathqApiResponseService;
        this.permitRequestMapper = permitRequestMapper;
        this.permitRequestListMapper = permitRequestListMapper;
        this.userRepository = userRepository;
        this.randomUtil = randomUtil;
        this.serviceTypeRepository = serviceTypeRepository;
        this.licenseRequestMaritimeMediumRepository = licenseRequestMaritimeMediumRepository;
        this.licenseRequestProvidedLanguagesRepository = licenseRequestProvidedLanguagesRepository;
        this.licenseRequestMaritimeServicesRepository = licenseRequestMaritimeServicesRepository;
        this.licenseRequestMarineTourServicesRepository = licenseRequestMarineTourServicesRepository;
        this.licenseRequestCountryRepository = licenseRequestCountryRepository;
        this.licenseRequestFuelTypesRepository = licenseRequestFuelTypesRepository;
        this.htmlToPdf = appProperties.getHtmlToPdf();
        this.attachmentService = attachmentService;
        this.attachmentMapper = attachmentMapper;
        this.attachmentRepository = attachmentRepository;
        this.permitRequestQueryService = permitRequestQueryService;
        this.userMapper = userMapper;
        this.permitVisitRepository = permitVisitRepository;
        this.visitAttachmentRepository = visitAttachmentRepository;
        this.downloadUtil = downloadUtil;
        this.paymentRequestRepository = paymentRequestRepository;
        this.companyActivityService = companyActivityService;
        this.docksRepository = docksRepository;
        this.licenseRequestDocksRepository = licenseRequestDocksRepository;
        this.permitChangesRepository = permitChangesRepository;
        this.beachActivitiesRepository = beachActivitiesRepository;
        this.beachEquipmentRepository = beachEquipmentRepository;
    }

    /**
     * Save a permitRequest.
     *
     * @param permitRequestDTO the entity to save.
     * @return the persisted entity.
     */
    @Transactional
    public PermitRequestDTO save(PermitRequestDTO permitRequestDTO, List<MultipartFile> files) {
        log.debug("Request to save PermitRequest : {}", permitRequestDTO);
        PermitRequest permitRequest = permitRequestMapper.toEntity(permitRequestDTO);
        User loggedInUser = userRepository.findById(SecurityUtils.getCurrentUser().getUserID()).get();
        Long lastSeqNum = randomUtil.getLastSeqNum();
        permitRequest.setLicenceNumber(
            randomUtil.generateRequestNumber(serviceTypeRepository.findById(permitRequestDTO.getServiceType().getId()).get(), lastSeqNum)
        );
        permitRequest.setRequestNumber(randomUtil.generateRequestNumber(lastSeqNum));
        permitRequest.setSubmitDate(Instant.now());
        BigDecimal durationYears = BigDecimal.valueOf(1);
        if (permitRequest.getLicenseRequest().getRequestedLicenseDuration().toString().equals(LicenseDuration.TWO_YEAR.toString())) {
            durationYears = BigDecimal.valueOf(2);
        } else if (
            permitRequest.getLicenseRequest().getRequestedLicenseDuration().toString().equals(LicenseDuration.THREE_YEAR.toString())
        ) {
            durationYears = BigDecimal.valueOf(3);
        }
        permitRequest.setFees(permitRequest.getServiceType().getFees().multiply(durationYears));
        permitRequest.setPaymentPeriodDays(permitRequest.getServiceType().getPaymentPeriodDays());
        permitRequest.setPaymentPeriodDate(Instant.now().plus(permitRequest.getServiceType().getPaymentPeriodDays(), ChronoUnit.DAYS));
        Company company = companyService.getRegisteredCompany();
        permitRequest.setCompany(company);
        if (permitRequestDTO.getSecondaryCompany() != null && permitRequestDTO.getBranchDto() != null) {
            permitRequestDTO.getSecondaryCompany().setCrIssueDate(company.getCrIssueDate());
            permitRequestDTO.getSecondaryCompany().setCrExpiryDate(company.getCrExpiryDate());
            permitRequestDTO.getSecondaryCompany().setBusinessType(company.getBusinessType());
            permitRequestDTO.getSecondaryCompany().setCrEntityNumber(permitRequestDTO.getBranchDto().getCrNationalNumber());
            permitRequestDTO.getSecondaryCompany().setCrMainEntityNumber(permitRequestDTO.getBranchDto().getMainCrNationalNumber());
            permitRequestDTO.getSecondaryCompany().setCrName(permitRequestDTO.getBranchDto().getNameAr());
            permitRequestDTO.getSecondaryCompany().setParentCrNumber(company.getCrNumber());
            permitRequestDTO.getSecondaryCompany().setCrNumber(permitRequestDTO.getBranchDto().getCrNumber());
            Company secCompany = companyService.saveCompany(permitRequestDTO.getSecondaryCompany());
            permitRequest.setSecondaryCompany(secCompany);
        }

        if (permitRequestDTO.getWorkLocation() != null) {
            NationalAddress nationalAddress = nationalAddressService.save(permitRequestDTO.getWorkLocation());
            permitRequest.setWorkLocation(nationalAddress);
        }

        if (permitRequestDTO.getLicenseRequest().getMarinaLocation() != null) {
            NationalAddress nationalAddress = nationalAddressService.save(permitRequestDTO.getLicenseRequest().getMarinaLocation());
            permitRequest.getLicenseRequest().setMarinaLocation(nationalAddress);
        }
        //        List<CompanyOwner> companyOwners= companyOwnerRepository.findAllByCompany(company);
        company.setCompanyOwners(null);
        companyOwnerService.deleteAllByCompany(company);
        companyOwnerService.save(permitRequestDTO.getCompanyOwners(), company);

        List<AttachmentDTO> attachmentDTOS = new ArrayList<>();
        if (files != null) {
            for (MultipartFile multipartFile : files) {
                try {
                    // TODO pass allowed max size for files here
                    if (multipartFile.getBytes().length >= 1) {
                        attachmentService.saveFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            permitRequest,
                            attachmentDTOS,
                            false,
                            null
                        );
                    }
                } catch (IOException e) {
                    throw new RuntimeException("fail to save attachment in permit request saving : " + e.getMessage());
                }
            }
        }
        List<Attachment> attachments = attachmentMapper.toEntity(attachmentDTOS);
        permitRequest.setAttachments(new HashSet<>(attachments));
        permitRequest.setUuid(UUID.randomUUID());
        setTotalPriceWithAndWithoutVat(permitRequest, durationYears);
        final PermitRequest savedPermitRequest = permitRequestRepository.save(permitRequest);
        if (permitRequest.getLicenseRequest().getLicenseRequestDocks() != null) {
            permitRequest
                .getLicenseRequest()
                .getLicenseRequestDocks()
                .forEach(dock -> {
                    dock.setLicenseRequest(savedPermitRequest.getLicenseRequest());
                    dock.setDocks(docksRepository.save(dock.getDocks()));
                    licenseRequestDocksRepository.save(dock);
                });
        }

        if (savedPermitRequest.getLicenseRequest() != null) {
            LicenseRequest licenseRequest = savedPermitRequest.getLicenseRequest();

            // Handle BeachActivities lookup associations
            if (
                permitRequestDTO.getLicenseRequest() != null &&
                permitRequestDTO.getLicenseRequest().getBeachActivities() != null &&
                !permitRequestDTO.getLicenseRequest().getBeachActivities().isEmpty()
            ) {
                // Load managed BeachActivities entities
                Set<Long> beachActivityIds = permitRequestDTO
                    .getLicenseRequest()
                    .getBeachActivities()
                    .stream()
                    .map(BeachActivities::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

                Set<BeachActivities> managedBeachActivities = new HashSet<>(beachActivitiesRepository.findAllById(beachActivityIds));

                if (managedBeachActivities.size() != beachActivityIds.size()) {
                    throw new EntityNotFoundException("Some BeachActivities not found");
                }

                // Set the activities (this will create the join entities)
                licenseRequest.setBeachActivities(managedBeachActivities);
            }

            // Handle BeachEquipment lookup associations
            if (
                permitRequestDTO.getLicenseRequest() != null &&
                permitRequestDTO.getLicenseRequest().getBeachEquipment() != null &&
                !permitRequestDTO.getLicenseRequest().getBeachEquipment().isEmpty()
            ) {
                // Load managed BeachEquipment entities
                Set<Long> beachEquipmentIds = permitRequestDTO
                    .getLicenseRequest()
                    .getBeachEquipment()
                    .stream()
                    .map(BeachEquipment::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

                Set<BeachEquipment> managedBeachEquipment = new HashSet<>(beachEquipmentRepository.findAllById(beachEquipmentIds));

                if (managedBeachEquipment.size() != beachEquipmentIds.size()) {
                    throw new EntityNotFoundException("Some beachEquipment not found");
                }

                // Set the Equipment (this will create the join entities)
                licenseRequest.setBeachEquipment(managedBeachEquipment);
            }
        }
        PermitRequest finalsavedPermitRequest = permitRequestRepository.save(savedPermitRequest);

        //        attachmentDTOS.forEach(attachmentDTO -> attachmentDTO.setPermitRequest(permitRequestMapper.toDto(savedPermitRequest)));
        attachments.forEach(attachment -> attachment.setPermitRequest(finalsavedPermitRequest));
        attachmentRepository.saveAll(attachments);

        savePermitRequestStatus(finalsavedPermitRequest);
        savePermitRequestHistory(finalsavedPermitRequest);
        return permitRequestMapper.toDto(finalsavedPermitRequest);
    }

    public void setTotalPriceWithAndWithoutVat(PermitRequest permitRequest, BigDecimal durationYears) {
        if (Objects.nonNull(permitRequest.getServiceType())) {
            permitRequest.setTotalPriceWithVat(permitRequest.getServiceType().getFees().multiply(durationYears));
            permitRequest.setTotalPriceWithoutVat(permitRequest.getServiceType().getFees().multiply(durationYears));
        }
    }

    public PermitRequestDTO updatePermitRequest(PermitRequestDTO permitRequestDTO, MultipartFile[] files) {
        log.debug("Request to save PermitRequest : {}", permitRequestDTO);
        Optional<PermitRequest> oldPermitRequestOptional = null;

        if (SecurityUtils.isServiceProvider()) {
            oldPermitRequestOptional =
                permitRequestRepository.findByIdAndCompany_Id(permitRequestDTO.getId(), companyService.getRegisteredCompany().getId());
            if (oldPermitRequestOptional.isEmpty()) {
                throw new RuntimeException("Cannot updat a permit request not belonging my company");
            } else {
                try {
                    recordChanges(oldPermitRequestOptional.get(), permitRequestDTO);
                } catch (Exception ex) {
                    log.debug("Error in recording changes : {}", ex.getMessage());
                }
            }
        }

        PermitRequest permitRequest = permitRequestMapper.toEntity(permitRequestDTO);
        if (oldPermitRequestOptional != null) {
            permitRequest.setAssignedEmployeeRole(oldPermitRequestOptional.get().getAssignedEmployeeRole());
        }
        permitRequest.setPaymentPeriodDate(Instant.now().plus(permitRequest.getServiceType().getPaymentPeriodDays(), ChronoUnit.DAYS));
        User loggedInUser = userRepository.findById(SecurityUtils.getCurrentUser().getUserID()).get();
        if (permitRequestDTO.getWorkLocation() != null) {
            NationalAddress nationalAddress = nationalAddressService.save(permitRequestDTO.getWorkLocation());
            permitRequest.setWorkLocation(nationalAddress);
        }
        if (permitRequestDTO.getLicenseRequest().getMarinaLocation() != null) {
            NationalAddress marinaLocation = nationalAddressService.save(permitRequestDTO.getLicenseRequest().getMarinaLocation());
            permitRequest.getLicenseRequest().setMarinaLocation(marinaLocation);
        }

        licenseRequestFuelTypesRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        licenseRequestCountryRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        licenseRequestMarineTourServicesRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        licenseRequestMaritimeServicesRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        licenseRequestProvidedLanguagesRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        licenseRequestMaritimeMediumRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        licenseRequestDocksRepository.nativeDeleteAllByLicenseRequestId(permitRequestDTO.getLicenseRequest().getId());
        if (permitRequestDTO.getAttachments() == null) {
            permitRequestDTO.setAttachments(new ArrayList<>());
        }
        List<AttachmentDTO> attachmentDTOS = new ArrayList<>(permitRequestDTO.getAttachments());
        if (files != null) {
            for (MultipartFile multipartFile : files) {
                if (!Objects.equals(multipartFile.getOriginalFilename(), "no-file-h")) {
                    try {
                        // This will filter files that are already saved
                        // TODO pass allowed max size for files here
                        if (multipartFile.getBytes().length >= 1) {
                            attachmentService.saveFileAndReturnPath(
                                Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                                multipartFile.getOriginalFilename(),
                                permitRequest,
                                attachmentDTOS,
                                permitRequestRepository.getReferenceById(permitRequestDTO.getId()).getRequestStatus() == StatusType.DRAFT,
                                null
                            );
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("fail to save attachment in permit request update : " + e.getMessage());
                    }
                }
            }
        }

        attachmentDTOS.forEach(attachment -> {
            if (attachment.isRemoved()) {
                if (permitRequestRepository.getReferenceById(permitRequestDTO.getId()).getRequestStatus() == StatusType.DRAFT) {
                    attachmentService.deleteFile(attachment.getDocUrl());
                }
                attachmentRepository.deleteById(attachment.getId());
            } else if (attachment.isRemoved() || attachment.getId() == null) {
                permitChangesRepository.save(new PermitChanges().getChangeObj(permitRequestDTO.getRequestNumber(), "Attachments"));
            }
        });

        // Filter replaced file from the List
        attachmentDTOS = attachmentDTOS.stream().filter(attachment -> !attachment.isRemoved()).collect(Collectors.toList());

        List<Attachment> attachments = attachmentMapper.toEntity(attachmentDTOS);
        permitRequest.setAttachments(new HashSet<>(attachments));

        final PermitRequest savedPermitRequest = permitRequestRepository.save(permitRequest);

        attachments.forEach(attachment -> attachment.setPermitRequest(savedPermitRequest));
        attachmentRepository.saveAll(attachments);

        if (permitRequest.getLicenseRequest().getLicenseRequestDocks() != null) {
            permitRequest
                .getLicenseRequest()
                .getLicenseRequestDocks()
                .forEach(dock -> {
                    dock.setLicenseRequest(savedPermitRequest.getLicenseRequest());
                    dock.setDocks(docksRepository.save(dock.getDocks()));
                    licenseRequestDocksRepository.save(dock);
                });
        }

        //        permitRequest = permitRequestRepository.save(permitRequest);
        if (savedPermitRequest.getRequestStatus() != StatusType.DRAFT) {
            savePermitRequestStatus(savedPermitRequest);
            savePermitRequestHistory(savedPermitRequest);
        }
        //        savePermitRequestHistory(permitRequest);
        return permitRequestMapper.toDto(savedPermitRequest);
    }

    private void recordChanges(PermitRequest oldPermit, PermitRequestDTO permitRequestDTO) {
        permitChangesRepository.deleteAllByRequestNumber(oldPermit.getRequestNumber());
        List<PermitChanges> permitChangesList = new ArrayList<>();
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestMaritimeMediums().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestMaritimeMediums().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestMaritimeMediums()
                .forEach(oldMedium -> {
                    oldIds.add(oldMedium.getMaritimeMedium().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestMaritimeMediums()
                .forEach(newMedium -> {
                    newIds.add(newMedium.getMaritimeMedium().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestMaritimeMediums"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestMaritimeMediums"));
        }
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestProvidedLanguages().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestProvidedLanguages().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestProvidedLanguages()
                .forEach(oldLang -> {
                    oldIds.add(oldLang.getProvidedLanguages().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestProvidedLanguages()
                .forEach(newLang -> {
                    newIds.add(newLang.getProvidedLanguages().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestProvidedLanguages"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestProvidedLanguages"));
        }
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestMaritimeServices().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestMaritimeServices().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestMaritimeServices()
                .forEach(oldMaritimeService -> {
                    oldIds.add(oldMaritimeService.getMaritimeTourismServices().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestMaritimeServices()
                .forEach(newMaritimeService -> {
                    newIds.add(newMaritimeService.getMaritimeTourismServices().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestMaritimeServices"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestMaritimeServices"));
        }
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestMarineTourServices().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestMarineTourServices().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestMarineTourServices()
                .forEach(oldMaritimeService -> {
                    oldIds.add(oldMaritimeService.getMarineTourOperatorServices().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestMarineTourServices()
                .forEach(newMaritimeService -> {
                    newIds.add(newMaritimeService.getMarineTourOperatorServices().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestMarineTourServices"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestMarineTourServices"));
        }
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestCountries().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestCountries().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestCountries()
                .forEach(oldCountry -> {
                    oldIds.add(oldCountry.getCountryOfWork().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestCountries()
                .forEach(newCountry -> {
                    newIds.add(newCountry.getCountryOfWork().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestCountries"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestCountries"));
        }
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestFuelTypes().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestFuelTypes().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestFuelTypes()
                .forEach(oldFuelType -> {
                    oldIds.add(oldFuelType.getFuelTypes().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestFuelTypes()
                .forEach(newFuelType -> {
                    newIds.add(newFuelType.getFuelTypes().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestFuelTypes"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestFuelTypes"));
        }
        if (
            !oldPermit.getLicenseRequest().getLicenseRequestDocks().isEmpty() ||
            !permitRequestDTO.getLicenseRequest().getLicenseRequestDocks().isEmpty()
        ) {
            List<Long> oldIds = new ArrayList<>();
            List<Long> newIds = new ArrayList<>();
            oldPermit
                .getLicenseRequest()
                .getLicenseRequestDocks()
                .forEach(oldDock -> {
                    oldIds.add(oldDock.getDocks().getId());
                });
            permitRequestDTO
                .getLicenseRequest()
                .getLicenseRequestDocks()
                .forEach(newDock -> {
                    newIds.add(newDock.getDocks().getId());
                });
            Collections.sort(oldIds);
            Collections.sort(newIds);
            if (!oldIds.equals(newIds)) {
                permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestDocks"));
            }
        } else {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "LicenseRequestDocks"));
        }

        if (
            (oldPermit.getLicenseRequest().getRequestedLicenseDuration() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getRequestedLicenseDuration()
                    .equals(permitRequestDTO.getLicenseRequest().getRequestedLicenseDuration())) ||
            (oldPermit.getLicenseRequest().getRequestedLicenseDuration() == null &&
                permitRequestDTO.getLicenseRequest().getRequestedLicenseDuration() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "RequestedLicenseDuration"));
        }
        if (
            (oldPermit.getTotalFundingOfProject() != null &&
                !oldPermit.getTotalFundingOfProject().setScale(0).equals(permitRequestDTO.getTotalFundingOfProject().setScale(0))) ||
            (oldPermit.getTotalFundingOfProject() == null && permitRequestDTO.getTotalFundingOfProject() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "TotalFundingOfProject"));
        }
        if (
            (oldPermit.getSaudiLaborCount() != null && !oldPermit.getSaudiLaborCount().equals(permitRequestDTO.getSaudiLaborCount())) ||
            (oldPermit.getSaudiLaborCount() == null && permitRequestDTO.getSaudiLaborCount() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "SaudiLaborCount"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfYearsExperience() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getNoOfYearsExperience()
                    .equals(permitRequestDTO.getLicenseRequest().getNoOfYearsExperience())) ||
            (oldPermit.getLicenseRequest().getNoOfYearsExperience() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfYearsExperience() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfYearsExperience"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfCountryCompanyOpr() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getNoOfCountryCompanyOpr()
                    .equals(permitRequestDTO.getLicenseRequest().getNoOfCountryCompanyOpr())) ||
            (oldPermit.getLicenseRequest().getNoOfCountryCompanyOpr() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfCountryCompanyOpr() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfCountryCompanyOpr"));
        }
        if (
            ((oldPermit.getLicenseRequest().getNoOfManagedMaritimeMediumsGeneral() != null &&
                    !oldPermit
                        .getLicenseRequest()
                        .getNoOfManagedMaritimeMediumsGeneral()
                        .equals(permitRequestDTO.getLicenseRequest().getNoOfManagedMaritimeMediumsGeneral())) ||
                (oldPermit.getLicenseRequest().getNoOfManagedMaritimeMediumsGeneral() == null &&
                    permitRequestDTO.getLicenseRequest().getNoOfManagedMaritimeMediumsGeneral() != null))
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfManagedMaritimeMediumsGeneral"));
        }
        if (
            (oldPermit.getMinistryOfHumanResourcesNumber() != null &&
                !oldPermit.getMinistryOfHumanResourcesNumber().equals(permitRequestDTO.getMinistryOfHumanResourcesNumber())) ||
            (oldPermit.getMinistryOfHumanResourcesNumber() == null && permitRequestDTO.getMinistryOfHumanResourcesNumber() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MinistryOfHumanResourcesNumber"));
        }
        if (
            (oldPermit.getSaudiLaborCount() != null && !oldPermit.getSaudiLaborCount().equals(permitRequestDTO.getSaudiLaborCount())) ||
            (oldPermit.getSaudiLaborCount() == null && permitRequestDTO.getSaudiLaborCount() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "SaudiLaborCount"));
        }
        if (
            (oldPermit.getForeignersLaborCount() != null &&
                !oldPermit.getForeignersLaborCount().equals(permitRequestDTO.getForeignersLaborCount())) ||
            (oldPermit.getForeignersLaborCount() == null && permitRequestDTO.getForeignersLaborCount() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "ForeignersLaborCount"));
        }
        if (
            (oldPermit.getNumberOfMaleEmployees() != null &&
                !oldPermit.getNumberOfMaleEmployees().equals(permitRequestDTO.getNumberOfMaleEmployees())) ||
            (oldPermit.getNumberOfMaleEmployees() == null && oldPermit.getNumberOfMaleEmployees() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NumberOfMaleEmployees"));
        }
        if (
            (oldPermit.getNumberOfFemaleEmployees() != null &&
                !oldPermit.getNumberOfFemaleEmployees().equals(permitRequestDTO.getNumberOfFemaleEmployees())) ||
            (oldPermit.getNumberOfFemaleEmployees() == null && permitRequestDTO.getNumberOfFemaleEmployees() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NumberOfFemaleEmployees"));
        }
        if (
            (oldPermit.getSaudizationLevel() != null && !oldPermit.getSaudizationLevel().equals(permitRequestDTO.getSaudizationLevel())) ||
            (oldPermit.getSaudizationLevel() == null && permitRequestDTO.getSaudizationLevel() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "SaudizationLevel"));
        }
        if (
            (oldPermit.getRateRange() != null &&
                !oldPermit.getRateRange().setScale(0).equals(permitRequestDTO.getRateRange().setScale(0))) ||
            (oldPermit.getRateRange() == null && permitRequestDTO.getRateRange() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "RateRange"));
        }
        if (
            (oldPermit.getContactOfficerName() != null &&
                !oldPermit.getContactOfficerName().equals(permitRequestDTO.getContactOfficerName())) ||
            (oldPermit.getContactOfficerName() == null && permitRequestDTO.getContactOfficerName() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "ContactOfficerName"));
        }
        if (
            (oldPermit.getContactOfficerEmailAddress() != null &&
                !oldPermit.getContactOfficerEmailAddress().equals(permitRequestDTO.getContactOfficerEmailAddress())) ||
            (oldPermit.getContactOfficerEmailAddress() == null && permitRequestDTO.getContactOfficerEmailAddress() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "ContactOfficerEmailAddress"));
        }
        if (
            (oldPermit.getContactOfficerMobileNumber() != null &&
                !oldPermit.getContactOfficerMobileNumber().equals(permitRequestDTO.getContactOfficerMobileNumber())) ||
            (oldPermit.getContactOfficerMobileNumber() == null && permitRequestDTO.getContactOfficerMobileNumber() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "ContactOfficerMobileNumber"));
        }
        if (
            (oldPermit.getVatRegistrationNumber() != null &&
                !oldPermit.getVatRegistrationNumber().equals(permitRequestDTO.getVatRegistrationNumber())) ||
            (oldPermit.getVatRegistrationNumber() == null && permitRequestDTO.getVatRegistrationNumber() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "VatRegistrationNumber"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfOwnedMaritimeMediumsWorld() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getNoOfOwnedMaritimeMediumsWorld()
                    .equals(permitRequestDTO.getLicenseRequest().getNoOfOwnedMaritimeMediumsWorld())) ||
            (oldPermit.getLicenseRequest().getNoOfOwnedMaritimeMediumsWorld() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfOwnedMaritimeMediumsWorld() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfOwnedMaritimeMediumsWorld"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfOwnedMaritimeMediumsKsa() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getNoOfOwnedMaritimeMediumsKsa()
                    .equals(permitRequestDTO.getLicenseRequest().getNoOfOwnedMaritimeMediumsKsa())) ||
            (oldPermit.getLicenseRequest().getNoOfOwnedMaritimeMediumsKsa() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfOwnedMaritimeMediumsKsa() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfOwnedMaritimeMediumsKsa"));
        }
        if (
            (oldPermit.getLicenseRequest().getMaritimeTourismAgent() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getMaritimeTourismAgent()
                    .getId()
                    .equals(permitRequestDTO.getLicenseRequest().getMaritimeTourismAgent().getId())) ||
            (oldPermit.getLicenseRequest().getMaritimeTourismAgent() == null &&
                permitRequestDTO.getLicenseRequest().getMaritimeTourismAgent() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MaritimeTourismAgent"));
        }
        if (
            (oldPermit.getMinistryOfHumanResourcesNumber() != null &&
                !oldPermit.getMinistryOfHumanResourcesNumber().equals(permitRequestDTO.getMinistryOfHumanResourcesNumber())) ||
            (oldPermit.getMinistryOfHumanResourcesNumber() == null && permitRequestDTO.getMinistryOfHumanResourcesNumber() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MinistryOfHumanResourcesNumber"));
        }
        if (
            (oldPermit.getLicenseRequest().getMarinaOwnerNameAr() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getMarinaOwnerNameAr()
                    .equals(permitRequestDTO.getLicenseRequest().getMarinaOwnerNameAr())) ||
            (oldPermit.getLicenseRequest().getMarinaOwnerNameAr() == null && permitRequestDTO.getLicenseRequest() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MarinaOwnerNameAr"));
        }
        if (
            (oldPermit.getLicenseRequest().getMarinaCrNumber() != null &&
                !oldPermit.getLicenseRequest().getMarinaCrNumber().equals(permitRequestDTO.getLicenseRequest().getMarinaCrNumber())) ||
            (oldPermit.getLicenseRequest().getMarinaCrNumber() == null && permitRequestDTO.getLicenseRequest().getMarinaCrNumber() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MarinaCrNumber"));
        }
        if (
            (oldPermit.getLicenseRequest().getMarinaCrNameAr() != null &&
                !oldPermit.getLicenseRequest().getMarinaCrNameAr().equals(permitRequestDTO.getLicenseRequest().getMarinaCrNameAr())) ||
            (oldPermit.getLicenseRequest().getMarinaCrNameAr() == null && permitRequestDTO.getLicenseRequest().getMarinaCrNameAr() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MarinaCrNameAr"));
        }
        if (
            (oldPermit.getLicenseRequest().getMarinaCrNameEn() != null &&
                !oldPermit.getLicenseRequest().getMarinaCrNameEn().equals(permitRequestDTO.getLicenseRequest().getMarinaCrNameEn())) ||
            (oldPermit.getLicenseRequest().getMarinaCrNameEn() == null && permitRequestDTO.getLicenseRequest().getMarinaCrNameEn() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "MarinaCrNameEn"));
        }
        if (
            (oldPermit.getLicenseRequest().getNumberOfDocks() != null &&
                !oldPermit.getLicenseRequest().getNumberOfDocks().equals(permitRequestDTO.getLicenseRequest().getNumberOfDocks())) ||
            (oldPermit.getLicenseRequest().getNumberOfDocks() == null && permitRequestDTO.getLicenseRequest().getNumberOfDocks() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NumberOfDocks"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfControlTowers() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getNoOfControlTowers()
                    .equals(permitRequestDTO.getLicenseRequest().getNoOfControlTowers())) ||
            (oldPermit.getLicenseRequest().getNoOfControlTowers() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfControlTowers() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfControlTowers"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfMarinaFacilities() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getNoOfMarinaFacilities()
                    .equals(permitRequestDTO.getLicenseRequest().getNoOfMarinaFacilities())) ||
            (oldPermit.getLicenseRequest().getNoOfMarinaFacilities() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfMarinaFacilities() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfMarinaFacilities"));
        }
        if (
            (oldPermit.getLicenseRequest().getBoatsCapacity() != null &&
                !oldPermit.getLicenseRequest().getBoatsCapacity().equals(permitRequestDTO.getLicenseRequest().getBoatsCapacity())) ||
            (oldPermit.getLicenseRequest().getBoatsCapacity() == null && permitRequestDTO.getLicenseRequest().getBoatsCapacity() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "BoatsCapacity"));
        }
        if (
            (oldPermit.getLicenseRequest().getYachtsCapacity() != null &&
                !oldPermit.getLicenseRequest().getYachtsCapacity().equals(permitRequestDTO.getLicenseRequest().getYachtsCapacity())) ||
            (oldPermit.getLicenseRequest().getYachtsCapacity() == null && permitRequestDTO.getLicenseRequest().getYachtsCapacity() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "YachtsCapacity"));
        }
        if (
            (oldPermit.getLicenseRequest().getOtherMaritimeMediumCapacity() != null &&
                !oldPermit
                    .getLicenseRequest()
                    .getOtherMaritimeMediumCapacity()
                    .equals(permitRequestDTO.getLicenseRequest().getOtherMaritimeMediumCapacity())) ||
            (oldPermit.getLicenseRequest().getOtherMaritimeMediumCapacity() == null &&
                permitRequestDTO.getLicenseRequest().getOtherMaritimeMediumCapacity() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "OtherMaritimeMediumCapacity"));
        }
        if (
            (oldPermit.getLicenseRequest().getNoOfFuelStations() != null &&
                !oldPermit.getLicenseRequest().getNoOfFuelStations().equals(permitRequestDTO.getLicenseRequest().getNoOfFuelStations())) ||
            (oldPermit.getLicenseRequest().getNoOfFuelStations() == null &&
                permitRequestDTO.getLicenseRequest().getNoOfFuelStations() != null)
        ) {
            permitChangesList.add(new PermitChanges().getChangeObj(oldPermit.getRequestNumber(), "NoOfFuelStations"));
        }

        permitChangesRepository.saveAll(permitChangesList);
    }

    private void savePermitRequestStatus(PermitRequest permitRequest) {
        PermitRequestStatus permitRequestStatus = new PermitRequestStatus();
        permitRequestStatus.setPermitRequest(permitRequest);
        permitRequestStatus.setRequestStatus(permitRequest.getRequestStatus());
        permitRequestStatus.setRequestNote(permitRequest.getRequestNote());
        permitRequestStatus.setRequestNumber(permitRequest.getRequestNumber());
        permitRequestStatus.setAssignedEmployeeName(permitRequest.getAssignedEmployeeName());
        permitRequestStatus.setAssignedEmployee(permitRequest.getAssignedEmployee());
        permitRequestStatusRepository.save(permitRequestStatus);
    }

    private void savePermitRequestHistory(PermitRequest permitRequest) {
        PermitRequestHistory permitRequestHistory = new PermitRequestHistory();

        permitRequestHistory.setRequestStatus(permitRequest.getRequestStatus());
        permitRequestHistory.setRequestNote(permitRequest.getRequestNote());
        permitRequestHistory.setRequestNumber(permitRequest.getRequestNumber());
        permitRequestHistory.setAssignedEmployeeName(permitRequest.getAssignedEmployeeName());
        permitRequestHistory.setAssignedEmployee(permitRequest.getAssignedEmployee());

        permitRequestHistory.setSecondaryCompany(permitRequest.getSecondaryCompany());
        permitRequestHistory.setContactOfficerEmailAddress(permitRequest.getContactOfficerEmailAddress());
        permitRequestHistory.setContactOfficerMobileNumber(permitRequest.getContactOfficerMobileNumber());
        permitRequestHistory.setContactOfficerName(permitRequest.getContactOfficerName());
        permitRequestHistory.setDisclaimerChecked(permitRequest.getDisclaimerChecked());
        permitRequestHistory.setEczaLicenseNumber(permitRequest.getEczaLicenseNumber());
        permitRequestHistory.setFees(permitRequest.getFees());
        permitRequestHistory.setForeignersLaborCount(permitRequest.getForeignersLaborCount());
        //        permitRequestHistory.setGeneralInformationOfLabor(permitRequest.getGeneralInformationOfLabor());
        permitRequestHistory.setMisaLicenseNumber(permitRequest.getMisaLicenseNumber());
        permitRequestHistory.setRateRange(permitRequest.getRateRange());
        permitRequestHistory.setSaudiLaborCount(permitRequest.getSaudiLaborCount());
        permitRequestHistory.setSaudizationLevel(permitRequest.getSaudizationLevel());
        permitRequestHistory.setSubmitDate(permitRequest.getSubmitDate());
        permitRequestHistory.setTotalPriceWithoutVat(permitRequest.getTotalPriceWithoutVat());
        permitRequestHistory.setTotalPriceWithVat(permitRequest.getTotalPriceWithVat());
        permitRequestHistory.setVatRegistrationNumber(permitRequest.getVatRegistrationNumber());
        permitRequestHistory.setCompany(permitRequest.getCompany());
        permitRequestHistory.setLicenseProvider(permitRequest.getLicenseProvider());
        permitRequestHistory.setServiceType(permitRequest.getServiceType());
        permitRequestHistory.setWorkLocation(permitRequest.getWorkLocation());

        permitRequestHistory.setLiabilityChecked(permitRequest.getLiabilityChecked());
        permitRequestHistory.setExceptionalApproval(permitRequest.getExceptionalApproval());
        permitRequestHistory.setRequiredPayFees(permitRequest.getRequiredPayFees());
        permitRequestHistory.setPaymentPeriodDays(permitRequest.getPaymentPeriodDays());
        permitRequestHistory.setPaymentPeriodDate(permitRequest.getPaymentPeriodDate());
        permitRequestHistory.setUuid(permitRequest.getUuid());
        // permitRequestHistory.setAttachments(permitRequest.getAttachments());
        LicenseRequestHistory licenseRequestHistory = prepareLicenseRequestHistory(permitRequest);
        permitRequestHistory.setLicenseRequestHistory(licenseRequestHistory);
        permitRequestHistoryRepository.save(permitRequestHistory);
    }

    private LicenseRequestHistory prepareLicenseRequestHistory(PermitRequest permitRequest) {
        LicenseRequestHistory licenseRequestHistory = new LicenseRequestHistory();
        LicenseRequest licenseRequest = permitRequest.getLicenseRequest();
        licenseRequestHistory.setBoatsCapacity(licenseRequest.getBoatsCapacity());
        licenseRequestHistory.setDockDepth(licenseRequest.getDockDepth());
        licenseRequestHistory.setDockLength(licenseRequest.getDockLength());
        licenseRequestHistory.setMarinaCrNameAr(licenseRequest.getMarinaCrNameAr());
        licenseRequestHistory.setMarinaCrNumber(licenseRequest.getMarinaCrNumber());
        licenseRequestHistory.setMarinaCrNameEn(licenseRequest.getMarinaCrNameEn());
        licenseRequestHistory.setMarinaOwnerName(licenseRequest.getMarinaOwnerNameAr());
        licenseRequestHistory.setNoOfControlTowers(licenseRequest.getNoOfControlTowers());
        licenseRequestHistory.setNoOfCountryCompanyOpr(licenseRequest.getNoOfCountryCompanyOpr());
        licenseRequestHistory.setNoOfElectricChargingStations(licenseRequest.getNoOfElectricChargingStations());
        licenseRequestHistory.setNoOfFuelStations(licenseRequest.getNoOfFuelStations());
        licenseRequestHistory.setNoOfManagedMaritimeMediums(licenseRequest.getNoOfManagedMaritimeMediums());
        licenseRequestHistory.setNoOfManagedMaritimeMediumsGeneral(licenseRequest.getNoOfManagedMaritimeMediumsGeneral());
        licenseRequestHistory.setNoOfMarinaFacilities(licenseRequest.getNoOfMarinaFacilities());
        licenseRequestHistory.setNoOfWasteStations(licenseRequest.getNoOfWasteStations());
        licenseRequestHistory.setNoOfWasteStationsPerPlatform(licenseRequest.getNoOfWasteStationsPerPlatform());
        licenseRequestHistory.setNoOfYearsExperience(licenseRequest.getNoOfYearsExperience());
        licenseRequestHistory.setNumberOfDocks(licenseRequest.getNumberOfDocks());
        licenseRequestHistory.setOtherMaritimeMediumCapacity(licenseRequest.getOtherMaritimeMediumCapacity());
        licenseRequestHistory.setRequestedLicenseDuration(licenseRequest.getRequestedLicenseDuration());
        licenseRequestHistory.setYachtsCapacity(licenseRequest.getYachtsCapacity());
        licenseRequestHistory.setMarinaLocation(licenseRequest.getMarinaLocation());
        licenseRequestHistory.setMaritimeTourismAgent(licenseRequest.getMaritimeTourismAgent());

        licenseRequestHistory.setLicenseRequestFuelTypes(prepareLicenseRequestFuelTypes(licenseRequest));
        licenseRequestHistory.setLicenseRequestMaritimeMediums(prepareLicenseRequestMaritimeMediums(licenseRequest));
        licenseRequestHistory.setLicenseRequestMarineTourServices(prepareLicenseRequestMarineTourServices(licenseRequest));
        licenseRequestHistory.setLicenseRequestCountries(prepareLicenseRequestCountries(licenseRequest));
        licenseRequestHistory.setLicenseRequestMaritimeServices(prepareLicenseRequestMaritimeServices(licenseRequest));
        licenseRequestHistory.setLicenseRequestProvidedLanguages(prepareLicenseRequestProvidedLanguages(licenseRequest));
        licenseRequestHistory.setLicenseRequestDocks(prepareLicenseRequestDocks(licenseRequest));

        return licenseRequestHistory;
    }

    private Set<LicenseRequestProvidedLanguagesHistory> prepareLicenseRequestProvidedLanguages(LicenseRequest licenseRequest) {
        Set<LicenseRequestProvidedLanguagesHistory> licenseRequestProvidedLanguagesHistories = new HashSet<>();
        for (LicenseRequestProvidedLanguages licenseRequestProvidedLanguages : licenseRequest.getLicenseRequestProvidedLanguages()) {
            LicenseRequestProvidedLanguagesHistory licenseRequestProvidedLanguagesHistory = new LicenseRequestProvidedLanguagesHistory();
            licenseRequestProvidedLanguagesHistory.setProvidedLanguages(licenseRequestProvidedLanguages.getProvidedLanguages());
            licenseRequestProvidedLanguagesHistories.add(licenseRequestProvidedLanguagesHistory);
        }
        return licenseRequestProvidedLanguagesHistories;
    }

    private Set<LicenseRequestDocksHistory> prepareLicenseRequestDocks(LicenseRequest licenseRequest) {
        Set<LicenseRequestDocksHistory> licenseRequestDocksHistories = new HashSet<>();
        for (LicenseRequestDocks licenseRequestDocks : licenseRequest.getLicenseRequestDocks()) {
            LicenseRequestDocksHistory licenseRequestDocksHistory = new LicenseRequestDocksHistory();
            licenseRequestDocksHistory.setDocks(licenseRequestDocks.getDocks());
            licenseRequestDocksHistory.setLicenseRequestHistory(licenseRequestDocksHistory.getLicenseRequestHistory());
            licenseRequestDocksHistories.add(licenseRequestDocksHistory);
        }
        return licenseRequestDocksHistories;
    }

    private Set<LicenseRequestMaritimeServicesHistory> prepareLicenseRequestMaritimeServices(LicenseRequest licenseRequest) {
        Set<LicenseRequestMaritimeServicesHistory> licenseRequestMaritimeServicesHistories = new HashSet<>();
        for (LicenseRequestMaritimeServices licenseRequestMaritimeServices : licenseRequest.getLicenseRequestMaritimeServices()) {
            LicenseRequestMaritimeServicesHistory licenseRequestCountryHistory = new LicenseRequestMaritimeServicesHistory();
            licenseRequestMaritimeServices.setMaritimeTourismServices(licenseRequestMaritimeServices.getMaritimeTourismServices());
            licenseRequestMaritimeServicesHistories.add(licenseRequestCountryHistory);
        }
        return licenseRequestMaritimeServicesHistories;
    }

    private Set<LicenseRequestCountryHistory> prepareLicenseRequestCountries(LicenseRequest licenseRequest) {
        Set<LicenseRequestCountryHistory> licenseRequestCountryHistories = new HashSet<>();
        for (LicenseRequestCountry licenseRequestCountry : licenseRequest.getLicenseRequestCountries()) {
            LicenseRequestCountryHistory licenseRequestCountryHistory = new LicenseRequestCountryHistory();
            licenseRequestCountryHistory.setCountryOfWork(licenseRequestCountry.getCountryOfWork());
            licenseRequestCountryHistories.add(licenseRequestCountryHistory);
        }
        return licenseRequestCountryHistories;
    }

    private Set<LicenseRequestMarineTourServicesHistory> prepareLicenseRequestMarineTourServices(LicenseRequest licenseRequest) {
        Set<LicenseRequestMarineTourServicesHistory> licenseRequestMarineTourServicesList = new HashSet<>();
        for (LicenseRequestMarineTourServices LicenseRequestMarineTourServices : licenseRequest.getLicenseRequestMarineTourServices()) {
            LicenseRequestMarineTourServicesHistory licenseRequestMarineTourService = new LicenseRequestMarineTourServicesHistory();
            licenseRequestMarineTourService.setMarineTourOperatorServices(LicenseRequestMarineTourServices.getMarineTourOperatorServices());
            licenseRequestMarineTourServicesList.add(licenseRequestMarineTourService);
        }
        return licenseRequestMarineTourServicesList;
    }

    private Set<LicenseRequestMaritimeMediumHistory> prepareLicenseRequestMaritimeMediums(LicenseRequest licenseRequest) {
        Set<LicenseRequestMaritimeMediumHistory> licenseRequestMaritimeMediumHistories = new HashSet<>();
        for (LicenseRequestMaritimeMedium licenseRequestMaritimeMedium : licenseRequest.getLicenseRequestMaritimeMediums()) {
            LicenseRequestMaritimeMediumHistory licenseRequestMaritimeMediumHistory = new LicenseRequestMaritimeMediumHistory();
            licenseRequestMaritimeMediumHistory.setMaritimeMedium(licenseRequestMaritimeMedium.getMaritimeMedium());
            licenseRequestMaritimeMediumHistory.setOtherValue(licenseRequestMaritimeMedium.getOtherValue());
            licenseRequestMaritimeMediumHistories.add(licenseRequestMaritimeMediumHistory);
        }
        return licenseRequestMaritimeMediumHistories;
    }

    private Set<LicenseRequestFuelTypesHistory> prepareLicenseRequestFuelTypes(LicenseRequest licenseRequest) {
        Set<LicenseRequestFuelTypesHistory> licenseRequestFuelTypesHistories = new HashSet<>();
        for (LicenseRequestFuelTypes licenseRequestFuelTypes : licenseRequest.getLicenseRequestFuelTypes()) {
            LicenseRequestFuelTypesHistory licenseRequestFuelTypesHistory = new LicenseRequestFuelTypesHistory();
            licenseRequestFuelTypesHistory.setFuelTypes(licenseRequestFuelTypes.getFuelTypes());
            licenseRequestFuelTypesHistories.add(licenseRequestFuelTypesHistory);
        }
        return licenseRequestFuelTypesHistories;
    }

    /**
     * Update a permitRequest.
     *
     * @param permitRequestDTO the entity to save.
     * @return the persisted entity.
     */
    public PermitRequestDTO update(PermitRequestDTO permitRequestDTO) {
        log.debug("Request to update PermitRequest : {}", permitRequestDTO);
        PermitRequest permitRequest = permitRequestMapper.toEntity(permitRequestDTO);
        permitRequest = permitRequestRepository.save(permitRequest);
        return permitRequestMapper.toDto(permitRequest);
    }

    public PermitRequest updateWFInfo(PermitRequestDTO permitRequestDTO, AdminUserDTO officer, String role) {
        log.debug("Request to update PermitRequest work flow: {}", permitRequestDTO);
        PermitRequest permitRequest = null;
        Optional<PermitRequest> permitRequestOptional = permitRequestRepository.findById(permitRequestDTO.getId());
        if (permitRequestOptional.isPresent()) {
            permitRequest = permitRequestOptional.get();
            permitRequest.setRequiredPayFees(permitRequestDTO.getRequiredPayFees());
            permitRequest.setExceptionalApproval(permitRequestDTO.getExceptionalApproval());
            permitRequest.setRequestStatus(permitRequestDTO.getRequestStatus());
            permitRequest.setRequestNote(permitRequestDTO.getRequestNote());
            if (permitRequestDTO.getRequestStatus() == StatusType.PENDING_PAYMENT) {
                permitRequest.setPaymentPeriodDate(
                    Instant.now().plus(permitRequest.getServiceType().getPaymentPeriodDays(), ChronoUnit.DAYS)
                );
            }

            if (permitRequestDTO.getRequestStatus() == StatusType.COMPLETED) {
                Calendar currentDate = Calendar.getInstance();
                permitRequest.setIssueDate(currentDate.getTime());
                int durationYears =
                    switch (permitRequestDTO.getLicenseRequest().getRequestedLicenseDuration()) {
                        case TWO_YEAR -> 2;
                        case THREE_YEAR -> 3;
                        default -> 1;
                    };
                currentDate.add(Calendar.YEAR, durationYears);
                permitRequest.setExpiryDate(currentDate.getTime());
            }

            User assignedUser = null;
            if (officer != null) {
                assignedUser = userRepository.findById(officer.getId()).orElse(null);
            }
            if (assignedUser == null) {
                Long currentUserId = SecurityUtils.getCurrentUser().getUserID();
                assignedUser = userRepository.findById(currentUserId).orElse(null);
            }
            if (
                assignedUser != null &&
                !permitRequest.getRequestStatus().equals(StatusType.PENDING_INSPECTION) &&
                !permitRequest.getRequestStatus().equals(StatusType.PENDING_MANAGEMENT_LICENSING) &&
                !permitRequest.getRequestStatus().equals(StatusType.PENDING_REPORT_APPROVAL)
            ) {
                permitRequest.setAssignedEmployee(assignedUser);
                permitRequest.setAssignedEmployeeName(assignedUser.getFirstName());
                permitRequest.setAssignedEmployeeRole(role);
            } else {
                permitRequest.setAssignedEmployee(null);
                permitRequest.setAssignedEmployeeName(null);
                permitRequest.setAssignedEmployeeRole(role);
            }
            permitRequest = permitRequestRepository.save(permitRequest);
            savePermitRequestStatus(permitRequest);

            mailService.sendEmailFromTemplate(assignedUser, EmailTemplate.UPDATE_RQ_STATUS, "email.rq.status.title", permitRequest);
        }
        return permitRequest;
    }

    /**
     * Partially update a permitRequest.
     *
     * @param permitRequestDTO the entity to update partially.
     * @return the persisted entity.
     */
    public Optional<PermitRequestDTO> partialUpdate(PermitRequestDTO permitRequestDTO) {
        log.debug("Request to partially update PermitRequest : {}", permitRequestDTO);

        return permitRequestRepository
            .findById(permitRequestDTO.getId())
            .map(existingPermitRequest -> {
                permitRequestMapper.partialUpdate(existingPermitRequest, permitRequestDTO);

                return existingPermitRequest;
            })
            .map(permitRequestRepository::save)
            .map(permitRequestMapper::toDto);
    }

    /**
     * Get one permitRequest by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<PermitRequest> findOne(Long id) {
        log.debug("Request to get PermitRequest : {}", id);
        Optional<PermitRequest> permitRequest = Optional.empty();
        if (SecurityUtils.isServiceProvider()) {
            permitRequest = permitRequestRepository.findByIdAndCompany_Id(id, companyService.getRegisteredCompany().getId());
            permitRequest.get().getAttachments().size();
        } else {
            permitRequest = permitRequestRepository.findById(id);
            permitRequest.get().getAttachments().size();
        }
        return permitRequest;
    }

    @Transactional(readOnly = true)
    public Optional<PermitRequest> findAll(Long id) {
        log.debug("Request to get PermitRequest : {}", id);
        return permitRequestRepository.findAllById(id);
    }

    /**
     * Delete the permitRequest by id.
     *
     * @param id the id of the entity.
     */
    public void delete(Long id) {
        log.debug("Request to delete PermitRequest : {}", id);
        if (SecurityUtils.isServiceProvider()) {
            permitRequestRepository.deleteByIdAndCompany_Id(id, companyService.getRegisteredCompany().getId());
        } else {
            permitRequestRepository.deleteById(id);
        }
    }

    public byte[] exportPermitRequestByUUID(String permitRequestUUID) throws IOException, WriterException {
        Optional<PermitRequest> optionalPermitRequest = permitRequestRepository.findOneByUuid(UUID.fromString(permitRequestUUID));
        if (optionalPermitRequest.isEmpty()) {
            return null;
        }
        return exportPermitRequest(optionalPermitRequest.get());
    }

    public byte[] exportPermitRequestById(Long permitRequestId) throws IOException, WriterException {
        Optional<PermitRequest> optionalPermitRequest = permitRequestRepository.findById(permitRequestId);
        if (optionalPermitRequest.isEmpty()) {
            return null;
        }
        return exportPermitRequest(optionalPermitRequest.get());
    }

    private byte[] exportPermitRequest(PermitRequest permitRequest) throws IOException, WriterException {
        //        1,Maritime Tourism Agent
        //        2,Large Yacht Chartering License
        //        3,Marina Operator License
        //        4,Marine Tour Operator License
        //        5,Cruise Ships Operator License
        String templateFileName = null;
        if (Objects.nonNull(permitRequest.getServiceType())) {
            int serviceTypeId = Math.toIntExact(permitRequest.getServiceType().getId());
            templateFileName =
                switch (serviceTypeId) {
                    case 1 -> "Maritime Tourism Agent";
                    case 2 -> "Large Yacht Chartering License";
                    case 3 -> "Marina Operator License";
                    case 4 -> "Marine Tour Operator License";
                    case 5 -> "Cruise Ships Operator License";
                    case 8 -> "Beach Operator License";
                    default -> templateFileName;
                };
        }

        if (templateFileName == null) {
            return null;
        }
        String templatePath = "templates/reports";
        String filePath = PdfGeneratorUtil.getFolderPath(templatePath);
        String content = "Constants.EMPTY";
        content = PdfGeneratorUtil.loadTemplate(filePath, content, templateFileName + ".html");
        content = fillContractInfo(permitRequest, content, templateFileName);
        return PdfGeneratorUtil.convertToByte(
            htmlToPdf.getPath(),
            filePath,
            templatePath,
            content,
            false,
            "landscape",
            htmlToPdf.getPdfParams()
        );
    }

    private String fillContractInfo(PermitRequest permitRequest, String content, String templateFileName)
        throws IOException, WriterException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+3"));
        String requestNumber = permitRequest.getLicenceNumber();
        String issueDate = Objects.nonNull(permitRequest.getIssueDate()) ? dateFormat.format(permitRequest.getIssueDate()) : null;
        String expiryDate = Objects.nonNull(permitRequest.getExpiryDate()) ? dateFormat.format(permitRequest.getExpiryDate()) : null;
        String organizationName = Objects.nonNull(permitRequest.getCompany()) ? permitRequest.getCompany().getCrName() : null;
        CrInfoWathqResponse companyEnCrInfoWathqResponse = wathqApiResponseService.getCompanyCrInfoWathqResponse(
            permitRequest.getCompany().getCrNumber(),
            LanguageEnum.EN
        );
        CrInfoWathqResponse companyArCrInfoWathqResponse = wathqApiResponseService.getCompanyCrInfoWathqResponse(
            permitRequest.getCompany().getCrNumber(),
            LanguageEnum.AR
        );
        String organizationNameEn = companyEnCrInfoWathqResponse != null ? companyEnCrInfoWathqResponse.getName() : "";
        String cityEn = companyEnCrInfoWathqResponse != null ? companyEnCrInfoWathqResponse.getAddress().getNational().getCity() : "";
        String addressEn = companyEnCrInfoWathqResponse != null
            ? companyEnCrInfoWathqResponse.getAddress().getNational().getBuildingNumber() +
            ", " +
            companyEnCrInfoWathqResponse.getAddress().getNational().getStreetName()
            : "";
        String districtEn = companyEnCrInfoWathqResponse != null
            ? companyEnCrInfoWathqResponse.getAddress().getNational().getDistrict()
            : "";
        String maritimeMediumNameAr = String.join(
            ", ",
            permitRequest
                .getLicenseRequest()
                .getLicenseRequestMaritimeMediums()
                .stream()
                .map(licenseRequestMaritimeMedium -> licenseRequestMaritimeMedium.getMaritimeMedium().getNameAr())
                .collect(Collectors.toList())
        );
        String maritimeMediumNameEn = String.join(
            ", ",
            permitRequest
                .getLicenseRequest()
                .getLicenseRequestMaritimeMediums()
                .stream()
                .map(licenseRequestMaritimeMedium -> licenseRequestMaritimeMedium.getMaritimeMedium().getNameEn())
                .collect(Collectors.toList())
        );
        String crNumber = Objects.nonNull(permitRequest.getCompany()) ? permitRequest.getCompany().getCrNumber() : null;
        String city = Objects.nonNull(permitRequest.getCompany()) && Objects.nonNull(permitRequest.getCompany().getNationalAddress())
            ? permitRequest.getCompany().getNationalAddress().getCity()
            : null;
        String district = Objects.nonNull(permitRequest.getCompany()) && Objects.nonNull(permitRequest.getCompany().getNationalAddress())
            ? permitRequest.getCompany().getNationalAddress().getDistrict()
            : null;
        String address = Objects.nonNull(permitRequest.getCompany()) && Objects.nonNull(permitRequest.getCompany().getGeneralAddress())
            ? permitRequest.getCompany().getNationalAddress().getBuildingNumber() +
            ", " +
            permitRequest.getCompany().getNationalAddress().getStreetName()
            : null;

        //        List<CompanyActivity> sortedCompanyActivityList = new ArrayList<>();
        //        if (Objects.nonNull(permitRequest.getCompany())) {
        //            List<CompanyActivity> companyActivities = companyActivityService.getCompanyActivities(permitRequest.getCompany().getId());
        //            if (Objects.nonNull(companyActivities)) {
        //                sortedCompanyActivityList = companyActivities.stream().sorted(Comparator.comparingLong(CompanyActivity::getId)).toList();
        //            }
        //        }

        List<Activity> sortedCompanyActivityList = new ArrayList<>();
        if (Objects.nonNull(companyArCrInfoWathqResponse) && !CollectionUtils.isEmpty(companyArCrInfoWathqResponse.getActivities())) {
            List<Activity> companyActivities = companyArCrInfoWathqResponse.getActivities();
            sortedCompanyActivityList = companyActivities.stream().sorted(Comparator.comparing(Activity::getId)).toList();
        }
        StringBuilder servicesAr = new StringBuilder();
        StringBuilder servicesEn = new StringBuilder();
        if (Objects.nonNull(permitRequest.getLicenseRequest().getLicenseRequestMaritimeServices())) {
            permitRequest
                .getLicenseRequest()
                .getLicenseRequestMaritimeServices()
                .forEach(licenseRequestMaritimeServices -> {
                    servicesAr.append(licenseRequestMaritimeServices.getMaritimeTourismServices().getNameAr());
                    servicesAr.append(", ");
                    servicesEn.append(licenseRequestMaritimeServices.getMaritimeTourismServices().getNameEn());
                    servicesEn.append(", ");
                });
        }
        Map<String, String> map = new HashMap<>();

        map.put("${licenseNumber}", requestNumber);
        map.put("${issueDate}", issueDate);
        map.put("${expiryDate}", expiryDate);
        map.put("${organizationName}", organizationName);
        map.put("${organizationNameEn}", organizationNameEn);
        map.put("${crNumber}", crNumber);
        map.put("${city}", city);
        map.put("${cityEn}", cityEn);
        map.put("${district}", district);
        map.put("${districtEn}", districtEn);
        map.put("${address}", address);
        map.put("${addressEn}", addressEn);
        map.put("${vesselType}", maritimeMediumNameAr);
        map.put("${vesselTypeEn}", maritimeMediumNameEn);
        map.put("${servicesAr}", servicesAr.toString());
        map.put("${servicesEn}", servicesEn.toString());
        String baseURL = StringUtils.substringBeforeLast(StringUtils.substringBeforeLast(getCurrentAPIURL(), "/"), "/");
        String qrCodeLink = baseURL + "/report/" + permitRequest.getUuid();
        System.out.println("current API ==> " + qrCodeLink);
        byte[] qrCodeImage = QRCodeGenerator.getQRCodeImage(qrCodeLink, 150, 150);
        String base64EncodedImage = Base64.getEncoder().encodeToString(qrCodeImage);
        map.put("${qrcode}", base64EncodedImage);
        //todo add custom fields for each report
        switch (templateFileName) {
            case "Maritime Tourism Agent":
                break;
            case "Large Yacht Chartering License":
                setCompanyActivities(map, sortedCompanyActivityList);
                break;
            case "Marina Operator License":
                String marinaCrNameEn = "";
                String marinaCrNameAr = "";
                String marinaOwnerNameEn = "";
                String marinaOwnerNameAr = "";
                String marinaCrNumber = "";
                if (Objects.nonNull(permitRequest.getLicenseRequest())) {
                    marinaCrNameEn = permitRequest.getLicenseRequest().getMarinaCrNameEn();
                    marinaCrNameAr = permitRequest.getLicenseRequest().getMarinaCrNameAr();
                    marinaOwnerNameEn = permitRequest.getLicenseRequest().getMarinaOwnerNameEn();
                    marinaOwnerNameAr = permitRequest.getLicenseRequest().getMarinaOwnerNameAr();
                    marinaCrNumber = permitRequest.getLicenseRequest().getMarinaCrNumber();
                }
                map.put("${marinaCRNumber}", marinaCrNumber);
                map.put("${marinaCRNameEN}", marinaCrNameEn);
                map.put("${marinaCRNameAR}", marinaCrNameAr);
                // Todo en/ar name!!
                map.put("${ownerNameAR}", marinaOwnerNameAr);
                map.put("${ownerNameEN}", marinaOwnerNameEn);
                break;
            case "Marine Tour Operator License":
                setCompanyActivities(map, sortedCompanyActivityList);
                break;
            case "Cruise Ships Operator License":
                setCompanyActivities(map, sortedCompanyActivityList);
                break;
        }

        return PdfGeneratorUtil.replaceInString(content, map);
    }

    private void setCompanyActivities(Map<String, String> map, List<Activity> sortedCompanyActivityList) {
        if (!sortedCompanyActivityList.isEmpty()) {
            map.put("${mainActivity}", sortedCompanyActivityList.get(0).getName());
        } else {
            map.put("${mainActivity}", "");
            map.put("${subActivity}", "");
        }
        if (sortedCompanyActivityList.size() >= 2) {
            map.put("${subActivity}", sortedCompanyActivityList.get(1).getName());
        } else {
            map.put("${subActivity}", "");
        }
    }

    private String getCurrentAPIURL() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletRequest request = servletRequestAttributes.getRequest();
        StringBuffer url = request.getRequestURL();
        return url.toString();
    }

    public List<AdminUserDTO> getLicensingOfficers() {
        List<AdminUserDTO> licensingOfficers = new ArrayList<>();
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        User loggedInUser = userRepository.findById(rsaUser.getUserID()).get();
        AdminUserDTO loggedInUserDTO = userMapper.userToAdminUserDTO(loggedInUser);

        boolean isLicensingOfficer = SecurityUtils.isLicensingOfficer();
        boolean isLicensingSupervisor = SecurityUtils.isLicensingSupervisor();

        if (isLicensingOfficer && !isLicensingSupervisor) {
            licensingOfficers.add(loggedInUserDTO);
        } else {
            List<User> users = userRepository.findAllByAuthority(List.of(AuthoritiesConstants.SRSA_LICENSING_OFFICER));
            users.forEach(user -> licensingOfficers.add(userMapper.userToAdminUserDTO(user)));
        }
        return licensingOfficers;
    }

    public List<AdminUserDTO> getInspectionOfficers() {
        List<AdminUserDTO> inspectionOfficers = new ArrayList<>();
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        User loggedInUser = userRepository.findById(rsaUser.getUserID()).get();
        AdminUserDTO loggedInUserDTO = userMapper.userToAdminUserDTO(loggedInUser);

        boolean isInspectionOfficer = SecurityUtils.isInspectionOfficer();
        boolean isInspectionSupervisor = SecurityUtils.isInspectionSupervisor();

        if (isInspectionOfficer && !isInspectionSupervisor) {
            inspectionOfficers.add(loggedInUserDTO);
        } else {
            List<User> users = userRepository.findAllByAuthority(List.of(AuthoritiesConstants.SRSA_INSPECTION_OFFICER));
            users.forEach(user -> inspectionOfficers.add(userMapper.userToAdminUserDTO(user)));
        }
        return inspectionOfficers;
    }

    public List<AdminUserDTO> getLicensingManagers() {
        List<String> authority = new ArrayList<>();
        authority.add(0, AuthoritiesConstants.SRSA_LICENSING_MANAGER);
        List<User> users = userRepository.findAllByAuthority(authority);
        List<AdminUserDTO> dto = new ArrayList<>();
        users.forEach(user -> {
            dto.add(userMapper.userToAdminUserDTO(user));
        });
        return dto;
    }

    public ResponseEntity<Boolean> selectLicensingManagers(PermitRequestDTO permitRequestDTO, AdminUserDTO officer) {
        permitRequestDTO.setRequestStatus(StatusType.PENDING_MANAGEMENT_LICENSING);
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SRSA_LICENSING_MANAGER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> selectLicensingOfficer(PermitRequestDTO permitRequestDTO, AdminUserDTO officer) {
        permitRequestDTO.setRequestStatus(StatusType.UNDER_PROCESS);
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SRSA_LICENSING_OFFICER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> selectInspectionOfficers(PermitRequestDTO permitRequestDTO, AdminUserDTO officer) {
        permitRequestDTO.setRequestStatus(StatusType.UNDER_INSPECTION);
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SRSA_INSPECTION_OFFICER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> returnPermitRequest(PermitRequestDTO permitRequestDTO) {
        String role = "";
        if (
            (SecurityUtils.isLicensingOfficer() || SecurityUtils.isSuperAdmin()) &&
            permitRequestDTO.getRequestStatus().equals(StatusType.UNDER_PROCESS)
        ) {
            permitRequestDTO.setRequestStatus(StatusType.RETURNED_LICENSING_OFFICER);
            role = AuthoritiesConstants.SRSA_LICENSING_OFFICER;
        }
        if (
            (SecurityUtils.isLicensingManager() || SecurityUtils.isSuperAdmin()) &&
            permitRequestDTO.getRequestStatus().equals(StatusType.PENDING_MANAGEMENT_LICENSING)
        ) {
            // Check returnOption to determine the new status
            if (RETURN_TO_LICENSING_SUPERVISOR_OPTION.equals(permitRequestDTO.getReturnOption())) {
                permitRequestDTO.setRequestStatus(StatusType.PENDING_REVIEW);
                role = AuthoritiesConstants.SRSA_LICENSING_SUPERVISOR;
            } else {
                // Default behavior (REQUEST_PROVIDER)
                permitRequestDTO.setRequestStatus(StatusType.RETURNED_LICENSING_MANAGER);
                role = AuthoritiesConstants.SRSA_LICENSING_MANAGER;
            }
        }
        if (
            (SecurityUtils.isInspectionOfficer() || SecurityUtils.isSuperAdmin()) &&
            permitRequestDTO.getRequestStatus().equals(StatusType.UNDER_INSPECTION)
        ) {
            permitRequestDTO.setRequestStatus(StatusType.RETURNED_INSPECTION_OFFICER);
            role = AuthoritiesConstants.SRSA_INSPECTION_OFFICER;
        }
        updateWFInfo(permitRequestDTO, null, role);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> rejectPermitRequest(PermitRequestDTO permitRequestDTO) {
        permitRequestDTO.setRequestStatus(StatusType.REJECTED);
        AdminUserDTO officer = new AdminUserDTO();
        officer.setId(SecurityUtils.getCurrentUser().getUserID());
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SERVICE_PROVIDER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> unAssignPermitRequest(PermitRequestDTO permitRequestDTO) {
        String role = "";

        if (permitRequestDTO.getRequestStatus() == StatusType.UNDER_PROCESS) {
            permitRequestDTO.setRequestStatus(StatusType.PENDING_REVIEW);
            role = AuthoritiesConstants.SRSA_LICENSING_SUPERVISOR;
        } else if (permitRequestDTO.getRequestStatus() == StatusType.UNDER_INSPECTION) {
            permitRequestDTO.setRequestStatus(StatusType.PENDING_INSPECTION);
            role = AuthoritiesConstants.SRSA_INSPECTION_SUPERVISOR;
        }

        AdminUserDTO officer = new AdminUserDTO();
        officer.setId(SecurityUtils.getCurrentUser().getUserID());
        updateWFInfo(permitRequestDTO, officer, role);
        return ResponseEntity.ok().body(true);
    }

    public Resource getAttachmentVisitFile(Long permitVisitId) {
        Optional<VisitAttachment> visitAttachmentOptional = visitAttachmentRepository.findFirstByPermitVisitIdOrderByCreatedDateDesc(
            permitVisitId
        );
        if (visitAttachmentOptional.isEmpty()) {
            log.error("Couldn't find attachment for visit with id {}", permitVisitId);
            return null;
        }
        VisitAttachment visitAttachment = visitAttachmentOptional.get();
        return downloadUtil.getFileAsResourceWithPath(visitAttachment.getUrl(), visitAttachment.getName());
    }

    public ResponseEntity<Boolean> sendToPayPermitRequest(PermitRequestDTO permitRequestDTO, List<MultipartFile> files) {
        permitRequestDTO.setRequestStatus(
            BooleanUtils.isTrue(permitRequestDTO.getExceptionalApproval())
                ? (BooleanUtils.isTrue(permitRequestDTO.getRequiredPayFees()) ? StatusType.PENDING_PAYMENT : StatusType.COMPLETED)
                : StatusType.PENDING_PAYMENT
        );

        AdminUserDTO officer = new AdminUserDTO();
        officer.setId(SecurityUtils.getCurrentUser().getUserID());
        PermitRequest permitRequest = updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SERVICE_PROVIDER);
        if (files != null && !files.isEmpty()) {
            List<AttachmentDTO> attachmentDTOS = new ArrayList<>();
            for (MultipartFile multipartFile : files) {
                try {
                    if (!Objects.requireNonNull(multipartFile.getOriginalFilename()).equalsIgnoreCase("no-file-h")) {
                        attachmentService.saveFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            permitRequest,
                            attachmentDTOS,
                            false,
                            5L
                        );
                    }
                } catch (IOException e) {
                    throw new RuntimeException("failed to save attachment in permit request saving : " + e.getMessage());
                }
            }
            List<Attachment> attachments = attachmentMapper.toEntity(attachmentDTOS);
            for (Attachment attachment : attachments) {
                attachment.permitRequest(permitRequest);
            }
            attachmentRepository.saveAll(attachments);
        }
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> licensingOfficerSubmit(Boolean needVisit, PermitRequestDTO permitRequestDTO) {
        String role = "";
        if (needVisit) {
            permitRequestDTO.setRequestStatus(StatusType.PENDING_INSPECTION);
            //            role = AuthoritiesConstants.SRSA_LICENSING_SUPERVISOR;
        } else {
            permitRequestDTO.setRequestStatus(StatusType.PENDING_MANAGEMENT_LICENSING);
            role = AuthoritiesConstants.SRSA_LICENSING_MANAGER;
        }
        updateWFInfo(permitRequestDTO, null, role);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> sendToInspectionManager(PermitRequestDTO permitRequestDTO, List<MultipartFile> files) {
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        Optional<User> user = userRepository.findOneWithAuthoritiesById(rsaUser.getUserID());
        if (user.isPresent()) {
            permitRequestDTO.setRequestStatus(StatusType.PENDING_REPORT_APPROVAL);
            PermitRequest permitRequest = updateWFInfo(permitRequestDTO, null, AuthoritiesConstants.SRSA_INSPECTION_MANAGER);
            PermitVisit permitVisit = permitVisitRepository
                .findFirstByPermitRequestIdOrderByCreatedDateDesc(permitRequestDTO.getId())
                .get();
            permitVisit.setVisitStatus(VisitStatus.COMPLETED);
            permitVisitRepository.save(permitVisit);
            if (files != null && !files.isEmpty()) {
                List<AttachmentDTO> attachmentDTOS = new ArrayList<>();
                for (MultipartFile multipartFile : files) {
                    try {
                        if (!Objects.requireNonNull(multipartFile.getOriginalFilename()).equalsIgnoreCase("no-file-h")) {
                            // TODO pass allowed max size for files here
                            attachmentService.saveFileAndReturnPath(
                                Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                                multipartFile.getOriginalFilename(),
                                permitRequest,
                                attachmentDTOS,
                                false,
                                null
                            );
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("failed to save attachment in permit request saving : " + e.getMessage());
                    }
                }
                if (!attachmentDTOS.isEmpty()) {
                    attachmentsToEntity(permitRequest, permitVisit, attachmentDTOS);
                }
            }
        }

        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> editVisitReportAndResendToInspectionManager(
        PermitRequestDTO permitRequestDTO,
        List<MultipartFile> files
    ) {
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        Optional<User> user = userRepository.findOneWithAuthoritiesById(rsaUser.getUserID());
        if (user.isPresent()) {
            permitRequestDTO.setRequestStatus(StatusType.PENDING_REPORT_APPROVAL);
            PermitRequest permitRequest = updateWFInfo(permitRequestDTO, null, AuthoritiesConstants.SRSA_INSPECTION_MANAGER);
            PermitVisit permitVisit = permitVisitRepository.findFirstByPermitRequestIdOrderByCreatedDateDesc(permitRequest.getId()).get();
            if (files != null && !files.isEmpty()) {
                List<AttachmentDTO> attachmentDTOS = new ArrayList<>();
                for (MultipartFile multipartFile : files) {
                    try {
                        // TODO pass allowed max size for files here
                        attachmentService.saveFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            permitRequest,
                            attachmentDTOS,
                            false,
                            null
                        );
                    } catch (IOException e) {
                        throw new RuntimeException("failed to save attachment in permit request saving : " + e.getMessage());
                    }
                }
                attachmentsToEntity(permitRequest, permitVisit, attachmentDTOS);
            }
        }

        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> scheduleVisit(PermitRequestDTO permitRequestDTO, LocalDateTime localDateTime) {
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        Optional<User> user = userRepository.findOneWithAuthoritiesById(rsaUser.getUserID());
        if (user.isPresent()) {
            PermitVisit permitVisit = new PermitVisit();
            if (permitRequestDTO.getRequestStatus() == StatusType.PENDING_VISIT) {
                permitVisit = permitVisitRepository.findFirstByPermitRequestIdOrderByCreatedDateDesc(permitRequestDTO.getId()).get();
            }
            permitRequestDTO.setRequestStatus(StatusType.PENDING_VISIT);
            AdminUserDTO officer = new AdminUserDTO();
            officer.setId(SecurityUtils.getCurrentUser().getUserID());
            PermitRequest permitRequest = updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SRSA_INSPECTION_OFFICER);

            permitVisit.setPermitRequest(permitRequest);
            permitVisit.setUser(user.get());
            permitVisit.setVisitDateG(localDateTime);
            permitVisit.setVisitStatus(VisitStatus.SCHEDULED);
            permitVisitRepository.save(permitVisit);
        }

        return ResponseEntity.ok().body(true);
    }

    public List<PermitVisit> getPermitVisit(PermitRequest permitRequest) {
        return permitVisitRepository.findAllByPermitRequestIdOrderByCreatedDateAsc(permitRequest.getId());
    }

    private void attachmentsToEntity(PermitRequest permitRequest, PermitVisit permitVisit, List<AttachmentDTO> attachmentDTOS) {
        List<Attachment> attachments = attachmentMapper.toEntity(attachmentDTOS);
        for (Attachment attachment : attachments) {
            attachment.permitRequest(permitRequest);
            VisitAttachment visitAttachment = new VisitAttachment();
            visitAttachment.setPermitVisit(permitVisit);
            visitAttachment.setUrl(attachment.getDocUrl());
            visitAttachment.setName(attachment.getDocName());
            visitAttachmentRepository.save(visitAttachment);
        }
        attachmentRepository.saveAll(attachments);
    }

    public List<PermitChanges> getPermitChanges(String requestNumber) {
        return permitChangesRepository.findAllByRequestNumber(requestNumber);
    }

    /**
     * Check if a company has a permit request with a specific service type ID
     * This method directly checks for the four conditions: company ID, service type ID, status COMPLETED, and expiry date after now
     *
     * @return true if the company has an active permit request with the specified service type ID, false otherwise
     */
    public boolean srHasYachtRentalFacility() {
        // Service type ID for yacht rental facility
        Long yachtRentalServiceTypeId = 3L; // Todo Assuming 3 is the ID for yacht rental service type, need to be changed
        Company company = companyService.getRegisteredCompany();
        if (Objects.nonNull(company)) {
            return permitRequestRepository.companyHasActiveRequest(company.getId(), yachtRentalServiceTypeId);
        }
        return false;
    }
}
