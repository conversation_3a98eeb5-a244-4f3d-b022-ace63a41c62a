import { Component, inject, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { AlertComponent } from '../../../shared/alert/alert.component';
import { AlertErrorComponent } from '../../../shared/alert/alert-error.component';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import FilterComponent from '../../../shared/filter/filter.component';
import { FormsModule } from '@angular/forms';
import HasAnyAuthorityDirective from '../../../shared/auth/has-any-authority.directive';
import { NgClass, NgIf } from '@angular/common';
import { PaginatorModule } from 'primeng/paginator';
import { TableComponent } from '../../../layouts/table/table.component';
import TranslateDirective from '../../../shared/language/translate.directive';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FilterOptions, IFilterOption, IFilterOptions } from '../../../shared/filter';
import { Account } from '../../../core/auth/account.model';
import { ITEMS_PER_PAGE, PAGE_HEADER } from '../../../config/pagination.constants';
import { ActivatedRoute, Data, ParamMap, Router, RouterLink } from '@angular/router';
import { TourismPermitService } from '../../tourism-permit/service/tourism-permit.service';
import { MarineMediumService } from '../../marine-medium/marine-medium.service';
import { SortService, type SortState, sortStateSignal } from '../../../shared/sort';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AccountService } from '../../../core/auth/account.service';
import { ITourismPermit } from '../../tourism-permit/tourism-permit.model';
import { MarineStatusType } from '../../enumerations/marine-status-type.model';
import { Authority } from '../../../config/authority.constants';
import { IVisaTourismRequestModel } from '../visa-tourism-request.model';
import { VisaRequestStatusEnum } from '../visa-request-status.enum';
import { combineLatest, Subscription, tap } from 'rxjs';
import { DEFAULT_SORT_DATA, SORT } from '../../../config/navigation.constants';
type TableAction = {
  label: string;
  action: string;
  id: number;
  routerLink?: string; // Make routerLink optional
  handler?: () => void; // Add handler as an optional property
};
@Component({
  selector: 'jhi-visa-request-list',
  standalone: true,
  imports: [
    AlertComponent,
    AlertErrorComponent,
    FaIconComponent,
    FilterComponent,
    FormsModule,
    HasAnyAuthorityDirective,
    NgIf,
    PaginatorModule,
    TableComponent,
    TranslateDirective,
    TranslateModule,
    RouterLink,
    NgClass,
  ],
  templateUrl: './visa-request-list.component.html',
  styleUrl: './visa-request-list.component.scss',
})
export class VisaRequestListComponent implements OnInit {
  subscription: Subscription | null = null;
  filters: IFilterOptions = new FilterOptions();
  account?: Account | null;
  searchRqNumber: string = '';
  protected readonly Authority = Authority;
  protected readonly MarineStatusType = MarineStatusType;
  itemsPerPage = ITEMS_PER_PAGE;
  totalItems = 0;
  page = 0;
  isGovUser = false;
  visaTourismRequests: IVisaTourismRequestModel[] = [];
  public router = inject(Router);
  protected activatedRoute = inject(ActivatedRoute);
  protected marineMediumService = inject(MarineMediumService);
  protected sortService = inject(SortService);
  protected modalService = inject(NgbModal);
  protected ngZone = inject(NgZone);
  protected accountService = inject(AccountService);
  protected translateService = inject(TranslateService);
  searchStatus?: VisaRequestStatusEnum[] = [];
  activeTab: string = 'draftRequests'; // Default active tab
  criteria: any;
  private isLoading: boolean = false;
  tableActions: any[] = [];
  tableColumns = [
    { defaultField: 'requestNumber', header: 'srsaApp.visaTourism.permitNumber', sortable: false },
    {
      defaultField: 'marineMedium.nameAr',
      enField: 'marineMedium.nameEn',
      header: 'srsaApp.visaTourism.marineMedium',
      sortable: false,
      localeValues: true,
    },
    {
      defaultField: 'requestStatus',
      fieldJhiTranslate: 'srsaApp.tourismPermit.status',
      header: 'srsaApp.technicalPermit.requestStatus',
      sortable: false,
    },
    { defaultField: 'submitDate', header: 'srsaApp.permitRequest.submitDate', sortable: false, type: 'date', format: 'formatMediumDate' },
  ];
  ngOnInit(): void {
    this.subscription = combineLatest([this.activatedRoute.queryParamMap, this.activatedRoute.data])
      .pipe(
        tap(([params, data]) => this.fillComponentAttributeFromRoute(params, data)),
        tap(() => this.loadDraftRequests()),
      )
      .subscribe();

    this.accountService.identity().subscribe(
      account => {
        this.account = account;
        // Any additional code that depends on account can go here
      },
      error => {
        console.error('Error loading account:', error);
      },
    );
    this.isGovUser = this.accountService.hasAnyAuthority(['ROLE_SRSA_USER']);
    this.filters.filterChanges.subscribe(filterOptions => this.handleNavigation(1, filterOptions));
  }
  protected fillComponentAttributeFromRoute(params: ParamMap, data: Data): void {
    const page = params.get(PAGE_HEADER);
    this.page = +(page ?? 0);
    this.filters.initializeFromParams(params);
  }

  loadDraftRequests() {
    this.activeTab = 'draftRequests';
  }

  getTabClass(tabName: string): string {
    return this.activeTab === tabName ? 'figma-button-selected' : 'figma-button-sign';
  }
  loadNeedGovApprovalRequests() {
    this.activeTab = 'needActionRequests';
  }

  loadAllInProgressRequests() {
    this.activeTab = 'allRequests';
  }

  onSearch() {}

  isArabic(): boolean {
    let currentLanguage = this.translateService.currentLang;
    currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
    return currentLanguage.startsWith('ar', 0);
  }

  getAllTableActions(): void {
    if (this.visaTourismRequests) {
      this.tableActions = this.visaTourismRequests?.map(req => ({
        id: req.id,
        actions: this.getTableActions(req),
      }));
    }
  }
  getTableActions(req: any): any[] {
    const actions: TableAction[] = [{ label: 'entity.action.view', action: 'view', routerLink: '/visa-tourism', id: req.id }];
    this.canEdit(req).then(canEdit => {
      if (canEdit) {
        actions.push({
          label: 'entity.action.edit',
          action: 'edit',
          routerLink: '/visa-tourism',
          id: req.id,
        });
      }
    });

    return actions;
  }
  async canEdit(visaRequest: IVisaTourismRequestModel): Promise<boolean> {
    // Check if user has required authority AND is the owner
    const hasRequiredRole = this.accountService.hasAnyAuthority([
      Authority.ROLE_SERVICE_PROVIDER,
      Authority.ROLE_INDIVIDUAL_USER_NON_FOREIGN,
    ]);
    const isOwner = this.account?.id === visaRequest.ownerId;
    if (!hasRequiredRole || !isOwner) {
      return false;
    }
    // Check  status conditions
    return visaRequest.requestStatus === VisaRequestStatusEnum.DRAFT;
  }
  navigateToWithComponentValues(event: SortState | any): void {
    this.handleNavigation(this.page, this.filters.filterOptions);
  }

  navigateToPage(page: any): void {
    this.page = page.page;
    this.handleNavigation(page, this.filters.filterOptions);
  }
  protected handleNavigation(page: number, filterOptions?: IFilterOption[]): void {
    this.isLoading = true;
    this.load();
  }

  private load() {}
  previousState(): void {
    this.router.navigate(['/visa-tourism']);
  }
}
