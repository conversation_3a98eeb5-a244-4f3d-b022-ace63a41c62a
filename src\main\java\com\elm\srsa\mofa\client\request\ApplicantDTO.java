package com.elm.srsa.mofa.client.request;

import java.util.List;

public class ApplicantDTO {

    private String navigationalLicenseNumber;
    private String firstNameAr;

    private String applicationNumber;

    private String firstNameEn;
    private String fatherNameAr;
    private String fatherNameEn;
    private String grandFatherNameAr;
    private String grandFatherNameEn;
    private String familyNameAr;
    private String familyNameEn;
    private String passportNo;
    private int passportType;
    private String issuePassportCountry;
    private String passportIssueDate;
    private String passportExpiryDate;
    private String birthPlace;
    private String birthDate;
    private String nationality;
    private String prevNationality;
    private int religion;
    private int maritalstatus;
    private int gender;
    private String job;
    private int entryType;
    private List<TravelHistoryDTO> prevTravels;
    private String email;
    private String mobileNo;
    private String personalImage;
    private String residenceCountry;
    private String birthCountry;
    private boolean doYouWantDoUmra;
    private int visaValidity;
    private int residenceDuration;
    private String address;
    private InsuranceQuestionsDTO insuranceQuestions;
    private SecurityQuestionsDTO securityQuestions;
    private String expectedEntryDate;
    private String guardianPassportNumber;

    public String getApplicationNumber() {
        return applicationNumber;
    }

    public void setApplicationNumber(String applicationNumber) {
        this.applicationNumber = applicationNumber;
    }

    public String getNavigationalLicenseNumber() {
        return navigationalLicenseNumber;
    }

    public void setNavigationalLicenseNumber(String navigationalLicenseNumber) {
        this.navigationalLicenseNumber = navigationalLicenseNumber;
    }

    public String getFirstNameAr() {
        return firstNameAr;
    }

    public void setFirstNameAr(String firstNameAr) {
        this.firstNameAr = firstNameAr;
    }

    public String getFirstNameEn() {
        return firstNameEn;
    }

    public void setFirstNameEn(String firstNameEn) {
        this.firstNameEn = firstNameEn;
    }

    public String getFatherNameAr() {
        return fatherNameAr;
    }

    public void setFatherNameAr(String fatherNameAr) {
        this.fatherNameAr = fatherNameAr;
    }

    public String getFatherNameEn() {
        return fatherNameEn;
    }

    public void setFatherNameEn(String fatherNameEn) {
        this.fatherNameEn = fatherNameEn;
    }

    public String getGrandFatherNameAr() {
        return grandFatherNameAr;
    }

    public void setGrandFatherNameAr(String grandFatherNameAr) {
        this.grandFatherNameAr = grandFatherNameAr;
    }

    public String getGrandFatherNameEn() {
        return grandFatherNameEn;
    }

    public void setGrandFatherNameEn(String grandFatherNameEn) {
        this.grandFatherNameEn = grandFatherNameEn;
    }

    public String getFamilyNameAr() {
        return familyNameAr;
    }

    public void setFamilyNameAr(String familyNameAr) {
        this.familyNameAr = familyNameAr;
    }

    public String getFamilyNameEn() {
        return familyNameEn;
    }

    public void setFamilyNameEn(String familyNameEn) {
        this.familyNameEn = familyNameEn;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public int getPassportType() {
        return passportType;
    }

    public void setPassportType(int passportType) {
        this.passportType = passportType;
    }

    public String getIssuePassportCountry() {
        return issuePassportCountry;
    }

    public void setIssuePassportCountry(String issuePassportCountry) {
        this.issuePassportCountry = issuePassportCountry;
    }

    public String getPassportIssueDate() {
        return passportIssueDate;
    }

    public void setPassportIssueDate(String passportIssueDate) {
        this.passportIssueDate = passportIssueDate;
    }

    public String getPassportExpiryDate() {
        return passportExpiryDate;
    }

    public void setPassportExpiryDate(String passportExpiryDate) {
        this.passportExpiryDate = passportExpiryDate;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getPrevNationality() {
        return prevNationality;
    }

    public void setPrevNationality(String prevNationality) {
        this.prevNationality = prevNationality;
    }

    public int getReligion() {
        return religion;
    }

    public void setReligion(int religion) {
        this.religion = religion;
    }

    public int getMaritalstatus() {
        return maritalstatus;
    }

    public void setMaritalstatus(int maritalstatus) {
        this.maritalstatus = maritalstatus;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public int getEntryType() {
        return entryType;
    }

    public void setEntryType(int entryType) {
        this.entryType = entryType;
    }

    public List<TravelHistoryDTO> getPrevTravels() {
        return prevTravels;
    }

    public void setPrevTravels(List<TravelHistoryDTO> prevTravels) {
        this.prevTravels = prevTravels;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getPersonalImage() {
        return personalImage;
    }

    public void setPersonalImage(String personalImage) {
        this.personalImage = personalImage;
    }

    public String getResidenceCountry() {
        return residenceCountry;
    }

    public void setResidenceCountry(String residenceCountry) {
        this.residenceCountry = residenceCountry;
    }

    public String getBirthCountry() {
        return birthCountry;
    }

    public void setBirthCountry(String birthCountry) {
        this.birthCountry = birthCountry;
    }

    public boolean isDoYouWantDoUmra() {
        return doYouWantDoUmra;
    }

    public void setDoYouWantDoUmra(boolean doYouWantDoUmra) {
        this.doYouWantDoUmra = doYouWantDoUmra;
    }

    public int getVisaValidity() {
        return visaValidity;
    }

    public void setVisaValidity(int visaValidity) {
        this.visaValidity = visaValidity;
    }

    public int getResidenceDuration() {
        return residenceDuration;
    }

    public void setResidenceDuration(int residenceDuration) {
        this.residenceDuration = residenceDuration;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public InsuranceQuestionsDTO getInsuranceQuestions() {
        return insuranceQuestions;
    }

    public void setInsuranceQuestions(InsuranceQuestionsDTO insuranceQuestions) {
        this.insuranceQuestions = insuranceQuestions;
    }

    public SecurityQuestionsDTO getSecurityQuestions() {
        return securityQuestions;
    }

    public void setSecurityQuestions(SecurityQuestionsDTO securityQuestions) {
        this.securityQuestions = securityQuestions;
    }

    public String getExpectedEntryDate() {
        return expectedEntryDate;
    }

    public void setExpectedEntryDate(String expectedEntryDate) {
        this.expectedEntryDate = expectedEntryDate;
    }

    public String getGuardianPassportNumber() {
        return guardianPassportNumber;
    }

    public void setGuardianPassportNumber(String guardianPassportNumber) {
        this.guardianPassportNumber = guardianPassportNumber;
    }
}
