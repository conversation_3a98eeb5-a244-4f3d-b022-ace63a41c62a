package com.elm.srsa.web.rest.request;

import java.time.LocalDate;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UpdateProfileRequest {

    private String userLogin;
    private String userRegType;
    private String currentPassword;
    private String newPassword;
    private String newPasswordConfirmation;
    private String email;
    private String mobile;
    private Long updateRequestId;
    private String otpUUID;
    private String otpCode;
    private String nationalNumber;
    private String passportNumber;
    private String passportFullName;
    private String passportExpiryDateHijri;
    private String passportExpiryDateGregorian;
    private String nationalityId;
    private String passportCountryId;
    private String gender;
    private String idNumber;
    private String crNumber;
    private String birthDate;
    private boolean hijri;
    private String language;

    private String organizationName;
    private String organizationReferenceNumber;
    private String entityIssuingCommercialLicense;
    private Long companyNationalityId;
    private String businessDescription;
    private String commercialLicenseExpiryDate;
}
