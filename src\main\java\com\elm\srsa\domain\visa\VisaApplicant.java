package com.elm.srsa.domain.visa;

import com.elm.srsa.domain.visa.enumerations.*;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "visa_applicant")
@Getter
@Setter
public class VisaApplicant {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // المعرف

    @ManyToOne
    @JoinColumn(name = "visa_request_id")
    private VisaRequest visaRequest; // الطلب المرتبط

    // need to add in liquibase

    private String firstNameAr;
    private String firstNameEn;
    private String fatherNameAr;
    private String fatherNameEn;
    private String grandFatherNameAr;
    private String grandFatherNameEn;
    private String familyNameAr;
    private String familyNameEn;

    private boolean isCurrentlyInHospitalOrTakingEmerTreatments;
    private boolean youHaveWeaknessOrCongenitalPlatformation;
    private boolean areYouPregnant;
    private boolean isNoramalPregnancy;
    private int howManyMonthsOfPregnancy;
    private String navigationalLicenseNumber;
    private String prevNationality;

    private String guardianPassportNnumber;

    private String visaStatus;
    private String visaNumber;
    private String applicantNumber;

    ///////////////////////
    private String fullName; // الاسم الكامل
    private String passportNumber; // رقم الجواز
    private String nationality; // الجنسية
    private LocalDate birthDate; // تاريخ الميلاد

    @Enumerated(EnumType.STRING)
    private Gender gender; // الجنس

    @Enumerated(EnumType.STRING)
    private IdentityType identityType; // نوع الهوية

    @Enumerated(EnumType.STRING)
    private Role role; // الدور/الوظيفة

    private String jobTitle; // المسمى الوظيفي

    @Enumerated(EnumType.STRING)
    private MaritalStatus maritalStatus; // الحالة الاجتماعية

    @Enumerated(EnumType.STRING)
    private Religion religion; // الديانة

    private String email; // البريد الإلكتروني
    private String phoneNumber; // رقم الجوال
    private String address; // العنوان

    private LocalDate passportIssueDate; // تاريخ إصدار الجواز
    private LocalDate passportExpiryDate; // تاريخ انتهاء الجواز
    private String passportCountry; // بلد إصدار الجواز
    private String birthCountry; // بلد الميلاد
    private String residenceCountry; // بلد الإقامة

    private LocalDate expectedEntryDate; // تاريخ الدخول المتوقع

    @Enumerated(EnumType.STRING)
    private EntryType numberOfEntries; // عدد مرات الدخول

    @Enumerated(EnumType.STRING)
    private PassportType passportType;

    private Integer visaDurationDays; // مدة التأشيرة بالأيام

    private Boolean wantsUmrah; // هل يرغب في العمرة؟
    private Boolean hasOtherNationality; // هل لديه جنسية أخرى؟
    private String otherNationality; // الجنسية الأخرى

    private Boolean hasDisability; // هل لديه إعاقة؟
    private Boolean tookVaccines; // هل تلقى تطعيمات؟

    @OneToOne(mappedBy = "visaApplicant", cascade = CascadeType.ALL)
    private VisaMedicalHistory medicalHistory;

    @OneToOne(mappedBy = "visaApplicant", cascade = CascadeType.ALL)
    private VisaSecurityClearance securityClearance;

    @OneToMany(mappedBy = "visaApplicant", cascade = CascadeType.ALL)
    private List<VisaApplicantPreviousTravel> previousTravels;
}
