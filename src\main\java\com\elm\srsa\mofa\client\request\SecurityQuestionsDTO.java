package com.elm.srsa.mofa.client.request;

public class SecurityQuestionsDTO {

    private SecurityQuestionDTO haveYouArrestedForMoneyCases;
    private SecurityQuestionDTO haveYouJudjedInYourCountry;
    private SecurityQuestionDTO haveYouServedInArmyForces;
    private boolean haveYouArrestedByInterpol;
    private boolean haveYouDeportedFromKSAOrOtherCountry;
    private SecurityQuestionDTO haveYouWorkedInMediaOrPoliticalField;
    private SecurityQuestionDTO doYouBelongToAnyTerroristOrganization;
    private boolean doesYouPassportContainsRestication;
    private boolean doYouHaveAnyDisibility;
    private boolean haveYouTokeVaccinations;
    private boolean deliverVaccinationCertificate;

    public SecurityQuestionDTO getHaveYouArrestedForMoneyCases() {
        return haveYouArrestedForMoneyCases;
    }

    public void setHaveYouArrestedForMoneyCases(SecurityQuestionDTO haveYouArrestedForMoneyCases) {
        this.haveYouArrestedForMoneyCases = haveYouArrestedForMoneyCases;
    }

    public SecurityQuestionDTO getHaveYouJudjedInYourCountry() {
        return haveYouJudjedInYourCountry;
    }

    public void setHaveYouJudjedInYourCountry(SecurityQuestionDTO haveYouJudjedInYourCountry) {
        this.haveYouJudjedInYourCountry = haveYouJudjedInYourCountry;
    }

    public SecurityQuestionDTO getHaveYouServedInArmyForces() {
        return haveYouServedInArmyForces;
    }

    public void setHaveYouServedInArmyForces(SecurityQuestionDTO haveYouServedInArmyForces) {
        this.haveYouServedInArmyForces = haveYouServedInArmyForces;
    }

    public boolean isHaveYouArrestedByInterpol() {
        return haveYouArrestedByInterpol;
    }

    public void setHaveYouArrestedByInterpol(boolean haveYouArrestedByInterpol) {
        this.haveYouArrestedByInterpol = haveYouArrestedByInterpol;
    }

    public boolean isHaveYouDeportedFromKSAOrOtherCountry() {
        return haveYouDeportedFromKSAOrOtherCountry;
    }

    public void setHaveYouDeportedFromKSAOrOtherCountry(boolean haveYouDeportedFromKSAOrOtherCountry) {
        this.haveYouDeportedFromKSAOrOtherCountry = haveYouDeportedFromKSAOrOtherCountry;
    }

    public SecurityQuestionDTO getHaveYouWorkedInMediaOrPoliticalField() {
        return haveYouWorkedInMediaOrPoliticalField;
    }

    public void setHaveYouWorkedInMediaOrPoliticalField(SecurityQuestionDTO haveYouWorkedInMediaOrPoliticalField) {
        this.haveYouWorkedInMediaOrPoliticalField = haveYouWorkedInMediaOrPoliticalField;
    }

    public SecurityQuestionDTO getDoYouBelongToAnyTerroristOrganization() {
        return doYouBelongToAnyTerroristOrganization;
    }

    public void setDoYouBelongToAnyTerroristOrganization(SecurityQuestionDTO doYouBelongToAnyTerroristOrganization) {
        this.doYouBelongToAnyTerroristOrganization = doYouBelongToAnyTerroristOrganization;
    }

    public boolean isDoesYouPassportContainsRestication() {
        return doesYouPassportContainsRestication;
    }

    public void setDoesYouPassportContainsRestication(boolean doesYouPassportContainsRestication) {
        this.doesYouPassportContainsRestication = doesYouPassportContainsRestication;
    }

    public boolean isDoYouHaveAnyDisibility() {
        return doYouHaveAnyDisibility;
    }

    public void setDoYouHaveAnyDisibility(boolean doYouHaveAnyDisibility) {
        this.doYouHaveAnyDisibility = doYouHaveAnyDisibility;
    }

    public boolean isHaveYouTokeVaccinations() {
        return haveYouTokeVaccinations;
    }

    public void setHaveYouTokeVaccinations(boolean haveYouTokeVaccinations) {
        this.haveYouTokeVaccinations = haveYouTokeVaccinations;
    }

    public boolean isDeliverVaccinationCertificate() {
        return deliverVaccinationCertificate;
    }

    public void setDeliverVaccinationCertificate(boolean deliverVaccinationCertificate) {
        this.deliverVaccinationCertificate = deliverVaccinationCertificate;
    }
}
