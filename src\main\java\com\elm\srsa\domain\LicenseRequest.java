package com.elm.srsa.domain;

import com.elm.srsa.domain.enumeration.LicenseDuration;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

/**
 * A LicenseRequest.
 */
@Entity
@Table(name = "license_request")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Where(clause = "record_activity = true")
@SQLDelete(sql = "UPDATE  license_request SET record_activity = 0 WHERE id = ?")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class LicenseRequest extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "no_of_years_experience")
    private Integer noOfYearsExperience;

    @Column(name = "no_of_country_company_opr")
    private Integer noOfCountryCompanyOpr;

    @Column(name = "no_of_managed_maritime_mediums")
    private Integer noOfManagedMaritimeMediums;

    @Column(name = "no_of_owned_maritime_mediums_ksa")
    private Integer noOfOwnedMaritimeMediumsKsa;

    @Column(name = "no_of_owned_maritime_mediums_world")
    private Integer noOfOwnedMaritimeMediumsWorld;

    @Column(name = "no_of_managed_maritime_mediums_general")
    private Integer noOfManagedMaritimeMediumsGeneral;

    @Enumerated(EnumType.STRING)
    @Column(name = "requested_license_duration")
    private LicenseDuration requestedLicenseDuration;

    @Column(name = "marina_owner_name")
    private String marinaOwnerNameAr;

    @Column(name = "marina_owner_name_english")
    private String marinaOwnerNameEn;

    @Column(name = "marina_cr_number")
    private String marinaCrNumber;

    @Column(name = "marina_cr_name_ar")
    private String marinaCrNameAr;

    @Column(name = "marina_cr_name_en")
    private String marinaCrNameEn;

    @Column(name = "number_of_docks")
    private Integer numberOfDocks;

    @Column(name = "dock_length")
    private Integer dockLength;

    @Column(name = "dock_depth")
    private Integer dockDepth;

    @Column(name = "dock_width")
    private Integer dockWidth;

    @Column(name = "no_of_water_stations")
    private Integer noOfWaterStations;

    @Column(name = "no_of_water_stations_per_platform")
    private Integer noOfWaterStationsPerPlatform;

    @Column(name = "no_of_fuel_stations")
    private Integer noOfFuelStations;

    @Column(name = "no_of_waste_stations")
    private Integer noOfWasteStations;

    @Column(name = "no_of_waste_stations_per_platform")
    private Integer noOfWasteStationsPerPlatform;

    @Column(name = "no_of_electric_charging_stations")
    private Integer noOfElectricChargingStations;

    @Column(name = "no_of_control_towers")
    private Integer noOfControlTowers;

    @Column(name = "no_of_marina_facilities")
    private Integer noOfMarinaFacilities;

    @Column(name = "boats_capacity")
    private Integer boatsCapacity;

    @Column(name = "yachts_capacity")
    private Integer yachtsCapacity;

    @Column(name = "beach_owner_name")
    private String beachOwnerName;

    @Column(name = "beach_trade_name")
    private String beachTradeName;

    @Column(name = "beach_owner_commercial_registration_number")
    private String beachOwnerCommercialRegistrationNumber;

    @Column(name = "no_of_available_restrooms")
    private Integer numberOfRestrooms;

    @Column(name = "no_of_available_lifeguards")
    private Integer numberOfLifeguards;

    @Column(name = "are_there_rooms_or_cabins_for_rent")
    @JsonProperty("isThereCabinsForRent")
    private Boolean isThereCabinsForRent;

    @Column(name = "no_of_cabins_and_rooms")
    private Integer NumberOfCabinsAndRooms;

    @Column(name = "no_of_food_truck_parking")
    private Integer numberOfFoodTruckParking;

    @Column(name = "visitor_capacity")
    private Integer visitorCapacity;

    @Column(name = "is_there_marine_vessel")
    @JsonProperty("isThereMarineVessel")
    private Boolean isThereMarineVessel;

    @Column(name = "number_of_other_marine_vessels")
    private Integer numberOfOtherMarineVessels;

    @Column(name = "previously_operated_beach_inside_or_outside_saudi_arabia")
    private Boolean previouslyOperatedBeachInsideOrOutsideSaudiArabia;

    @Column(name = "other_maritime_medium_capacity")
    private Integer otherMaritimeMediumCapacity;

    @ManyToOne(fetch = FetchType.EAGER)
    private NationalAddress marinaLocation;

    @ManyToOne(fetch = FetchType.EAGER)
    private Company maritimeTourismAgent;

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestMaritimeMedium> licenseRequestMaritimeMediums = new HashSet<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestProvidedLanguages> licenseRequestProvidedLanguages = new HashSet<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestMaritimeServices> licenseRequestMaritimeServices = new HashSet<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestMarineTourServices> licenseRequestMarineTourServices = new HashSet<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestCountry> licenseRequestCountries = new HashSet<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestFuelTypes> licenseRequestFuelTypes = new HashSet<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "licenseRequest", cascade = CascadeType.ALL)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "licenseRequest" }, allowSetters = true)
    private Set<LicenseRequestDocks> licenseRequestDocks = new HashSet<>();

    @OneToMany(mappedBy = "licenseRequest", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private Set<LicenseRequestBeachActivities> licenseRequestBeachActivities = new HashSet<>();

    @OneToMany(mappedBy = "licenseRequest", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private Set<LicenseRequestBeachEquipments> licenseRequestBeachEquipment = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public LicenseRequest id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getNoOfYearsExperience() {
        return this.noOfYearsExperience;
    }

    public LicenseRequest noOfYearsExperience(Integer noOfYearsExperience) {
        this.setNoOfYearsExperience(noOfYearsExperience);
        return this;
    }

    public void setNoOfYearsExperience(Integer noOfYearsExperience) {
        this.noOfYearsExperience = noOfYearsExperience;
    }

    public Integer getNoOfCountryCompanyOpr() {
        return this.noOfCountryCompanyOpr;
    }

    public LicenseRequest noOfCountryCompanyOpr(Integer noOfCountryCompanyOpr) {
        this.setNoOfCountryCompanyOpr(noOfCountryCompanyOpr);
        return this;
    }

    public void setNoOfCountryCompanyOpr(Integer noOfCountryCompanyOpr) {
        this.noOfCountryCompanyOpr = noOfCountryCompanyOpr;
    }

    public Integer getNoOfManagedMaritimeMediums() {
        return this.noOfManagedMaritimeMediums;
    }

    public LicenseRequest noOfManagedMaritimeMediums(Integer noOfManagedMaritimeMediums) {
        this.setNoOfManagedMaritimeMediums(noOfManagedMaritimeMediums);
        return this;
    }

    public void setNoOfManagedMaritimeMediums(Integer noOfManagedMaritimeMediums) {
        this.noOfManagedMaritimeMediums = noOfManagedMaritimeMediums;
    }

    public Integer getNoOfOwnedMaritimeMediumsKsa() {
        return noOfOwnedMaritimeMediumsKsa;
    }

    public void setNoOfOwnedMaritimeMediumsKsa(Integer noOfOwnedMaritimeMediumsKsa) {
        this.noOfOwnedMaritimeMediumsKsa = noOfOwnedMaritimeMediumsKsa;
    }

    public Integer getNoOfOwnedMaritimeMediumsWorld() {
        return noOfOwnedMaritimeMediumsWorld;
    }

    public void setNoOfOwnedMaritimeMediumsWorld(Integer noOfOwnedMaritimeMediumsWorld) {
        this.noOfOwnedMaritimeMediumsWorld = noOfOwnedMaritimeMediumsWorld;
    }

    public Integer getNoOfManagedMaritimeMediumsGeneral() {
        return this.noOfManagedMaritimeMediumsGeneral;
    }

    public LicenseRequest noOfManagedMaritimeMediumsGeneral(Integer noOfManagedMaritimeMediumsGeneral) {
        this.setNoOfManagedMaritimeMediumsGeneral(noOfManagedMaritimeMediumsGeneral);
        return this;
    }

    public void setNoOfManagedMaritimeMediumsGeneral(Integer noOfManagedMaritimeMediumsGeneral) {
        this.noOfManagedMaritimeMediumsGeneral = noOfManagedMaritimeMediumsGeneral;
    }

    public LicenseDuration getRequestedLicenseDuration() {
        return this.requestedLicenseDuration;
    }

    public LicenseRequest requestedLicenseDuration(LicenseDuration requestedLicenseDuration) {
        this.setRequestedLicenseDuration(requestedLicenseDuration);
        return this;
    }

    public void setRequestedLicenseDuration(LicenseDuration requestedLicenseDuration) {
        this.requestedLicenseDuration = requestedLicenseDuration;
    }

    public String getMarinaOwnerNameAr() {
        return this.marinaOwnerNameAr;
    }

    public LicenseRequest marinaOwnerName(String marinaOwnerName) {
        this.setMarinaOwnerNameAr(marinaOwnerName);
        return this;
    }

    public void setMarinaOwnerNameAr(String marinaOwnerName) {
        this.marinaOwnerNameAr = marinaOwnerName;
    }

    public String getMarinaCrNumber() {
        return this.marinaCrNumber;
    }

    public LicenseRequest marinaCrNumber(String marinaCrNumber) {
        this.setMarinaCrNumber(marinaCrNumber);
        return this;
    }

    public void setMarinaCrNumber(String marinaCrNumber) {
        this.marinaCrNumber = marinaCrNumber;
    }

    public String getMarinaCrNameAr() {
        return this.marinaCrNameAr;
    }

    public LicenseRequest marinaCrNameAr(String marinaCrNameAr) {
        this.setMarinaCrNameAr(marinaCrNameAr);
        return this;
    }

    public void setMarinaCrNameAr(String marinaCrNameAr) {
        this.marinaCrNameAr = marinaCrNameAr;
    }

    public String getMarinaCrNameEn() {
        return this.marinaCrNameEn;
    }

    public LicenseRequest marinaCrNameEn(String marinaCrNameEn) {
        this.setMarinaCrNameEn(marinaCrNameEn);
        return this;
    }

    public void setMarinaCrNameEn(String marinaCrNameEn) {
        this.marinaCrNameEn = marinaCrNameEn;
    }

    public Integer getNumberOfDocks() {
        return this.numberOfDocks;
    }

    public LicenseRequest numberOfDocks(Integer numberOfDocks) {
        this.setNumberOfDocks(numberOfDocks);
        return this;
    }

    public void setNumberOfDocks(Integer numberOfDocks) {
        this.numberOfDocks = numberOfDocks;
    }

    public Integer getDockLength() {
        return this.dockLength;
    }

    public LicenseRequest dockLength(Integer dockLength) {
        this.setDockLength(dockLength);
        return this;
    }

    public void setDockLength(Integer dockLength) {
        this.dockLength = dockLength;
    }

    public Integer getDockDepth() {
        return this.dockDepth;
    }

    public LicenseRequest dockDepth(Integer dockDepth) {
        this.setDockDepth(dockDepth);
        return this;
    }

    public void setDockDepth(Integer dockDepth) {
        this.dockDepth = dockDepth;
    }

    public Integer getNoOfWaterStations() {
        return this.noOfWaterStations;
    }

    public LicenseRequest noOfWaterStations(Integer noOfWaterStations) {
        this.setNoOfWaterStations(noOfWaterStations);
        return this;
    }

    public void setNoOfWaterStations(Integer noOfWaterStations) {
        this.noOfWaterStations = noOfWaterStations;
    }

    public Integer getNoOfWaterStationsPerPlatform() {
        return this.noOfWaterStationsPerPlatform;
    }

    public LicenseRequest noOfWaterStationsPerPlatform(Integer noOfWaterStationsPerPlatform) {
        this.setNoOfWaterStationsPerPlatform(noOfWaterStationsPerPlatform);
        return this;
    }

    public void setNoOfWaterStationsPerPlatform(Integer noOfWaterStationsPerPlatform) {
        this.noOfWaterStationsPerPlatform = noOfWaterStationsPerPlatform;
    }

    public Integer getNoOfFuelStations() {
        return this.noOfFuelStations;
    }

    public LicenseRequest noOfFuelStations(Integer noOfFuelStations) {
        this.setNoOfFuelStations(noOfFuelStations);
        return this;
    }

    public void setNoOfFuelStations(Integer noOfFuelStations) {
        this.noOfFuelStations = noOfFuelStations;
    }

    public Integer getNoOfWasteStations() {
        return this.noOfWasteStations;
    }

    public LicenseRequest noOfWasteStations(Integer noOfWasteStations) {
        this.setNoOfWasteStations(noOfWasteStations);
        return this;
    }

    public void setNoOfWasteStations(Integer noOfWasteStations) {
        this.noOfWasteStations = noOfWasteStations;
    }

    public Integer getNoOfWasteStationsPerPlatform() {
        return this.noOfWasteStationsPerPlatform;
    }

    public LicenseRequest noOfWasteStationsPerPlatform(Integer noOfWasteStationsPerPlatform) {
        this.setNoOfWasteStationsPerPlatform(noOfWasteStationsPerPlatform);
        return this;
    }

    public void setNoOfWasteStationsPerPlatform(Integer noOfWasteStationsPerPlatform) {
        this.noOfWasteStationsPerPlatform = noOfWasteStationsPerPlatform;
    }

    public Integer getNoOfElectricChargingStations() {
        return this.noOfElectricChargingStations;
    }

    public LicenseRequest noOfElectricChargingStations(Integer noOfElectricChargingStations) {
        this.setNoOfElectricChargingStations(noOfElectricChargingStations);
        return this;
    }

    public void setNoOfElectricChargingStations(Integer noOfElectricChargingStations) {
        this.noOfElectricChargingStations = noOfElectricChargingStations;
    }

    public Integer getNoOfControlTowers() {
        return this.noOfControlTowers;
    }

    public LicenseRequest noOfControlTowers(Integer noOfControlTowers) {
        this.setNoOfControlTowers(noOfControlTowers);
        return this;
    }

    public void setNoOfControlTowers(Integer noOfControlTowers) {
        this.noOfControlTowers = noOfControlTowers;
    }

    public Integer getNoOfMarinaFacilities() {
        return this.noOfMarinaFacilities;
    }

    public LicenseRequest noOfMarinaFacilities(Integer noOfMarinaFacilities) {
        this.setNoOfMarinaFacilities(noOfMarinaFacilities);
        return this;
    }

    public void setNoOfMarinaFacilities(Integer noOfMarinaFacilities) {
        this.noOfMarinaFacilities = noOfMarinaFacilities;
    }

    public Integer getBoatsCapacity() {
        return this.boatsCapacity;
    }

    public LicenseRequest boatsCapacity(Integer boatsCapacity) {
        this.setBoatsCapacity(boatsCapacity);
        return this;
    }

    public void setBoatsCapacity(Integer boatsCapacity) {
        this.boatsCapacity = boatsCapacity;
    }

    public Integer getYachtsCapacity() {
        return this.yachtsCapacity;
    }

    public LicenseRequest yachtsCapacity(Integer yachtsCapacity) {
        this.setYachtsCapacity(yachtsCapacity);
        return this;
    }

    public void setYachtsCapacity(Integer yachtsCapacity) {
        this.yachtsCapacity = yachtsCapacity;
    }

    public Integer getOtherMaritimeMediumCapacity() {
        return this.otherMaritimeMediumCapacity;
    }

    public LicenseRequest otherMaritimeMediumCapacity(Integer otherMaritimeMediumCapacity) {
        this.setOtherMaritimeMediumCapacity(otherMaritimeMediumCapacity);
        return this;
    }

    public void setOtherMaritimeMediumCapacity(Integer otherMaritimeMediumCapacity) {
        this.otherMaritimeMediumCapacity = otherMaritimeMediumCapacity;
    }

    public String getMarinaOwnerNameEn() {
        return marinaOwnerNameEn;
    }

    public void setMarinaOwnerNameEn(String marinaOwnerNameEnglish) {
        this.marinaOwnerNameEn = marinaOwnerNameEnglish;
    }

    public Integer getDockWidth() {
        return dockWidth;
    }

    public void setDockWidth(Integer dockWidth) {
        this.dockWidth = dockWidth;
    }

    public NationalAddress getMarinaLocation() {
        return this.marinaLocation;
    }

    public void setMarinaLocation(NationalAddress nationalAddress) {
        this.marinaLocation = nationalAddress;
    }

    public LicenseRequest marinaLocation(NationalAddress nationalAddress) {
        this.setMarinaLocation(nationalAddress);
        return this;
    }

    public Company getMaritimeTourismAgent() {
        return this.maritimeTourismAgent;
    }

    public void setMaritimeTourismAgent(Company company) {
        this.maritimeTourismAgent = company;
    }

    public LicenseRequest maritimeTourismAgent(Company company) {
        this.setMaritimeTourismAgent(company);
        return this;
    }

    public Set<LicenseRequestMaritimeMedium> getLicenseRequestMaritimeMediums() {
        return this.licenseRequestMaritimeMediums;
    }

    public void setLicenseRequestMaritimeMediums(Set<LicenseRequestMaritimeMedium> licenseRequestMaritimeMediums) {
        if (this.licenseRequestMaritimeMediums != null) {
            this.licenseRequestMaritimeMediums.forEach(i -> i.setLicenseRequest(null));
        }
        if (licenseRequestMaritimeMediums != null) {
            licenseRequestMaritimeMediums.forEach(i -> i.setLicenseRequest(this));
        }
        this.licenseRequestMaritimeMediums = licenseRequestMaritimeMediums;
    }

    public LicenseRequest licenseRequestMaritimeMediums(Set<LicenseRequestMaritimeMedium> licenseRequestMaritimeMediums) {
        this.setLicenseRequestMaritimeMediums(licenseRequestMaritimeMediums);
        return this;
    }

    public LicenseRequest addLicenseRequestMaritimeMediums(LicenseRequestMaritimeMedium licenseRequestMaritimeMedium) {
        this.licenseRequestMaritimeMediums.add(licenseRequestMaritimeMedium);
        licenseRequestMaritimeMedium.setLicenseRequest(this);
        return this;
    }

    public LicenseRequest removeLicenseRequestMaritimeMediums(LicenseRequestMaritimeMedium licenseRequestMaritimeMedium) {
        this.licenseRequestMaritimeMediums.remove(licenseRequestMaritimeMedium);
        licenseRequestMaritimeMedium.setLicenseRequest(null);
        return this;
    }

    public Set<LicenseRequestProvidedLanguages> getLicenseRequestProvidedLanguages() {
        return this.licenseRequestProvidedLanguages;
    }

    public void setLicenseRequestProvidedLanguages(Set<LicenseRequestProvidedLanguages> licenseRequestProvidedLanguages) {
        if (this.licenseRequestProvidedLanguages != null) {
            this.licenseRequestProvidedLanguages.forEach(i -> i.setLicenseRequest(null));
        }
        if (licenseRequestProvidedLanguages != null) {
            licenseRequestProvidedLanguages.forEach(i -> i.setLicenseRequest(this));
        }
        this.licenseRequestProvidedLanguages = licenseRequestProvidedLanguages;
    }

    public LicenseRequest licenseRequestProvidedLanguages(Set<LicenseRequestProvidedLanguages> licenseRequestProvidedLanguages) {
        this.setLicenseRequestProvidedLanguages(licenseRequestProvidedLanguages);
        return this;
    }

    public LicenseRequest addLicenseRequestProvidedLanguages(LicenseRequestProvidedLanguages licenseRequestProvidedLanguages) {
        this.licenseRequestProvidedLanguages.add(licenseRequestProvidedLanguages);
        licenseRequestProvidedLanguages.setLicenseRequest(this);
        return this;
    }

    public LicenseRequest removeLicenseRequestProvidedLanguages(LicenseRequestProvidedLanguages licenseRequestProvidedLanguages) {
        this.licenseRequestProvidedLanguages.remove(licenseRequestProvidedLanguages);
        licenseRequestProvidedLanguages.setLicenseRequest(null);
        return this;
    }

    public Set<LicenseRequestMaritimeServices> getLicenseRequestMaritimeServices() {
        return this.licenseRequestMaritimeServices;
    }

    public void setLicenseRequestMaritimeServices(Set<LicenseRequestMaritimeServices> licenseRequestMaritimeServices) {
        if (this.licenseRequestMaritimeServices != null) {
            this.licenseRequestMaritimeServices.forEach(i -> i.setLicenseRequest(null));
        }
        if (licenseRequestMaritimeServices != null) {
            licenseRequestMaritimeServices.forEach(i -> i.setLicenseRequest(this));
        }
        this.licenseRequestMaritimeServices = licenseRequestMaritimeServices;
    }

    public LicenseRequest licenseRequestMaritimeServices(Set<LicenseRequestMaritimeServices> licenseRequestMaritimeServices) {
        this.setLicenseRequestMaritimeServices(licenseRequestMaritimeServices);
        return this;
    }

    public LicenseRequest addLicenseRequestMaritimeServices(LicenseRequestMaritimeServices licenseRequestMaritimeServices) {
        this.licenseRequestMaritimeServices.add(licenseRequestMaritimeServices);
        licenseRequestMaritimeServices.setLicenseRequest(this);
        return this;
    }

    public LicenseRequest removeLicenseRequestMaritimeServices(LicenseRequestMaritimeServices licenseRequestMaritimeServices) {
        this.licenseRequestMaritimeServices.remove(licenseRequestMaritimeServices);
        licenseRequestMaritimeServices.setLicenseRequest(null);
        return this;
    }

    public Set<LicenseRequestMarineTourServices> getLicenseRequestMarineTourServices() {
        return this.licenseRequestMarineTourServices;
    }

    public void setLicenseRequestMarineTourServices(Set<LicenseRequestMarineTourServices> licenseRequestMarineTourServices) {
        if (this.licenseRequestMarineTourServices != null) {
            this.licenseRequestMarineTourServices.forEach(i -> i.setLicenseRequest(null));
        }
        if (licenseRequestMarineTourServices != null) {
            licenseRequestMarineTourServices.forEach(i -> i.setLicenseRequest(this));
        }
        this.licenseRequestMarineTourServices = licenseRequestMarineTourServices;
    }

    public LicenseRequest licenseRequestMarineTourServices(Set<LicenseRequestMarineTourServices> licenseRequestMarineTourServices) {
        this.setLicenseRequestMarineTourServices(licenseRequestMarineTourServices);
        return this;
    }

    public LicenseRequest addLicenseRequestMarineTourServices(LicenseRequestMarineTourServices licenseRequestMarineTourServices) {
        this.licenseRequestMarineTourServices.add(licenseRequestMarineTourServices);
        licenseRequestMarineTourServices.setLicenseRequest(this);
        return this;
    }

    public LicenseRequest removeLicenseRequestMarineTourServices(LicenseRequestMarineTourServices licenseRequestMarineTourServices) {
        this.licenseRequestMarineTourServices.remove(licenseRequestMarineTourServices);
        licenseRequestMarineTourServices.setLicenseRequest(null);
        return this;
    }

    public Set<LicenseRequestCountry> getLicenseRequestCountries() {
        return this.licenseRequestCountries;
    }

    public void setLicenseRequestCountries(Set<LicenseRequestCountry> licenseRequestCountries) {
        if (this.licenseRequestCountries != null) {
            this.licenseRequestCountries.forEach(i -> i.setLicenseRequest(null));
        }
        if (licenseRequestCountries != null) {
            licenseRequestCountries.forEach(i -> i.setLicenseRequest(this));
        }
        this.licenseRequestCountries = licenseRequestCountries;
    }

    public LicenseRequest licenseRequestCountries(Set<LicenseRequestCountry> licenseRequestCountries) {
        this.setLicenseRequestCountries(licenseRequestCountries);
        return this;
    }

    public LicenseRequest addLicenseRequestCountry(LicenseRequestCountry licenseRequestCountry) {
        this.licenseRequestCountries.add(licenseRequestCountry);
        licenseRequestCountry.setLicenseRequest(this);
        return this;
    }

    public LicenseRequest removeLicenseRequestCountry(LicenseRequestCountry licenseRequestCountry) {
        this.licenseRequestCountries.remove(licenseRequestCountry);
        licenseRequestCountry.setLicenseRequest(null);
        return this;
    }

    public Set<LicenseRequestFuelTypes> getLicenseRequestFuelTypes() {
        return this.licenseRequestFuelTypes;
    }

    public void setLicenseRequestFuelTypes(Set<LicenseRequestFuelTypes> licenseRequestFuelTypes) {
        if (this.licenseRequestFuelTypes != null) {
            this.licenseRequestFuelTypes.forEach(i -> i.setLicenseRequest(null));
        }
        if (licenseRequestFuelTypes != null) {
            licenseRequestFuelTypes.forEach(i -> i.setLicenseRequest(this));
        }
        this.licenseRequestFuelTypes = licenseRequestFuelTypes;
    }

    public LicenseRequest licenseRequestFuelTypes(Set<LicenseRequestFuelTypes> licenseRequestFuelTypes) {
        this.setLicenseRequestFuelTypes(licenseRequestFuelTypes);
        return this;
    }

    public LicenseRequest addLicenseRequestFuelTypes(LicenseRequestFuelTypes licenseRequestFuelTypes) {
        this.licenseRequestFuelTypes.add(licenseRequestFuelTypes);
        licenseRequestFuelTypes.setLicenseRequest(this);
        return this;
    }

    public LicenseRequest removeLicenseRequestFuelTypes(LicenseRequestFuelTypes licenseRequestFuelTypes) {
        this.licenseRequestFuelTypes.remove(licenseRequestFuelTypes);
        licenseRequestFuelTypes.setLicenseRequest(null);
        return this;
    }

    public Set<LicenseRequestDocks> getLicenseRequestDocks() {
        return licenseRequestDocks;
    }

    public void setLicenseRequestDocks(Set<LicenseRequestDocks> licenseRequestDocks) {
        this.licenseRequestDocks = licenseRequestDocks;
    }

    public String getBeachOwnerName() {
        return beachOwnerName;
    }

    public void setBeachOwnerName(String beachOwnerName) {
        this.beachOwnerName = beachOwnerName;
    }

    public String getBeachTradeName() {
        return beachTradeName;
    }

    public void setBeachTradeName(String beachTradeName) {
        this.beachTradeName = beachTradeName;
    }

    public String getBeachOwnerCommercialRegistrationNumber() {
        return beachOwnerCommercialRegistrationNumber;
    }

    public void setBeachOwnerCommercialRegistrationNumber(String beachOwnerCommercialRegistrationNumber) {
        this.beachOwnerCommercialRegistrationNumber = beachOwnerCommercialRegistrationNumber;
    }

    public Integer getNumberOfRestrooms() {
        return numberOfRestrooms;
    }

    public void setNumberOfRestrooms(Integer numberOfRestrooms) {
        this.numberOfRestrooms = numberOfRestrooms;
    }

    public Integer getNumberOfLifeguards() {
        return numberOfLifeguards;
    }

    public void setNumberOfLifeguards(Integer numberOfLifeguards) {
        this.numberOfLifeguards = numberOfLifeguards;
    }

    public Boolean getThereCabinsForRent() {
        return isThereCabinsForRent;
    }

    public void setThereCabinsForRent(Boolean thereCabinsForRent) {
        isThereCabinsForRent = thereCabinsForRent;
    }

    public Integer getNumberOfCabinsAndRooms() {
        return NumberOfCabinsAndRooms;
    }

    public void setNumberOfCabinsAndRooms(Integer numberOfCabinsAndRooms) {
        NumberOfCabinsAndRooms = numberOfCabinsAndRooms;
    }

    public Integer getNumberOfFoodTruckParking() {
        return numberOfFoodTruckParking;
    }

    public void setNumberOfFoodTruckParking(Integer numberOfFoodTruckParking) {
        this.numberOfFoodTruckParking = numberOfFoodTruckParking;
    }

    public Integer getVisitorCapacity() {
        return visitorCapacity;
    }

    public void setVisitorCapacity(Integer visitorCapacity) {
        this.visitorCapacity = visitorCapacity;
    }

    public Boolean getThereMarineVessel() {
        return isThereMarineVessel;
    }

    public void setThereMarineVessel(Boolean thereMarineVessel) {
        isThereMarineVessel = thereMarineVessel;
    }

    public Integer getNumberOfOtherMarineVessels() {
        return numberOfOtherMarineVessels;
    }

    public void setNumberOfOtherMarineVessels(Integer numberOfOtherMarineVessels) {
        this.numberOfOtherMarineVessels = numberOfOtherMarineVessels;
    }

    public Boolean isPreviouslyOperatedBeachInsideOrOutsideSaudiArabia() {
        return previouslyOperatedBeachInsideOrOutsideSaudiArabia;
    }

    public void setPreviouslyOperatedBeachInsideOrOutsideSaudiArabia(Boolean previouslyOperatedBeachInsideOrOutsideSaudiArabia) {
        this.previouslyOperatedBeachInsideOrOutsideSaudiArabia = previouslyOperatedBeachInsideOrOutsideSaudiArabia;
    }

    public Boolean getPreviouslyOperatedBeachInsideOrOutsideSaudiArabia() {
        return previouslyOperatedBeachInsideOrOutsideSaudiArabia;
    }

    public Set<BeachActivities> getBeachActivities() {
        return licenseRequestBeachActivities.stream().map(LicenseRequestBeachActivities::getBeachActivities).collect(Collectors.toSet());
    }

    public void setBeachActivities(Set<BeachActivities> beachActivities) {
        // Clear existing relationships
        this.licenseRequestBeachActivities.clear();

        // Add new relationships
        if (beachActivities != null) {
            for (BeachActivities activity : beachActivities) {
                this.licenseRequestBeachActivities.add(new LicenseRequestBeachActivities(this, activity));
            }
        }
    }

    public void addBeachActivity(BeachActivities beachActivity) {
        LicenseRequestBeachActivities relationship = new LicenseRequestBeachActivities(this, beachActivity);
        licenseRequestBeachActivities.add(relationship);
    }

    public void removeBeachActivity(BeachActivities beachActivity) {
        licenseRequestBeachActivities.removeIf(rel -> rel.getBeachActivities().equals(beachActivity));
    }

    public Set<BeachEquipment> getBeachEquipment() {
        return licenseRequestBeachEquipment.stream().map(LicenseRequestBeachEquipments::getBeachEquipment).collect(Collectors.toSet());
    }

    public void setBeachEquipment(Set<BeachEquipment> beachEquipments) {
        // Clear existing relationships
        this.licenseRequestBeachEquipment.clear();

        // Add new relationships
        if (beachEquipments != null) {
            for (BeachEquipment equipment : beachEquipments) {
                this.licenseRequestBeachEquipment.add(new LicenseRequestBeachEquipments(this, equipment));
            }
        }
    }

    public void addBeachEquipment(BeachEquipment beachEquipment) {
        LicenseRequestBeachEquipments relationship = new LicenseRequestBeachEquipments(this, beachEquipment);
        licenseRequestBeachEquipment.add(relationship);
    }

    public void removeBeachEquipment(BeachEquipment beachEquipment) {
        licenseRequestBeachEquipment.removeIf(rel -> rel.getBeachEquipment().equals(beachEquipment));
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LicenseRequest)) {
            return false;
        }
        return getId() != null && getId().equals(((LicenseRequest) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return (
            "LicenseRequest{" +
            "id=" +
            id +
            ", noOfYearsExperience=" +
            noOfYearsExperience +
            ", noOfCountryCompanyOpr=" +
            noOfCountryCompanyOpr +
            ", noOfManagedMaritimeMediums=" +
            noOfManagedMaritimeMediums +
            ", noOfManagedMaritimeMediumsGeneral=" +
            noOfManagedMaritimeMediumsGeneral +
            ", requestedLicenseDuration=" +
            requestedLicenseDuration +
            ", marinaOwnerNameArabic='" +
            marinaOwnerNameAr +
            '\'' +
            ", marinaOwnerNameEnglish='" +
            marinaOwnerNameEn +
            '\'' +
            ", marinaCrNumber='" +
            marinaCrNumber +
            '\'' +
            ", marinaCrNameAr='" +
            marinaCrNameAr +
            '\'' +
            ", marinaCrNameEn='" +
            marinaCrNameEn +
            '\'' +
            ", numberOfDocks=" +
            numberOfDocks +
            ", dockLength=" +
            dockLength +
            ", dockDepth=" +
            dockDepth +
            ", dockWidth=" +
            dockWidth +
            ", noOfWaterStations=" +
            noOfWaterStations +
            ", noOfWaterStationsPerPlatform=" +
            noOfWaterStationsPerPlatform +
            ", noOfFuelStations=" +
            noOfFuelStations +
            ", noOfWasteStations=" +
            noOfWasteStations +
            ", noOfWasteStationsPerPlatform=" +
            noOfWasteStationsPerPlatform +
            ", noOfElectricChargingStations=" +
            noOfElectricChargingStations +
            ", noOfControlTowers=" +
            noOfControlTowers +
            ", noOfMarinaFacilities=" +
            noOfMarinaFacilities +
            ", boatsCapacity=" +
            boatsCapacity +
            ", yachtsCapacity=" +
            yachtsCapacity +
            ", otherMaritimeMediumCapacity=" +
            otherMaritimeMediumCapacity +
            ", marinaLocation=" +
            marinaLocation +
            ", maritimeTourismAgent=" +
            maritimeTourismAgent +
            ", licenseRequestMaritimeMediums=" +
            licenseRequestMaritimeMediums +
            ", licenseRequestProvidedLanguages=" +
            licenseRequestProvidedLanguages +
            ", licenseRequestMaritimeServices=" +
            licenseRequestMaritimeServices +
            ", licenseRequestMarineTourServices=" +
            licenseRequestMarineTourServices +
            ", licenseRequestCountries=" +
            licenseRequestCountries +
            ", licenseRequestFuelTypes=" +
            licenseRequestFuelTypes +
            '}'
        );
    }
}
