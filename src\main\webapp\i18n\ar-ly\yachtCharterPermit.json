{"srsaApp": {"yachtCharterPermit": {"home": {"title": "رخصة تأجير سياحي", "refreshListLabel": "تحديث القائمة", "createLabel": "إنشاء رخصة تأجير سياحي جديد", "createOrEditLabel": "إنشاء أو تعديل رخصة تأجير سياحي", "notFound": "لم يتم العثور على رخصة تأجير سياحي"}, "created": "تصريح تأجير يخوت جديد تم إنشاؤه بالمعرف {{ param }}", "updated": "تصريح تأجير يخوت تم تحديثه بالمعرف {{ param }}", "deleted": "تصريح تأجير يخوت تم حذفه بالمعرف {{ param }}", "delete": {"question": "هل أنت متأكد أنك تريد حذف تصريح تأجير يخوت {{ id }}؟"}, "detail": {"title": "رخصة تأجير سياحي", "serviceTerm1": "المستخدم يجب ان يكون وكيل ملاحي", "serviceTerm2": "الوكيل الملاحي لديه تفويض ورخصه ساريه", "step1": "املأ نموذج الطلب", "step2": "قدّم الطلب للمراجعة", "step3": "ستعرض التصريح الصادر"}, "status": {"NEW": "جديد", "DRAFT": "مسودّة", "DELETED": "م<PERSON><PERSON><PERSON><PERSON>", "ACTIVE": "فعال", "EXPIRED": "منتهي", "RETURNED": "معاد", "COMPLETED": "مكتمل", "CANCELLED": "ملغي", "null": "", "PENDING_REVIEW": "بإنتظار المراجعة", "PENDING_REPORT_APPROVAL": "تحت إعتماد التقرير", "PENDING_MANAGEMENT_LICENSING": "تحت الإعتماد", "RETURNED_INSPECTION_OFFICER": "معاد من قبل ضابط الفحص", "UNDER_PROCESS": "تحت المراجعة", "PENDING_INSPECTION": "بإنتظار المعاينة", "UNDER_INSPECTION": "تحت المعاينة", "PENDING_VISIT": "بانتظار الفحص المكتبي", "REJECTED": "مرفو<PERSON>", "VISIT_COMPLETED": "إتمام الزيارة الميدانية", "PENDING_PAYMENT": "بإنتظار الدفع", "RETURNED_LICENSING_OFFICER": "معاد من قبل ضابط الترخيص", "RETURNED_LICENSING_MANAGER": "معاد من قبل مدير الترخيص"}, "id": "الرقم", "marineOwnerNameAr": "إسم مالك الواسطة", "marineOwnerNameEn": "إسم مالك الواسطة", "marineOwnerIdNumber": "رقم هوية مالك الواسطة", "marineOwnerPassportNumber": "رقم جواز مالك الواسطة", "delegatorNameAr": "إسم الوكيل", "delegatorNameEn": "إسم الوكيل", "delegatorMobileNumber": "رقم جوال الوكيل", "delegatorEmailNumber": "البريد الإلكتروني للوكيل", "permitNumber": "رقم التصريح", "requestStatus": "حالة التصريح", "marineColor": "لون الواسطة", "engineType": "نوع المحرك", "permitActivityLocation": "موقع الأنشطة", "permitActivityLatitude": "Permit Activity Latitude", "permitActivityLogitude": "Permit Activity Logitude", "noOfPassengers": "<PERSON><PERSON><PERSON> الركاب", "noOfCrew": "<PERSON><PERSON><PERSON> الطاقم", "captainName": "إس<PERSON> القبطان", "captainMobileNumber": "رقم جوال القبطان", "anchorageArea": "منطقة الرسو", "chartererName": "اسم المستأجر ", "chartererNumber": "رقم التواصل للمستأجر", "startDate": "تاريخ بداية التصريح", "endDate": "تاريخ نهاية التصريح", "captainPassportNumber": "رقم جواز سفر القبطان", "arrivalDate": "تاريخ الوصول", "departureDate": "تاريخ المغادرة", "disclosureChecked": "Disclosure Checked", "passengers": "الركاب", "marineMedium": "الواسطة", "marineCompanyOwner": "الوكيل", "marineOwner": "المالك", "marineDelegatorUser": "الوكيل", "marineDelegatorCompany": "Marine Delegator Company", "arrivalPort": "الوجهه", "departurePort": "ميناء المغادرة", "captainNatinoality": "جنسية القبطان", "allRequests": "قائمة التصاريح", "permitRequests": "طلبات تصاريح المغادرة", "newRequests": " الطلبات الجديدة", "select": "إختيار", "returnRq": "إعادة الطلب لإستكمال المتطلبات", "approveRq": "قبول", "returnRqConfirm": "هل انت متاكد من إعادة الطلب ؟", "returnRqReasonConfirm": "اسباب إعادة الطلب ", "confirmReturn": " إعادة الطلب ", "activity": "الأنشطه التي ستتم ممارستها", "otherActivity": "الأنشطه الأخرى", "yacht_technical_license": "الترخيص الفني لليخت", "yacht_navigational_license": "الترخيص الملاحي لليخت", "valid_technical_inspection_report": "تقرير معاينة فنية ساري المفعول", "tourist_shipping_agent_license": "رخصة الوكيل الملاحي السياحي", "yacht_charter_company_license": "ترخيص شركة تأجير اليخوت", "owner_identification_data": "البيانات التعريفية للمالك", "registration_and_technical_certificates": "شهادة التسجيل والشهادات الفنية لليخت", "minimum_safe_manning_document": "وثيقة الحد الأدنى للتطقيم الامن", "marine_captain_certificate": "شهادة القبطان البحرية", "insurance_certificate": "شهادة التأمين", "docking_contract": "عقد رسو/ ترخيص الرسو", "approved_tenancy_contract": "عقد الإيجار المعتمد", "accessories": "الملحقات", "addAccessories": "اضافة ملحقات", "chassisNumber": "رقم الهيكل", "accessorieColor": "اللون", "noOfaccessorie": "العدد", "purpose": "الغرض من الزيارة", "visitDate": "م<PERSON><PERSON><PERSON> الفحص", "visitStatus": "حالة الفحص", "statusVisit": {"SCHEDULED": "مجدول", "COMPLETED": "مكتمل", "NOT_VISITED": "لم يتم الفحص الزيارة"}, "visitReschedule": "إعادة جدولة الفحص المكتبي", "visitComplete": "إتمام الفحص المكتبي", "visitEdit": "تعديل محضر وتقرير الفحص المكتبي", "visitSchedule": "ارسال للفحص المكتبي", "visitScheduleTitle": "تحديد موعد الفحص المكتبي", "confirmVisit": "تأكيد الفحص المكتبي", "uploadVisitDocs": "رفع محضر و تقرير الفحص المكتبي", "visitReport": "محضر وتقرير الفحص المكتبي", "visitRequired": "هل الطلب فى حاجة إلي فحص المكتبي؟", "navigationalData": "بيانات الترخيص الملاحي", "Contract_concluded_tourism_manager": "عقد مبرم مع الوكيل الملاحي السياحي معتمد من الهيئة", "Document_yacht_planned_route_geographical_area": "مُستند يوّضح خط سير اليخت المخطط له ومدة إقامة اليخت ومناطق الإبحار داخل النطاق الجغرافي", "Yacht_Registration_Certificate": "شهادة تسجيل اليخت", "Copy_contract_sidewalk": "نسخة من عقد الرصيف", "Crew_Data_List_Visas": "قائمة بيانات افراد الطاقم وتاشيراته", "Copy_yacht_wireless_license": "نسخة من رخصة اللاسلكي لليخت", "Insurance_document": "وثيقة التامين", "Copy_passports_crew_passengers": "نسخة من جوازات السفر للطاقم والركاب", "Testimonies_captain_crew_yacht": "شهادات الربان والطاقم المتواجدين على متن اليخت", "Passenger_Data_List": "قائمة بيانات الركاب", "General_Inspection_Report": "تقرير الفحص العام", "Certificate_Compliance_flag": "شهادة الامتثال الصادرة من دولة العلم", "Yacht_Owner_Identification_Data": "البيانات التعريفية لمالك اليخت", "EXCEPTIONAL_DOC": " مستند مبررات الإستثناء", "Tech_Visit_Report_DOC": "مستند الزيارة", "exceptionApproval": "هل الطلب معفي من الشروط", "feeRequired": "<PERSON><PERSON> يوجد رسوم للطلب", "yachtTechnicalLicense": "الترخيص الفني لليخت", "yachtNavigationLicense": "الترخيص الملاحي لليخت", "validTechnicalInspectionReport": "تقرير معاينة فنية ساري المفعول", "copyOfTouristMaritimeAgentLicense": "نسخة من رخصة الوكيل الملاحي السياحي", "copyOfOwnerIdentificationData": "نسخة من البيانات التعريفية للمالك", "yachtRegistrationAndTechnicalCertificates": "شهادة التسجيل والمعدات الفنية لليخت", "minimumSafeManningDocument": "وثيقة الحد الأدنى للتنظيم الآمن", "captainsMaritimeCertificate": "شهادة القبطان البحرية", "insuranceCertificate": "شهادة التأمين", "copyOfYachtRentalCompanyLicense": "نسخة من ترخيص شركة تأجير اليخوت", "mooringContractLicense": "عقد رسو / ترخيص", "approvedRentalContract": "عقد الإيجار المعتمد", "yachtRentalFacility": "حساب اعمال يمتلك رخصة منشأة تأجير اليخوت", "error": {"noValidLicense": "ليس لديك رخصة منشأة تأجير سياحي سارية المفعول. يرجى الحصول على ترخيص صالح قبل بدء الخدمة.", "checkLicense": "حدث خطأ أثناء التحقق من الترخيص. يرجى المحاولة مرة أخرى لاحقًا."}}}}