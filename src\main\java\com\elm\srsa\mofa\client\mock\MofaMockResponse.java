package com.elm.srsa.mofa.client.mock;

import com.elm.srsa.mofa.client.request.*;
import com.elm.srsa.mofa.client.response.ApplicantResponse;
import com.elm.srsa.mofa.client.response.VisaResponse;
import com.elm.srsa.mofa.client.response.VisaResponseInsurance;
import com.elm.srsa.mofa.client.response.VisaTourismApplication;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MofaMockResponse {

    public static List<String> getArabicCountryCodesMockResponse() {
        return Arrays.asList(
            "ARE",
            "BHR",
            "COM",
            "DJI",
            "DZA",
            "EGY",
            "IRQ",
            "JOR",
            "KWT",
            "LBN",
            "LBY",
            "MAR",
            "MRT",
            "OMN",
            "PSE",
            "QAT",
            "SAU",
            "SDN",
            "SYR",
            "TUN",
            "YEM",
            "NKW",
            "NBH",
            "NQT",
            "NEM",
            "NOM"
        );
    }

    public static VisaResponse issueVisa() {
        VisaResponse response = new VisaResponse();
        List<ApplicantResponse> applicantResponseList = new ArrayList<>();
        ApplicantResponse applicant = new ApplicantResponse();
        applicantResponseList.add(applicant);
        applicant.setPassportNumber("A963258");
        applicant.setVisaStatus(1);
        applicant.setVisaNumber(6061702076L);
        applicant.setApplicantNumber("E71144191");
        applicant.setErrors(null);

        VisaResponseInsurance insurance = new VisaResponseInsurance();
        insurance.setFee(null);
        insurance.setCompanyName(null);
        insurance.setDocumentUrl("https://textlink.com4241677");

        applicant.setInusrance(insurance);
        response.setReferenceNumber(12345641L);
        response.setApplicants(applicantResponseList);
        return response;
    }

    public static VisaTourismApplication submitTourismVisaRequest() {
        VisaTourismApplication visaTourismApplication = new VisaTourismApplication();
        visaTourismApplication.setIban("************************");

        // Create first applicant
        ApplicantDTO applicant1 = new ApplicantDTO();
        applicant1.setNavigationalLicenseNumber("NLN45654646546");
        applicant1.setFirstNameAr("محمد");
        applicant1.setFirstNameEn("Mohammed");
        applicant1.setFatherNameAr("أحمد");
        applicant1.setFatherNameEn("Ahmed");
        applicant1.setGrandFatherNameAr("علي");
        applicant1.setGrandFatherNameEn("Ali");
        applicant1.setFamilyNameAr("الدوسري");
        applicant1.setFamilyNameEn("Aldousari");
        applicant1.setPassportNo("*********");
        applicant1.setPassportType(3);
        applicant1.setIssuePassportCountry("IND");
        applicant1.setPassportIssueDate("2022-08-21");
        applicant1.setPassportExpiryDate("2025-09-22");
        applicant1.setBirthPlace("Moroco");
        applicant1.setBirthDate("1985-08-02");
        applicant1.setNationality("IND");
        applicant1.setPrevNationality("EGY");
        applicant1.setReligion(3);
        applicant1.setMaritalstatus(1);
        applicant1.setGender(1);
        applicant1.setJob("Engineer");
        applicant1.setEntryType(1);

        TravelHistoryDTO travel1 = new TravelHistoryDTO();
        travel1.setCountryCode("TUN");
        travel1.setPurpose("string");
        travel1.setFromDate("2022-08-22");
        travel1.setToDate("2026-08-22");
        applicant1.setPrevTravels(List.of(travel1));

        applicant1.setEmail("<EMAIL>");
        applicant1.setMobileNo("00966552718668");
        applicant1.setPersonalImage(
            "/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MsT/tjMZhctKRmnMqckN7+VKsTcdsOAyvS1eACUlaAOx9D8YzGYxYhGsx0NIKdvB/FY9xggLY3LUoFNifbGYzGLF//Z"
        );
        applicant1.setResidenceCountry("EGY");
        applicant1.setBirthCountry("EGY");
        applicant1.setDoYouWantDoUmra(false);
        applicant1.setVisaValidity(90);
        applicant1.setResidenceDuration(90);
        applicant1.setAddress("Egypt");
        applicant1.setApplicationNumber("App1020");

        InsuranceQuestionsDTO insurance1 = new InsuranceQuestionsDTO();
        insurance1.setAreYouDisabledByAccident(true);
        insurance1.setCurrentlyInHospitalOrTakingEmerTreatments(true);
        insurance1.setYouHaveWeaknessOrCongenitalPlatformation(true);
        insurance1.setAreYouPregnant(true);
        insurance1.setNoramalPregnancy(true);
        insurance1.setHowManyMonthsOfPregnancy(8);
        applicant1.setInsuranceQuestions(insurance1);

        SecurityQuestionsDTO security1 = new SecurityQuestionsDTO();

        SecurityQuestionDTO arrestedForMoney = new SecurityQuestionDTO();
        arrestedForMoney.setAnswer(false);
        security1.setHaveYouArrestedForMoneyCases(arrestedForMoney);

        SecurityQuestionDTO judged = new SecurityQuestionDTO();
        judged.setAnswer(true);
        judged.setClarification("I have judjed");
        security1.setHaveYouJudjedInYourCountry(judged);

        SecurityQuestionDTO served = new SecurityQuestionDTO();
        served.setAnswer(true);
        served.setClarification("No, Ididn't serve");
        security1.setHaveYouServedInArmyForces(served);

        security1.setHaveYouArrestedByInterpol(true);
        security1.setHaveYouDeportedFromKSAOrOtherCountry(true);

        SecurityQuestionDTO worked = new SecurityQuestionDTO();
        worked.setAnswer(false);
        security1.setHaveYouWorkedInMediaOrPoliticalField(worked);

        SecurityQuestionDTO belong = new SecurityQuestionDTO();
        belong.setAnswer(false);
        security1.setDoYouBelongToAnyTerroristOrganization(belong);

        security1.setDoesYouPassportContainsRestication(true);
        security1.setDoYouHaveAnyDisibility(true);
        security1.setHaveYouTokeVaccinations(true);
        security1.setDeliverVaccinationCertificate(true);
        applicant1.setSecurityQuestions(security1);

        applicant1.setExpectedEntryDate("2025-05-22");

        // Create second applicant
        ApplicantDTO applicant2 = new ApplicantDTO();
        applicant2.setFirstNameAr("محمد");
        applicant2.setFirstNameEn("Mohammed");
        applicant2.setFatherNameAr("أحمد");
        applicant2.setFatherNameEn("Ahmed");
        applicant2.setGrandFatherNameAr("علي");
        applicant2.setGrandFatherNameEn("Ali");
        applicant2.setFamilyNameAr("الدوسري");
        applicant2.setFamilyNameEn("Aldousari");
        applicant2.setPassportNo("AF963852");
        applicant2.setPassportType(3);
        applicant2.setIssuePassportCountry("EGY");
        applicant2.setPassportIssueDate("2022-08-21T05:03:37.113Z");
        applicant2.setPassportExpiryDate("2029-09-22T05:03:37.113Z");
        applicant2.setBirthPlace("Moroco");
        applicant2.setBirthDate("2000-08-02T05:03:37.113Z");
        applicant2.setNationality("EGY");
        applicant2.setPrevNationality("EGY");
        applicant2.setReligion(3);
        applicant2.setMaritalstatus(1);
        applicant2.setGender(1);
        applicant2.setJob("Engineer");
        applicant2.setEntryType(1);

        TravelHistoryDTO travel2 = new TravelHistoryDTO();
        travel2.setCountryCode("TUN");
        travel2.setPurpose("string");
        travel2.setFromDate("2022-08-22T05:03:37.113Z");
        travel2.setToDate("2022-08-22T05:03:37.113Z");
        applicant2.setPrevTravels(List.of(travel2));

        applicant2.setEmail("<EMAIL>");
        applicant2.setMobileNo("00966552718668");
        applicant2.setPersonalImage(
            "/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdzGYxYhGsx0NIKdvB/FY9xggLY3LUoFNifbGYzGLF//Z"
        );
        applicant2.setResidenceCountry("EGY");
        applicant2.setBirthCountry("EGY");
        applicant2.setDoYouWantDoUmra(false);
        applicant2.setVisaValidity(180);
        applicant2.setResidenceDuration(90);
        applicant2.setAddress("Egypt");

        InsuranceQuestionsDTO insurance2 = new InsuranceQuestionsDTO();
        insurance2.setAreYouDisabledByAccident(true);
        insurance2.setCurrentlyInHospitalOrTakingEmerTreatments(true);
        insurance2.setYouHaveWeaknessOrCongenitalPlatformation(true);
        insurance2.setAreYouPregnant(true);
        insurance2.setNoramalPregnancy(true);
        insurance2.setHowManyMonthsOfPregnancy(8);
        applicant2.setInsuranceQuestions(insurance2);

        SecurityQuestionsDTO security2 = new SecurityQuestionsDTO();

        SecurityQuestionDTO arrestedForMoney2 = new SecurityQuestionDTO();
        arrestedForMoney2.setAnswer(false);
        security2.setHaveYouArrestedForMoneyCases(arrestedForMoney2);

        SecurityQuestionDTO judged2 = new SecurityQuestionDTO();
        judged2.setAnswer(true);
        judged2.setClarification("I have judjed");
        security2.setHaveYouJudjedInYourCountry(judged2);

        SecurityQuestionDTO served2 = new SecurityQuestionDTO();
        served2.setAnswer(true);
        served2.setClarification("No, Ididn't serve");
        security2.setHaveYouServedInArmyForces(served2);

        security2.setHaveYouArrestedByInterpol(true);
        security2.setHaveYouDeportedFromKSAOrOtherCountry(true);

        SecurityQuestionDTO worked2 = new SecurityQuestionDTO();
        worked2.setAnswer(false);
        security2.setHaveYouWorkedInMediaOrPoliticalField(worked2);

        SecurityQuestionDTO belong2 = new SecurityQuestionDTO();
        belong2.setAnswer(false);
        security2.setDoYouBelongToAnyTerroristOrganization(belong2);

        security2.setDoesYouPassportContainsRestication(true);
        security2.setDoYouHaveAnyDisibility(true);
        security2.setHaveYouTokeVaccinations(true);
        security2.setDeliverVaccinationCertificate(true);
        applicant2.setSecurityQuestions(security2);

        applicant2.setExpectedEntryDate("2025-04-22");
        applicant2.setGuardianPassportNumber("*********");
        applicant2.setNavigationalLicenseNumber("NLN45654646546");
        applicant1.setApplicationNumber("App1040");
        visaTourismApplication.setApplicants(List.of(applicant1, applicant2));

        return visaTourismApplication;
    }

    public static List<CountryDTO> getAllCountriesMockResponse() {
        List<CountryDTO> countries = new ArrayList<>();
        countries.add(new CountryDTO("CZE", "جمهورية التشيك", "Czech Republic"));
        countries.add(new CountryDTO("DEU", "ألمانيا", "Germany"));
        countries.add(new CountryDTO("DJI", "جيبوتى", "Djibouti"));
        countries.add(new CountryDTO("DMA", "الدمنيكان", "Dominica"));
        countries.add(new CountryDTO("DNK", "الدنمارك", "Denmark"));
        countries.add(new CountryDTO("DOM", "جمهورية الدمنيكان", "Dominican Republic"));
        countries.add(new CountryDTO("DZA", "الجزائر", "Algeria"));
        countries.add(new CountryDTO("ECU", "الاكوادور", "Ecuador"));
        countries.add(new CountryDTO("EGY", "مصر", "Egypt"));
        countries.add(new CountryDTO("ESP", "أسبانيا", "Spain"));
        countries.add(new CountryDTO("EST", "استونيا", "Estonia"));
        countries.add(new CountryDTO("ETH", "إثيوبيــــا", "Ethiopia"));
        countries.add(new CountryDTO("FIN", "فنلاندا", "Finland"));
        countries.add(new CountryDTO("FJI", "فيجــــــى", "Fiji"));
        countries.add(new CountryDTO("FLK", "جزر - فوكلاند", "Falkland Islands"));
        countries.add(new CountryDTO("FRA", "فرنسا", "France"));
        countries.add(new CountryDTO("FRO", "جزر فاروه", "Faroe Islands"));
        countries.add(new CountryDTO("GAB", "الجابون", "Gabon"));
        countries.add(new CountryDTO("GBR", "المملكة المتحدة", "United Kingdom"));
        countries.add(new CountryDTO("GEO", "جورجيا", "Georgia"));
        countries.add(new CountryDTO("GHA", "غانـا", "Ghana"));
        countries.add(new CountryDTO("GIB", "جبل طــــارق", "Gibraltar"));
        countries.add(new CountryDTO("GIN", "غينيا", "Guinea"));
        countries.add(new CountryDTO("GLP", "جواد يلوب", "Guadeloupe"));
        countries.add(new CountryDTO("GMB", "جامبيا", "Gambia"));
        countries.add(new CountryDTO("GNB", "غينيا بيساو", "Guinea-Bissau"));
        countries.add(new CountryDTO("GNQ", "غينيا الأستوائية", "Equatorial Guinea"));
        countries.add(new CountryDTO("GRC", "اليونان", "Greece"));
        countries.add(new CountryDTO("GRD", "جرينادا", "Grenada"));
        countries.add(new CountryDTO("GRL", "جرين لاند", "Greenland"));
        countries.add(new CountryDTO("GTM", "جواتيمالا", "Guatemala"));
        countries.add(new CountryDTO("GUF", "جوانا الفرنسية", "French Guiana"));
        countries.add(new CountryDTO("GUM", "جوام", "Guam"));
        countries.add(new CountryDTO("GUY", "جويانا", "Guyana"));
        countries.add(new CountryDTO("RKS", "كوسوفا", "Kosovo"));
        countries.add(new CountryDTO("HMD", "جزيرة هيرد وجزيرة ماكدونالد", "Heard Island and McDonald Island"));
        countries.add(new CountryDTO("HND", "هندوراس", "Honduras"));
        countries.add(new CountryDTO("HRV", "كرواتيا", "Croatia"));
        countries.add(new CountryDTO("HTI", "هايتى", "Haiti"));
        countries.add(new CountryDTO("HUN", "المجر", "Hungary"));
        countries.add(new CountryDTO("IDN", "إندونسيا", "Indonesia"));
        countries.add(new CountryDTO("IND", "الهند", "India"));
        countries.add(new CountryDTO("IRL", "ايرلاندا", "Ireland"));
        countries.add(new CountryDTO("IRN", "ايران", "Iran"));
        countries.add(new CountryDTO("IRQ", "العراق", "Iraq"));
        countries.add(new CountryDTO("ISL", "ايسلاندا", "Iceland"));
        countries.add(new CountryDTO("ITA", "ايطاليا", "Italy"));
        countries.add(new CountryDTO("JAM", "جامايكا", "Jamaica"));
        countries.add(new CountryDTO("JOR", "الاردن", "Jordan"));
        countries.add(new CountryDTO("JPN", "اليابان", "Japan"));
        countries.add(new CountryDTO("KAZ", "كاذاخستان", "Kazakhstan"));
        countries.add(new CountryDTO("KEN", "كينيا", "Kenya"));
        countries.add(new CountryDTO("KGZ", "قرغيزستان", "Kyrgystan"));
        countries.add(new CountryDTO("KHM", "كامبوديا", "Cambodia"));
        countries.add(new CountryDTO("KIR", "كيريباتيا", "Kiribati"));
        countries.add(new CountryDTO("KNA", "سانت كيتس و نافيس", "Saint Kitts and Nevis"));
        countries.add(new CountryDTO("KOR", "كوريا الجنــوبية", "Korea , South"));
        countries.add(new CountryDTO("KWT", "الكويت", "Kuwait"));
        countries.add(new CountryDTO("LAO", "لاوس", "Laos"));
        countries.add(new CountryDTO("LBN", "لبنان", "Lebanon"));
        countries.add(new CountryDTO("LBR", "ليبيريا", "Liberia"));
        countries.add(new CountryDTO("LBY", "ليبيا", "Libya"));
        countries.add(new CountryDTO("LCA", "سانت لوتشيا", "Saint Lucia"));
        countries.add(new CountryDTO("LIE", "ليختشتاين", "Liechtenstein"));
        countries.add(new CountryDTO("LKA", "سريلانكا", "Sri Lanka"));
        countries.add(new CountryDTO("LSO", "ليسوتو", "Lesotho"));
        countries.add(new CountryDTO("LTU", "لتوانيا", "Lithuania"));
        countries.add(new CountryDTO("LUX", "لوكسمبورج", "Luxembourg"));
        countries.add(new CountryDTO("LVA", "لاتفيا", "Latvia"));
        countries.add(new CountryDTO("MAC", "ماكاو الصين", "Macau China"));
        countries.add(new CountryDTO("MAR", "المغرب", "Morocco"));
        countries.add(new CountryDTO("MCO", "موناكو", "Monaco"));
        countries.add(new CountryDTO("MDA", "جمهورية مالدوفا", "Republic of Moldova"));
        countries.add(new CountryDTO("MDG", "مدغـشقر", "Madagascar"));
        countries.add(new CountryDTO("MDV", "المالديف", "Maldives"));
        countries.add(new CountryDTO("MEX", "المكسيك\u200f", "Mexico"));
        countries.add(new CountryDTO("MLI", "مالى", "Mali"));
        countries.add(new CountryDTO("MLT", "مالطا", "Malta"));
        countries.add(new CountryDTO("MMR", "مينمار", "Myanmar"));
        countries.add(new CountryDTO("MNG", "منغوليا", "Mongolia"));
        countries.add(new CountryDTO("MOZ", "موزمبيق", "Mozambique"));
        countries.add(new CountryDTO("MRT", "موريتانيا", "Mauritania"));
        countries.add(new CountryDTO("MSR", "مونتسرات", "Montserrat"));
        countries.add(new CountryDTO("MTQ", "المارتينك", "Martinique"));
        countries.add(new CountryDTO("MUS", "موريشيوس", "Mauritius"));
        countries.add(new CountryDTO("MWI", "مالاوى", "Malawi"));
        countries.add(new CountryDTO("MYS", "ماليزيا", "Malaysia"));
        countries.add(new CountryDTO("MYT", "مايوت", "Mayotte"));
        countries.add(new CountryDTO("NAM", "نامبيا", "Namibia"));
        countries.add(new CountryDTO("NCL", "نيو - كاليدونيا", "New Caledonia"));
        countries.add(new CountryDTO("NER", "النيجر", "Niger"));
        countries.add(new CountryDTO("NFK", "جزيرة نورفولك", "Norfolk Island"));
        countries.add(new CountryDTO("NGA", "نيجيريا", "Nigeria"));
        countries.add(new CountryDTO("NIC", "نيكاراجوا", "Nicaragua"));
        countries.add(new CountryDTO("NIU", "نيوى جزر", "Niue"));
        countries.add(new CountryDTO("NLD", "هولندا", "Netherlands"));
        countries.add(new CountryDTO("NOR", "النرويج", "Norway"));
        countries.add(new CountryDTO("NPL", "نيبال", "Nepal"));
        countries.add(new CountryDTO("NRU", "ناورو", "Nauru"));
        countries.add(new CountryDTO("NZL", "نيوزيلاند", "New Zealand"));
        countries.add(new CountryDTO("OMN", "عمان", "Oman"));
        countries.add(new CountryDTO("PAK", "باكستان", "Pakistan"));
        countries.add(new CountryDTO("PAN", "بنما", "Panama"));
        countries.add(new CountryDTO("PCN", "جزر بيتكايرن", "Pitcairn Islands"));
        countries.add(new CountryDTO("PER", "بيرو", "Peru"));
        countries.add(new CountryDTO("PHL", "الفلبين", "Philippines"));
        countries.add(new CountryDTO("PNG", "بابوا - نيو - جونييا", "Papua New Guinea"));
        countries.add(new CountryDTO("POL", "بولندا", "Poland"));
        countries.add(new CountryDTO("PRI", "بورتوريكو", "Puerto Rico"));
        countries.add(new CountryDTO("PRK", "كوريا الشمالية", "Korea, North"));
        countries.add(new CountryDTO("PRT", "البرتغال", "Portugal"));
        countries.add(new CountryDTO("PRY", "باراجواى", "Paraguay"));
        countries.add(new CountryDTO("PYF", "بولينيزيا الفرنسية", "French Polynesia"));
        countries.add(new CountryDTO("QAT", "قطر", "Qatar"));
        countries.add(new CountryDTO("REU", "رينو", "Reunion"));
        countries.add(new CountryDTO("ROU", "رومانيا", "Romania"));
        countries.add(new CountryDTO("RUS", "روسيا الإتحادية", "Russian Federation"));
        countries.add(new CountryDTO("RWA", "روانــــــــــدا", "Rwanda"));
        countries.add(new CountryDTO("SAU", "المملكة العربية السعودية", "Kingdom Saudi Arabia"));
        countries.add(new CountryDTO("SDN", "السودان", "Sudan"));
        countries.add(new CountryDTO("SEN", "السنغال", "Senegal"));
        countries.add(new CountryDTO("SGP", "سنغافورة", "Singapore"));
        countries.add(new CountryDTO("SGS", "جنوب جورجيا", "South Georgia and The South"));
        countries.add(new CountryDTO("SJM", "سفلبارد", "Svalbard"));
        countries.add(new CountryDTO("SLB", "جزر سليمــــان", "Solomon Islands"));
        countries.add(new CountryDTO("SLE", "سيراليون", "Sierra Leone"));
        countries.add(new CountryDTO("SLV", "السلفادور", "El Salvador"));
        countries.add(new CountryDTO("SMR", "سان مارينو", "San Marino"));
        countries.add(new CountryDTO("SOM", "الصومال", "Somalia"));
        countries.add(new CountryDTO("SPM", "سانت بيير و ميجولين", "Saint Pierre and Miquelon"));
        countries.add(new CountryDTO("STP", "ساو - توم - وبرنسيب", "Sao Tome And Principe"));
        countries.add(new CountryDTO("SUR", "سورينام", "Suriname"));
        countries.add(new CountryDTO("SVK", "جمهورية سلوفاكيا", "Slovak Republic"));
        countries.add(new CountryDTO("SVN", "سلوفينيا", "Slovenia"));
        countries.add(new CountryDTO("SWE", "السويد", "Sweden"));
        countries.add(new CountryDTO("SWZ", "إسواتيني", "Eswatini"));
        countries.add(new CountryDTO("SYC", "سيشل", "Seychelles"));
        countries.add(new CountryDTO("SYR", "سوريا", "Syrian"));
        countries.add(new CountryDTO("TCA", "جزر تركس وكايكوس", "Turks and Caicos Islands"));
        countries.add(new CountryDTO("TCD", "تشاد", "Chad"));
        countries.add(new CountryDTO("TGO", "توجو", "Togo"));
        countries.add(new CountryDTO("THA", "تايلاند", "Thailand"));
        countries.add(new CountryDTO("TJK", "طاجكستان", "Tajikistan"));
        countries.add(new CountryDTO("TKL", "توكيلاو", "Tokelau"));
        countries.add(new CountryDTO("TKM", "تركمنستان", "Turkmenistan"));
        countries.add(new CountryDTO("TON", "تونجــا", "Tonga"));
        countries.add(new CountryDTO("TTO", "ترينيداد وتوباجو", "Trinidad and Tobago"));
        countries.add(new CountryDTO("TUN", "تونس", "Tunisia"));
        countries.add(new CountryDTO("TUR", "تركيا", "Turkey"));
        countries.add(new CountryDTO("TUV", "توفالــــــــو", "Tuvalu"));
        countries.add(new CountryDTO("TWN", "تايوان الصين", "Taiwan China"));
        countries.add(new CountryDTO("TZA", "تنزانيا", "UR Tanzania"));
        countries.add(new CountryDTO("UGA", "أوغندا", "Uganda"));
        countries.add(new CountryDTO("UKR", "اكرانيا", "Ukraine"));
        countries.add(new CountryDTO("URY", "أورجواى", "Uruguay"));
        countries.add(new CountryDTO("USA", "الولايات المتحدة الامريكية", "United States"));
        countries.add(new CountryDTO("UZB", "اوزبكستان", "Uzbekistan"));
        countries.add(new CountryDTO("VCT", "سانت فينسنت و جرينادين", "Saint Vincent and the Grenadines"));
        countries.add(new CountryDTO("VEN", "فنزويلا", "Venezuela"));
        countries.add(new CountryDTO("VGB", "جزر فرجين", "Virgin Islands"));
        countries.add(new CountryDTO("VNM", "فييتنام", "Vietnam"));
        countries.add(new CountryDTO("VUT", "فانواتو", "Vanuatu"));
        countries.add(new CountryDTO("WLF", "واليز فوتونا", "Wallis and Futuna"));
        countries.add(new CountryDTO("YEM", "اليمن", "Yemen"));
        countries.add(new CountryDTO("ZAF", "جنوب أفريقيا", "South Africa"));
        countries.add(new CountryDTO("ZMB", "زامبيا", "Zambia"));
        countries.add(new CountryDTO("ZWE", "زيمبابوى", "Zimbabwe"));
        countries.add(new CountryDTO("ERI", "أريتيريا", "Eritrea"));
        countries.add(new CountryDTO("WSM", "ساموا", "Samoa"));
        countries.add(new CountryDTO("MNP", "جزر ماريانا الشمالية", "Northern Mariana Islands"));
        countries.add(new CountryDTO("FSM", "ولاية مكرونسيا الأتحادية", "Micronesia , Federated Stat"));
        countries.add(new CountryDTO("MHL", "جزر مارشال", "Marshall Islands"));
        countries.add(new CountryDTO("BWA", "بوتسوانا", "Botswana"));
        countries.add(new CountryDTO("VIR", "جزر فيرجينيا", "Virgin Islands(U.S.)"));
        countries.add(new CountryDTO("SHN", "هيلينا", "ST. Helena"));
        countries.add(new CountryDTO("PSE", "فلسطين", "Palestinian Territory, Occupied"));
        countries.add(new CountryDTO("PLW", "بالو", "Palau"));
        countries.add(new CountryDTO("MKD", "جمهورية شمال مقدونيا", "North Macedonia"));
        countries.add(new CountryDTO("FXX", "ميتروبوليتان الفرنسية", "France, Metropolitan"));
        countries.add(new CountryDTO("UMI", "جزر مينور", "United States Minor Outlying Islands"));
        countries.add(new CountryDTO("VAT", "الفاتيكان", "Vatican City State"));
        countries.add(new CountryDTO("IOT", "المقاطعة البريطانية في المحيط الهندي", "British Indian Ocean Territory"));
        countries.add(new CountryDTO("NKW", "غير كويتي", "Non-Kuwaiti"));
        countries.add(new CountryDTO("NBH", "غير بحريني", "Non-Bahraini"));
        countries.add(new CountryDTO("NQT", "غير قطري", "Non-Qatari"));
        countries.add(new CountryDTO("NEM", "غير اماراتي", "Non-Emirati"));
        countries.add(new CountryDTO("NOM", "غير عماني", "Non-Omani"));
        countries.add(new CountryDTO("ABW", "آروبا", "Aruba"));
        countries.add(new CountryDTO("AFG", "أفغانستان", "Afghanistan"));
        countries.add(new CountryDTO("AGO", "أنجولا", "Angola"));
        countries.add(new CountryDTO("AIA", "أنجويلا", "Anguilla"));
        countries.add(new CountryDTO("ALB", "ألبانيا", "Albania"));
        countries.add(new CountryDTO("AND", "اندورا", "Andorra"));
        countries.add(new CountryDTO("ARE", "الإمارات العربية المتحدة", "United Arab Emirates"));
        countries.add(new CountryDTO("ARG", "الارجنتين", "Argentina"));
        countries.add(new CountryDTO("ARM", "ارمينيا", "Armenia"));
        countries.add(new CountryDTO("ASM", "ساموا الأمريكية", "American Samoa"));
        countries.add(new CountryDTO("ATA", "القطب الجنوبى", "Antarctic"));
        countries.add(new CountryDTO("ATF", "فرنسا الجنوبية والقطبية", "French Southern and Antarctic"));
        countries.add(new CountryDTO("ATG", "انتيجوا و باربودا", "Antigua"));
        countries.add(new CountryDTO("AUT", "النمسا", "Austria"));
        countries.add(new CountryDTO("AUS", "استراليا", "Australia"));
        countries.add(new CountryDTO("AZE", "اذربيجان", "Azerbaijan"));
        countries.add(new CountryDTO("BDI", "بوروندى", "Burundi"));
        countries.add(new CountryDTO("BEL", "بلجيكا", "Belgium"));
        countries.add(new CountryDTO("BEN", "بنين", "Benin"));
        countries.add(new CountryDTO("BFA", "بوركينا", "Burkina Faso"));
        countries.add(new CountryDTO("BGD", "بنجلاديش", "Bangladesh"));
        countries.add(new CountryDTO("BGR", "بلغاريا", "Bulgaria"));
        countries.add(new CountryDTO("BHR", "البحرين", "Bahrain"));
        countries.add(new CountryDTO("BHS", "جزر البهاما", "Bahamas"));
        countries.add(new CountryDTO("BIH", "البوسنة", "Bosnia"));
        countries.add(new CountryDTO("BLR", "بيلاروس", "Belarus"));
        countries.add(new CountryDTO("BLZ", "بليز", "Belize"));
        countries.add(new CountryDTO("BMU", "برمودا", "Bermuda"));
        countries.add(new CountryDTO("BOL", "بوليفيا", "Bolivia"));
        countries.add(new CountryDTO("BRA", "البرازيل", "Brazil"));
        countries.add(new CountryDTO("BRB", "باربادوس", "Barbados"));
        countries.add(new CountryDTO("BRN", "بروناى", "Brunei"));
        countries.add(new CountryDTO("BTN", "بوتان", "Bhutan"));
        countries.add(new CountryDTO("BVT", "جزيرة بوفيت", "Bouvet Island"));
        countries.add(new CountryDTO("CAF", "جمهورية وسط أفريقيا", "Central African Republic"));
        countries.add(new CountryDTO("CAN", "كندا", "Canada"));
        countries.add(new CountryDTO("CCK", "جزر كوكوس", "Cocos Island"));
        countries.add(new CountryDTO("CHE", "سويسرا", "Switzerland"));
        countries.add(new CountryDTO("CHL", "تشيلي", "Chile"));
        countries.add(new CountryDTO("CHN", "الصين", "China"));
        countries.add(new CountryDTO("CIV", "كوت دي فوار", "Cote Divoire"));
        countries.add(new CountryDTO("CMR", "الكاميرون", "Cameroon"));
        countries.add(new CountryDTO("COG", "الكنغو", "Congo"));
        countries.add(new CountryDTO("COK", "جزر كوك", "Cook Island"));
        countries.add(new CountryDTO("COL", "كولومبيا", "Colombia"));
        countries.add(new CountryDTO("COM", "جزر القمر", "Comoros"));
        countries.add(new CountryDTO("CPV", "كاب فيرد", "Cape Verde"));
        countries.add(new CountryDTO("CRI", "كوستاريكا", "Costa Rica"));
        countries.add(new CountryDTO("CUB", "كوبا", "Cuba"));
        countries.add(new CountryDTO("CXR", "جزيرة كريسماس", "Christmas Island"));
        countries.add(new CountryDTO("CYM", "جزر كايمان", "Cayman Island"));
        countries.add(new CountryDTO("CYP", "قبرص", "Cyprus"));
        countries.add(new CountryDTO("COD", "جمهورية الكنغو الديمقراطية", "Democratic Republic of the Congo"));
        countries.add(new CountryDTO("SSD", "جمهورية جنوب السودان", "Republic of South Sudan"));
        countries.add(new CountryDTO("ANT", "انتيل الهولندية", "Netherlands Antilles"));
        countries.add(new CountryDTO("HKG", "هونج كونج الصين", "Hong Kong China"));
        countries.add(new CountryDTO("MNE", "الجبل الاسود", "MONTENEGRO"));
        countries.add(new CountryDTO("SRB", "صربيا", "SERBIA"));
        countries.add(new CountryDTO("TLS", "تيمور - لشتي", "Timor-Leste"));

        return countries;
    }
}
