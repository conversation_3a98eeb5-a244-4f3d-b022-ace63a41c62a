package com.elm.srsa.service;

import com.elm.srsa.domain.*;
import com.elm.srsa.repository.CompanyAttachmentRepository;
import com.elm.srsa.repository.ProfileUpdateRequestRepository;
import com.elm.srsa.repository.UserAttachmentRepository;
import com.elm.srsa.security.AuthoritiesConstants;
import com.elm.srsa.security.SecurityUtils;
import com.elm.srsa.service.dto.*;
import com.elm.srsa.service.mapper.*;
import com.elm.srsa.util.Constants;
import com.elm.srsa.web.rest.request.UpdateProfileRequest;
import com.elm.srsa.yakeen.rest.client.response.YakeenResponse;
import com.elm.srsa.yakeen.rest.client.response.model.PersonBasicInfo;
import com.elm.srsa.yakeen.rest.client.response.model.PersonIdInfo;
import java.io.IOException;
import java.nio.file.AccessDeniedException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Transactional
public class ProfileManagementService {

    private final UserService userService;
    private final CompanyService companyService;
    private final CompanyMapper companyMapper;
    private final ProfileUpdateRequestRepository updateRequestRepository;
    private final PasswordEncoder passwordEncoder;
    private final CountryMapper countryMapper;
    private final CountryService countryService;
    private final AttachmentService attachmentService;
    private final UserAttachmentRepository userAttachmentRepository;
    private final RelationshipWithOrganizationTypesMapper relationshipWithOrganizationTypesMapper;
    private final CompanyActivityService companyActivityService;
    private final CompanyActivityMapper companyActivityMapper;
    private final CompanyAttachmentRepository companyAttachmentRepository;
    private final GovEmployeeTypeMapper govEmployeeTypeMapper;

    public ProfileManagementService(
        UserService userService,
        CompanyService companyService,
        CompanyMapper companyMapper,
        ProfileUpdateRequestRepository updateRequestRepository,
        PasswordEncoder passwordEncoder,
        CountryMapper countryMapper,
        CountryService countryService,
        AttachmentService attachmentService,
        UserAttachmentRepository userAttachmentRepository,
        RelationshipWithOrganizationTypesMapper relationshipWithOrganizationTypesMapper,
        CompanyActivityService companyActivityService,
        CompanyAttachmentRepository companyAttachmentRepository,
        GovEmployeeTypeMapper govEmployeeTypeMapper,
        CompanyActivityMapper companyActivityMapper
    ) {
        this.userService = userService;
        this.companyService = companyService;
        this.companyMapper = companyMapper;
        this.updateRequestRepository = updateRequestRepository;
        this.passwordEncoder = passwordEncoder;
        this.countryMapper = countryMapper;
        this.countryService = countryService;
        this.attachmentService = attachmentService;
        this.userAttachmentRepository = userAttachmentRepository;
        this.relationshipWithOrganizationTypesMapper = relationshipWithOrganizationTypesMapper;
        this.companyActivityService = companyActivityService;
        this.companyAttachmentRepository = companyAttachmentRepository;
        this.govEmployeeTypeMapper = govEmployeeTypeMapper;
        this.companyActivityMapper = companyActivityMapper;
    }

    public UserProfileDTO getUserProfile(String userLogin, String userRegType) throws AccessDeniedException {
        Optional<User> userByLogin = userService.getUserByLogin(userLogin, userRegType);

        if (userByLogin.isEmpty()) {
            return null;
        }
        User user = userByLogin.get();
        if (!user.getId().equals(SecurityUtils.getCurrentUser().getUserID())) {
            throw new AccessDeniedException("User not authorized");
        }
        UserInfo userInfo = user.getUserInfo();
        UserProfileDTO userProfile = new UserProfileDTO();
        userProfile.setUserLogin(userLogin);
        userProfile.setAccountStatus(user.getRecordActivity());
        userProfile.setAccountCreationDate(LocalDateTime.ofInstant(user.getCreatedDate(), ZoneOffset.UTC).toLocalDate());
        userProfile.setIdNumber(userInfo.getIdNumber());
        userProfile.setIdNumberExpiryDate(userInfo.getIdExpirationDateG());
        userProfile.setFullNameAr(userInfo.getFullNameAr());
        userProfile.setFullNameEn(userInfo.getFullNameEn());
        userProfile.setBirthDateG(userInfo.getBirthDateG());
        userProfile.setBirthDateH(userInfo.getBirthDateH());
        userProfile.setEmail(user.getEmail());
        userProfile.setMobile(userInfo.getMobile());
        userProfile.setGender(userInfo.getGender());
        userProfile.setNationality(userInfo.getNationality());
        userProfile.setUserType(userInfo.getUserType().getAuthorityName());
        List<UserAttachmentDTO> userAttachments = userService.getUserAttachments(userInfo.getId());
        userProfile.setPassportFullName(userInfo.getPassportFullName());
        userProfile.setNationalNumber(userInfo.getNationalNumber());
        userProfile.setPassportNumber(userInfo.getPassportNumber());
        userProfile.setPassportExpiryDateG(userInfo.getPassportExpiryDateG());
        userProfile.setPassportExpiryDateH(userInfo.getPassportExpiryDateH());
        userProfile.setAttachments(userAttachments);
        userProfile.setCountryIssuingPassport(countryMapper.toDto(userInfo.getCountryIssuingPassport()));
        userProfile.setNationalityCountry(countryMapper.toDto(userInfo.getNationalityCountry()));
        userProfile.setUserRegType(userInfo.getUserRegType());
        if (
            userInfo.getUserType().getAuthorityName().equals(AuthoritiesConstants.SERVICE_PROVIDER) ||
            userInfo.getUserType().getAuthorityName().equals(AuthoritiesConstants.SERVICE_PROVIDER_NON_FOREIGN) ||
            userInfo.getUserType().getAuthorityName().equals(AuthoritiesConstants.SERVICE_PROVIDER_FOREIGN)
        ) {
            Company userCompany = companyService.getUserCompany(user);
            userProfile.setCompany(companyMapper.toDto(userCompany));
            userProfile.setRelationshipWithOrganization(
                relationshipWithOrganizationTypesMapper.toDto(userInfo.getRelationshipWithOrganization())
            );
        }
        if (userInfo.getUserRegType().equals(Constants.USER_TYPE_SP_USR)) {
            Company userCompany = companyService.getUserCompany(user);
            CompanyDTO companyDTO = companyMapper.toDto(userCompany);
            List<CompanyActivityDTO> companyActivityDTOS = companyActivityMapper.toDto(
                companyActivityService.getCompanyActivities(companyDTO.getId())
            );
            companyDTO.setActivities(companyActivityDTOS);
            userProfile.setCompany(companyDTO);

            if (userInfo.getUserType().getAuthorityName().equals(AuthoritiesConstants.SERVICE_PROVIDER_FOREIGN)) {
                // get relationship
                userProfile.setRelationshipWithOrganization(
                    relationshipWithOrganizationTypesMapper.toDto(userInfo.getRelationshipWithOrganization())
                );
                // get company attachment
                List<CompanyAttachmentDTO> companyAttachments = companyService.getCompanyAttachmentsDTO(userCompany.getId());
                userProfile.getCompany().setAttachments(companyAttachments);
            }
        }
        if (userInfo.getUserRegType().equals(Constants.USER_TYPE_G_USR)) {
            userProfile.setGovEmployeeType(govEmployeeTypeMapper.toDto(userInfo.getGovEmployeeType()));
        }
        return userProfile;
    }

    public void updateIndividualForeignInfo(
        UpdateProfileRequest updateProfileRequest,
        User user,
        MultipartFile[] passportImage,
        MultipartFile[] nationalImage
    ) {
        Optional<UserInfo> userInfoOptional = userService.getUserInfo(user.getId());

        if (userInfoOptional.isPresent()) {
            UserInfo userInfo = userInfoOptional.get();
            List<UserAttachment> userAttachment = new ArrayList<>();
            if (passportImage != null) {
                for (MultipartFile multipartFile : passportImage) {
                    try {
                        if (multipartFile.getBytes().length >= 1) {
                            attachmentService.saveUserFileAndReturnPath(
                                Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                                multipartFile.getOriginalFilename(),
                                userInfo,
                                userAttachment,
                                Constants.ATTACHMENT_MAX_SIZE_MB
                            );
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("fail to save Passport image in creating user  : " + e.getMessage());
                    }
                }
            }
            if (nationalImage != null) {
                for (MultipartFile multipartFile : nationalImage) {
                    try {
                        if (multipartFile.getBytes().length >= 1) {
                            attachmentService.saveUserFileAndReturnPath(
                                Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                                multipartFile.getOriginalFilename(),
                                userInfo,
                                userAttachment,
                                Constants.ATTACHMENT_MAX_SIZE_MB
                            );
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("fail to save National ID image in creating user  : " + e.getMessage());
                    }
                }
            }

            userInfo.setPassportFullName(updateProfileRequest.getPassportFullName());
            userInfo.setPassportNumber(updateProfileRequest.getPassportNumber());
            if (updateProfileRequest.getPassportExpiryDateGregorian() != null) {
                LocalDate passportExpiryDateG = LocalDate.parse(
                    updateProfileRequest.getPassportExpiryDateGregorian().split(Constants.CHAR_T)[Constants.INDEX_ZERO],
                    DateTimeFormatter.ISO_LOCAL_DATE
                );
                userInfo.setPassportExpiryDateG(passportExpiryDateG);
            }
            userInfo.setPassportExpiryDateH(updateProfileRequest.getPassportExpiryDateHijri());
            //            userInfo.setNationalNumber(updateProfileRequest.getNationalNumber());
            userInfo.setGender(updateProfileRequest.getGender());
            if (updateProfileRequest.isHijri()) {
                LocalDate birthdate = LocalDate.parse(
                    updateProfileRequest.getBirthDate().split(Constants.CHAR_T)[Constants.INDEX_ZERO],
                    DateTimeFormatter.ISO_LOCAL_DATE
                );
                userInfo.setBirthDateH(null);
                userInfo.setBirthDateG(birthdate);
            } else {
                userInfo.setBirthDateG(null);
                userInfo.setBirthDateH(updateProfileRequest.getBirthDate());
            }
            Optional<Country> optionalCountryIssuingPassport = countryService.findById(
                Long.valueOf(updateProfileRequest.getPassportCountryId())
            );
            optionalCountryIssuingPassport.ifPresent(userInfo::setCountryIssuingPassport);

            //            Optional<Country> optionalNationalityCountry = countryService.findById(Long.valueOf(updateProfileRequest.getNationalityId()));
            //            optionalNationalityCountry.ifPresent(userInfo::setNationalityCountry);

            userService.updateUserInfo(userInfo);
            // remove old attachments
            Optional<List<UserAttachment>> byUserInfoId = userAttachmentRepository.findByUserInfoId(userInfo.getId());
            byUserInfoId.ifPresent(userAttachments -> userAttachments.forEach(attachment -> attachment.setRecordActivity(false)));
            // add new attachments
            userAttachment.forEach(attachment -> attachment.setUserInfo(userInfo));
            userAttachmentRepository.saveAll(userAttachment);
        }
    }

    public void updateGCCIndividualProfile(UpdateProfileRequest updateProfileRequest, UserInfo userInfo, YakeenResponse yakeenResponse) {
        if (yakeenResponse.getPersonBasicInfo() != null || yakeenResponse.getPersonIdInfo() != null) {
            PersonBasicInfo personBasicInfo = yakeenResponse.getPersonBasicInfo();
            PersonIdInfo personIdInfo = yakeenResponse.getPersonIdInfo();

            //                userInfo.setIdNumber(updateProfileRequest.getNationalNumber());
            //                userInfo.setNationalNumber(updateProfileRequest.getNationalNumber());
            userInfo.setPassportNumber(updateProfileRequest.getPassportNumber());
            LocalDate idExpiryDateG = LocalDate.parse(
                personIdInfo.getIdExpirationDate().split(Constants.CHAR_T)[Constants.INDEX_ZERO],
                DateTimeFormatter.ISO_LOCAL_DATE
            );
            userInfo.setPassportExpiryDateG(idExpiryDateG);
            userInfo.setPassportExpiryDateH(personIdInfo.getIdExpirationDateH());
            //            Optional<Country> byId = countryService.findById(Long.valueOf(updateProfileRequest.getNationalityId()));
            //            if (byId.isPresent()) {
            //                Country country = byId.get();
            //                userInfo.setNationalityCountry(country);
            //                userInfo.setNationality(country.getNameAr());
            //            }
            userInfo.setFirstName(personBasicInfo.getFirstName());
            userInfo.setSecondName(personBasicInfo.getFatherName());
            userInfo.setFamilyName(personBasicInfo.getFamilyName());
            userInfo.setFirstNameEn(personBasicInfo.getFirstNameT());
            userInfo.setSecondNameEn(personBasicInfo.getFatherNameT());
            userInfo.setFamilyNameEn(personBasicInfo.getFamilyNameT());
            userInfo.setGender(personBasicInfo.getSexDescAr());
            if (updateProfileRequest.isHijri()) {
                userInfo.setBirthDateG(null);
                userInfo.setBirthDateH(updateProfileRequest.getBirthDate());
            } else {
                userInfo.setBirthDateH(null);
                userInfo.setBirthDateG(
                    LocalDate.parse(
                        updateProfileRequest.getBirthDate().split(Constants.CHAR_T)[Constants.INDEX_ZERO],
                        DateTimeFormatter.ISO_LOCAL_DATE
                    )
                );
            }
            userService.updateUserInfo(userInfo);
        }
    }

    public void updateForeignOrganizationInfo(
        UpdateProfileRequest updateProfileRequest,
        User user,
        MultipartFile[] commercialLicenseCertificate
    ) {
        Company userCompany = companyService.getUserCompany(user);

        userCompany.setCrNumber(updateProfileRequest.getOrganizationReferenceNumber());
        userCompany.setCrName(updateProfileRequest.getOrganizationName());
        userCompany.setEntityIssuingCommercialLicense(updateProfileRequest.getEntityIssuingCommercialLicense());

        userCompany.setCrEntityNumber(updateProfileRequest.getOrganizationReferenceNumber());
        userCompany.setCrMainEntityNumber(updateProfileRequest.getOrganizationReferenceNumber());
        userCompany.setCrMainNumber(updateProfileRequest.getOrganizationReferenceNumber());
        userCompany.setCrExpiryDate(updateProfileRequest.getCommercialLicenseExpiryDate());

        Optional<Country> optionalCountry = countryService.findById(updateProfileRequest.getCompanyNationalityId());
        optionalCountry.ifPresent(userCompany::setCompanyNationality);

        List<CompanyActivity> companyActivities = companyActivityService.getCompanyActivities(userCompany.getId());
        companyActivities.get(0).setDescription(updateProfileRequest.getBusinessDescription());

        // save company changes
        companyService.saveCompany(companyMapper.toDto(userCompany));
        // attachment
        List<CompanyAttachment> companyAttachments = new ArrayList<>();
        if (commercialLicenseCertificate != null) {
            for (MultipartFile multipartFile : commercialLicenseCertificate) {
                try {
                    if (multipartFile.getBytes().length >= 1) {
                        attachmentService.saveCompanyFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            userCompany,
                            companyAttachments,
                            Constants.ATTACHMENT_MAX_SIZE_MB
                        );
                    }
                } catch (IOException e) {
                    throw new RuntimeException("fail to save Commercial License Certificate in creating company : " + e.getMessage());
                }
            }
            // remove old
            List<CompanyAttachment> oldCompanyAttachments = companyService.getCompanyAttachments(userCompany.getId());
            oldCompanyAttachments.forEach(attachment -> attachment.setRecordActivity(false));
            companyAttachmentRepository.saveAll(oldCompanyAttachments);
            // save new
            companyAttachments.forEach(attachment -> attachment.setCompany(userCompany));
            companyAttachmentRepository.saveAll(companyAttachments);
        }
    }

    public void updateProfile(ProfileUpdateRequest profileUpdateRequest) {
        User user = profileUpdateRequest.getUser();
        switch (profileUpdateRequest.getRequestType()) {
            case EMAIL_UPDATE -> {
                user.setEmail(profileUpdateRequest.getEmail());
                userService.updateUser(user);
            }
            case MOBILE_UPDATE -> {
                Optional<UserInfo> userInfoOptional = userService.getUserInfo(user.getId());
                if (userInfoOptional.isPresent()) {
                    UserInfo userInfo = userInfoOptional.get();
                    userInfo.setMobile(profileUpdateRequest.getMobile());
                    userService.updateUserInfo(userInfo);
                }
            }
        }
        profileUpdateRequest.setRecordActivity(false);
        updateRequestRepository.save(profileUpdateRequest);
    }

    public void updatePassword(User user, String newPassword) {
        user.setPassword(passwordEncoder.encode(newPassword));
        userService.updateUser(user);
    }

    public Long createUpdateUserProfileRequest(ProfileUpdateRequest.RequestType requestType, String newValue, User user) {
        ProfileUpdateRequest profileUpdateRequest = ProfileUpdateRequest.builder().requestType(requestType).user(user).build();
        switch (requestType) {
            case EMAIL_UPDATE -> profileUpdateRequest.setEmail(newValue);
            case MOBILE_UPDATE -> profileUpdateRequest.setMobile(newValue);
        }
        return updateRequestRepository.save(profileUpdateRequest).getId();
    }

    public ProfileUpdateRequest getUpdateRequest(long updateRequestId, ProfileUpdateRequest.RequestType requestType) {
        return updateRequestRepository.findByIdAndRequestType(updateRequestId, requestType).orElse(null);
    }

    public UserAttachment getUserAttachmentByIdAndCurrentUserId(long attachmentId) {
        Optional<UserAttachment> optionalUserAttachment = userAttachmentRepository.findByIdAndUserInfoUserId(
            attachmentId,
            SecurityUtils.getCurrentUser().getUserID()
        );
        return optionalUserAttachment.orElse(null);
    }
}
