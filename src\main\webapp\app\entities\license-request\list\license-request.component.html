<div>
  <h2 id="page-heading" data-cy="LicenseRequestHeading">
    <span jhiTranslate="srsaApp.licenseRequest.home.title">License Requests</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [spin]="isLoading"></fa-icon>
        <span jhiTranslate="srsaApp.licenseRequest.home.refreshListLabel">Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-license-request"
        [routerLink]="['/license-request/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="srsaApp.licenseRequest.home.createLabel">Create a new License Request</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (licenseRequests?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="srsaApp.licenseRequest.home.notFound">No License Requests found</span>
    </div>
  }

  @if (licenseRequests && licenseRequests.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped dga-table" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="id">
              <div class="d-flex">
                <span jhiTranslate="global.field.id">ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfYearsExperience">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfYearsExperience">No Of Years Experience</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfCountryCompanyOpr">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfCountryCompanyOpr">No Of Country Company Opr</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfManagedMaritimeMediums">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfManagedMaritimeMediums">No Of Managed Maritime Mediums</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfOwnedMaritimeMediumsWorld">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfOwnedMaritimeMediumsWorld">No Of Owned Maritime Mediums Ksa World</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfOwnedMaritimeMediumsKsa">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfOwnedMaritimeMediumsKsa">No Of Owned Maritime Mediums Ksa World</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfManagedMaritimeMediumsGeneral">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfManagedMaritimeMediumsGeneral">No Of Managed Maritime Mediums General</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="requestedLicenseDuration">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.requestedLicenseDuration">Requested License Duration</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="marinaOwnerName">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.marinaOwnerName">Marina Owner Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="marinaCrNumber">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.marinaCrNumber">Marina Cr Number</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="marinaCrNameAr">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.marinaCrNameAr">Marina Cr Name Ar</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="marinaCrNameEn">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.marinaCrNameEn">Marina Cr Name En</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="numberOfDocks">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.numberOfDocks">Number Of Docks</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="dockLength">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.dockLength">Dock Length</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="dockDepth">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.dockDepth">Dock Depth</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfWaterStations">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfWaterStations">No Of Water Stations</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfWaterStationsPerPlatform">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfWaterStationsPerPlatform">No Of Water Stations Per Platform</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfFuelStations">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfFuelStations">No Of Fuel Stations</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfWasteStations">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfWasteStations">No Of Waste Stations</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfWasteStationsPerPlatform">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfWasteStationsPerPlatform">No Of Waste Stations Per Platform</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfElectricChargingStations">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfElectricChargingStations">No Of Electric Charging Stations</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfControlTowers">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfControlTowers">No Of Control Towers</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="noOfMarinaFacilities">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.noOfMarinaFacilities">No Of Marina Facilities</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="boatsCapacity">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.boatsCapacity">Boats Capacity</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="yachtsCapacity">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.yachtsCapacity">Yachts Capacity</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="otherMaritimeMediumCapacity">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.otherMaritimeMediumCapacity">Other Maritime Medium Capacity</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="marinaLocation.id">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.marinaLocation">Marina Location</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="maritimeTourismAgent.id">
              <div class="d-flex">
                <span jhiTranslate="srsaApp.licenseRequest.maritimeTourismAgent">Maritime Tourism Agent</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (licenseRequest of licenseRequests; track trackId) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/license-request', licenseRequest.id, 'view']">{{ licenseRequest.id }}</a>
              </td>
              <td>{{ licenseRequest.noOfYearsExperience }}</td>
              <td>{{ licenseRequest.noOfCountryCompanyOpr }}</td>
              <td>{{ licenseRequest.noOfManagedMaritimeMediums }}</td>
              <td>{{ licenseRequest.noOfOwnedMaritimeMediumsWorld }}</td>
              <td>{{ licenseRequest.noOfOwnedMaritimeMediumsKsa }}</td>
              <td>{{ licenseRequest.noOfManagedMaritimeMediumsGeneral }}</td>
              <td [jhiTranslate]="'srsaApp.LicenseDuration.' + (licenseRequest.requestedLicenseDuration ?? 'null')">
                {{
                  { null: '', ONE_YEAR: 'ONE_YEAR', TWO_YEAR: 'TWO_YEAR', THREE_YEAR: 'THREE_YEAR' }[
                    licenseRequest.requestedLicenseDuration ?? 'null'
                  ]
                }}
              </td>
              <td>{{ licenseRequest.marinaOwnerNameAr }}</td>
              <td>{{ licenseRequest.marinaCrNumber }}</td>
              <td>{{ licenseRequest.marinaCrNameAr }}</td>
              <td>{{ licenseRequest.marinaCrNameEn }}</td>
              <td>{{ licenseRequest.numberOfDocks }}</td>
              <td>{{ licenseRequest.dockLength }}</td>
              <td>{{ licenseRequest.dockDepth }}</td>
              <td>{{ licenseRequest.noOfWaterStations }}</td>
              <td>{{ licenseRequest.noOfWaterStationsPerPlatform }}</td>
              <td>{{ licenseRequest.noOfFuelStations }}</td>
              <td>{{ licenseRequest.noOfWasteStations }}</td>
              <td>{{ licenseRequest.noOfWasteStationsPerPlatform }}</td>
              <td>{{ licenseRequest.noOfElectricChargingStations }}</td>
              <td>{{ licenseRequest.noOfControlTowers }}</td>
              <td>{{ licenseRequest.noOfMarinaFacilities }}</td>
              <td>{{ licenseRequest.boatsCapacity }}</td>
              <td>{{ licenseRequest.yachtsCapacity }}</td>
              <td>{{ licenseRequest.numberOfOtherMarineVessels }}</td>
              <td>
                @if (licenseRequest.marinaLocation) {
                  <div>
                    <a [routerLink]="['/national-address', licenseRequest.marinaLocation.id, 'view']">{{
                      licenseRequest.marinaLocation.id
                    }}</a>
                  </div>
                }
              </td>
              <td>
                @if (licenseRequest.maritimeTourismAgent) {
                  <div>
                    <a [routerLink]="['/company', licenseRequest.maritimeTourismAgent.id, 'view']">{{
                      licenseRequest.maritimeTourismAgent.id
                    }}</a>
                  </div>
                }
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a
                    [routerLink]="['/license-request', licenseRequest.id, 'view']"
                    class="btn btn-info btn-sm"
                    data-cy="entityDetailsButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">عرض</span>
                  </a>

                  <a
                    [routerLink]="['/license-request', licenseRequest.id, 'edit']"
                    class="btn btn-primary btn-sm"
                    data-cy="entityEditButton"
                  >
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">تعديل</span>
                  </a>

                  <button type="submit" (click)="delete(licenseRequest)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">حذف</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (licenseRequests && licenseRequests.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
