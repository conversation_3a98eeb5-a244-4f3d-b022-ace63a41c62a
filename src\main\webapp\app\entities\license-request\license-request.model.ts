import { ICompany } from 'app/entities/company/company.model';
import { LicenseDuration } from 'app/entities/enumerations/license-duration.model';
import { INationalAddress } from '../../account/register/model/national.address.model';
import { ILicenseRequestMaritimeMedium } from '../license-request-maritime-medium/license-request-maritime-medium.model';
import { ILicenseRequestProvidedLanguages } from '../license-request-provided-languages/license-request-provided-languages.model';
import { ILicenseRequestMaritimeServices } from '../license-request-maritime-services/license-request-maritime-services.model';
import { ILicenseRequestMarineTourServices } from '../license-request-marine-tour-services/license-request-marine-tour-services.model';
import { ILicenseRequestFuelTypes } from '../license-request-fuel-types/license-request-fuel-types.model';
import { ILicenseRequestCountry } from '../license-request-country/license-request-country.model';
import { ILicenseRequestDocks } from '../permit-request/license-request-docks.model';
import { IBeachActivities } from '../permit-request/beach-activities.model';
import { IBeachEquipment } from '../permit-request/beach-equipment.model';

export interface ILicenseRequest {
  id: number;
  noOfYearsExperience?: number | null;
  noOfCountryCompanyOpr?: number | null;
  noOfManagedMaritimeMediums?: number | null;
  noOfOwnedMaritimeMediumsWorld?: number | null;
  noOfOwnedMaritimeMediumsKsa?: number | null;
  noOfManagedMaritimeMediumsGeneral?: number | null;
  requestedLicenseDuration?: keyof typeof LicenseDuration | null;
  marinaOwnerNameAr?: string | null;
  marinaOwnerNameEn?: string | null;
  beachOwnerName?: string | null;
  beachTradeName?: string | null;
  beachOwnerCommercialRegistrationNumber?: string | null;
  numberOfRestrooms?: number | null;
  numberOfLifeguards?: number | null;
  isThereCabinsForRent?: boolean | null;
  numberOfCabinsAndRooms?: number | null;
  numberOfFoodTruckParking?: number | null;
  visitorCapacity?: number | null;
  isThereMarineVessel?: boolean | null;
  numberOfOtherMarineVessel?: number | null;
  previouslyOperatedBeachInsideOrOutsideSaudiArabia?: boolean | null;
  marinaCrNumber?: string | null;
  marinaCrNameAr?: string | null;
  marinaCrNameEn?: string | null;
  numberOfDocks?: number | null;
  dockLength?: number | null;
  dockDepth?: number | null;
  dockWidth?: number | null;
  noOfWaterStations?: number | null;
  noOfWaterStationsPerPlatform?: number | null;
  noOfFuelStations?: number | null;
  noOfWasteStations?: number | null;
  noOfWasteStationsPerPlatform?: number | null;
  noOfElectricChargingStations?: number | null;
  noOfControlTowers?: number | null;
  noOfMarinaFacilities?: number | null;
  boatsCapacity?: number | null;
  yachtsCapacity?: number | null;
  numberOfOtherMarineVessels?: number | null;
  marinaLocation?: INationalAddress | null;
  maritimeTourismAgent?: ICompany | null;
  licenseRequestMaritimeMediums?: ILicenseRequestMaritimeMedium[] | null;
  licenseRequestProvidedLanguages?: ILicenseRequestProvidedLanguages[] | null;
  licenseRequestMaritimeServices?: ILicenseRequestMaritimeServices[] | null;
  licenseRequestMarineTourServices?: ILicenseRequestMarineTourServices[] | null;
  licenseRequestCountries?: ILicenseRequestCountry[] | null;
  licenseRequestFuelTypes?: ILicenseRequestFuelTypes[] | null;
  licenseRequestDocks?: ILicenseRequestDocks[] | null;
  beachActivities?: IBeachActivities[] | null;
  beachEquipment?: IBeachEquipment[] | null;
}

export type NewLicenseRequest = Omit<ILicenseRequest, 'id'> & { id: null };
