<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20250806230000-add-applicant-to-visa-attachment" author="rsobh">
        <!-- Add a visa applicant ID column with foreign key constraint -->
        <addColumn tableName="visa_tourism_request_attachment">
            <column name="visa_applicant_id" type="bigint">
                <constraints nullable="true" />
            </column>
        </addColumn>

        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="visa_tourism_request_attachment"
            baseColumnNames="visa_applicant_id"
            referencedTableName="visa_applicant"
            referencedColumnNames="id"
            constraintName="fk_attachment_visa_applicant"/>
    </changeSet>

</databaseChangeLog>
