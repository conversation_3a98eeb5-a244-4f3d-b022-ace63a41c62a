package com.elm.srsa.service;

import com.elm.srsa.config.ApplicationProperties;
import com.elm.srsa.domain.AttachmentType;
import com.elm.srsa.domain.YachtCharterPermit;
import com.elm.srsa.domain.visa.VisaRequest;
import com.elm.srsa.domain.visa.VisaTourismRequestAttachment;
import com.elm.srsa.repository.AttachmentTypeRepository;
import com.elm.srsa.repository.visa.VisaTourismRequestAttachmentRepository;
import com.elm.srsa.service.dto.VisaRequestDTO;
import com.elm.srsa.service.dto.YachtCharterPermitAttachmentDTO;
import com.elm.srsa.service.mapper.AttachmentTypeMapper;
import com.elm.srsa.util.Constants;
import com.elm.srsa.util.FileScanUtil;
import com.elm.srsa.util.Utils;
import com.elm.srsa.web.rest.errors.BadRequestAlertException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class VisaTourismRequestAttachmentService {

    private static final Logger LOG = LoggerFactory.getLogger(VisaTourismRequestAttachmentService.class);

    private final VisaTourismRequestAttachmentRepository attachmentRepository;
    private final ApplicationProperties appProperties;
    private final FileScanUtil fileScanUtil;
    private final AttachmentTypeMapper attachmentTypeMapper;
    private final AttachmentTypeRepository attachmentTypeRepository;
    private final Long maxFileSizeMB = 5L;

    public VisaTourismRequestAttachmentService(
        VisaTourismRequestAttachmentRepository attachmentRepository,
        ApplicationProperties appProperties,
        FileScanUtil fileScanUtil,
        AttachmentTypeMapper attachmen,
        AttachmentTypeRepository attachmentTypeRepository
    ) {
        this.attachmentRepository = attachmentRepository;
        this.appProperties = appProperties;
        this.fileScanUtil = fileScanUtil;
        this.attachmentTypeMapper = attachmen;
        this.attachmentTypeRepository = attachmentTypeRepository;
    }

    public VisaTourismRequestAttachment save(VisaTourismRequestAttachment attachment) {
        return attachmentRepository.save(attachment);
    }

    public void saveFileAndReturnPath(
        String base64String,
        String originalFilename,
        VisaRequest visaRequest,
        List<VisaTourismRequestAttachment> attachments
    ) {
        try {
            byte[] bytesAttachment = Base64.getDecoder().decode(base64String);

            long fileSizeInBytes = bytesAttachment.length;
            long maxFileSizeInBytes = maxFileSizeMB * 1024 * 1024; // Convert MB to Bytes
            if (fileSizeInBytes > maxFileSizeInBytes) {
                throw new BadRequestAlertException(
                    "File size exceeds the maximum limit of " + maxFileSizeMB + " MB",
                    null,
                    "fileSizeExceedsLimit"
                );
            }

            fileScanUtil.validateFile(bytesAttachment);

            String mainPath =
                appProperties.getDocumentAttachmentConfiguration().getPermitRequestAttachmentsPath() +
                File.separator +
                visaRequest.getRequestNumber() +
                File.separator +
                originalFilename +
                File.separator;
            String fileName = Utils.generateUniqueStringValue() + "." + Utils.getFileExtensionFromBase64(base64String);
            String path = mainPath + fileName;

            File attachment = new File(path);
            if (!attachment.exists()) {
                try {
                    File parentDir = attachment.getParentFile();
                    if (parentDir != null && !parentDir.exists()) {
                        boolean dirsCreated = parentDir.mkdirs();
                        if (!dirsCreated) {
                            throw new IOException("Failed to create directories for path: " + parentDir.getAbsolutePath());
                        }
                    }
                    boolean fileCreated = attachment.createNewFile();
                    if (!fileCreated) {
                        throw new IOException("Failed to create file: " + attachment.getAbsolutePath());
                    }
                } catch (IOException e) {
                    LOG.error("Error creating file or directory for path: {}", path, e);
                    throw new BadRequestAlertException(
                        "Error while creating file or directory for application number !!",
                        null,
                        "fileCreationError"
                    );
                }
            }

            try (FileOutputStream fileOutputStream = new FileOutputStream(attachment)) {
                fileOutputStream.write(bytesAttachment);
            }
            attachments.add(buildVisaTourismRequestAttachment("", fileName, path, visaRequest));
        } catch (Exception e) {
            LOG.info("Error while saving file into disk for request number : {}", visaRequest.getRequestNumber(), e);
            throw new BadRequestAlertException("Error while saving file into disk for application number !!", null, "invalidAttachments");
        }
    }

    public VisaTourismRequestAttachment buildVisaTourismRequestAttachment(
        String desc,
        String docName,
        String docUrl,
        VisaRequest visaRequest
    ) {
        VisaTourismRequestAttachment attachment = new VisaTourismRequestAttachment();
        attachment.setDesc(desc);
        attachment.setDocName(docName);
        attachment.setDocUrl(docUrl);
        attachment.setVisaRequest(visaRequest);
        return attachment;
    }

    public static void deleteFolder(Path pathToDelete) throws IOException {
        // Traverse the directory and delete all files and directories
        Files.walk(pathToDelete).sorted(Comparator.reverseOrder()).map(Path::toFile).forEach(File::delete);

        if (Files.exists(pathToDelete)) {
            throw new IOException("Failed to delete the folder " + pathToDelete);
        }
    }

    @Transactional(readOnly = true)
    public List<VisaTourismRequestAttachment> findByVisaRequestId(Long visaRequestId) {
        return attachmentRepository
            .findAll()
            .stream()
            .filter(att -> att.getVisaRequest() != null && visaRequestId.equals(att.getVisaRequest().getId()))
            .toList();
    }

    public void deleteById(Long id) {
        attachmentRepository.deleteById(id);
    }
}
