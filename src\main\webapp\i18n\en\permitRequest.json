{"srsaApp": {"permitRequest": {"home": {"title": "Permit Requests", "refreshListLabel": "Refresh list", "createLabel": "Create a new Permit Request", "createOrEditLabel": "Create Permit Request", "notFound": "No Permit Requests found", "saveAsDraft": "Save as Draft"}, "created": "A new Permit Request is created", "createdNumber": "Order number {{ param }} has been successfully sent, and you will be notified", "updated": "A Permit Request is updated with identifier {{ param }}", "deleted": "A Permit Request is deleted with identifier {{ param }}", "cancelRequest": "Cancel the request", "delete": {"question": "Are you sure you want to delete Permit Request {{ id }}?"}, "detail": {"title": "License", "startService": "Start Service", "requiredDocuments": "Required Documents", "serviceTerms": "Terms of Service", "serviceTerm1": "The commercial register must be active", "serviceTerm2": "The activity of the commercial registry must be {{ param }}", "serviceTerm3": "Agree to the terms and conditions", "obligations": "Obligations", "steps": "Steps", "subsidiaryCommercialRegistrationSelect": "Select a subsidiary commercial registration", "confirmation": "Confirmation", "googleMapSelect": "Select location on Google Maps", "otherVal": "Enter the other Value", "sendRequest": "Send", "resendRequest": "Resend Request", "myRequest": "My Request"}, "licenseNumber": "License Number", "licenseType": "License Type", "licenseStatus": "License Status", "issueDate": "Issue Date", "expiryDate": "Expiry Date", "id": "ID", "requestNumber": "Request Number", "requestStatus": "Request Status", "submitDate": "Submit Date", "misaLicenseNumber": "Misa License Number", "eczaLicenseNumber": "Ecza License Number", "saudiLaborCount": "Saudi Labor Count", "foreignersLaborCount": "Foreigners Labor Count", "saudizationLevel": "Saudization Level", "maleFemaleTotalError": "Number of male & female not match the total of saudi and foreigners workers count", "numberOfMaleEmployees": "Number of Male Employees", "numberOfFemaleEmployees": "Number of Female Employees", "contactInformation": "Contact Information", "rateRange": "Rate Range", "generalInformationOfLabor": "General Information Of Labor", "contactOfficerName": "Contact Officer Name", "contactOfficerMobileNumber": "Contact Officer Mobile Number", "contactOfficerEmailAddress": "Contact Officer <PERSON><PERSON> Address", "vatRegistrationNumber": "Vat Registration Number", "requestNote": "Request Note", "disclaimerChecked": "Disclaimer Checked", "fees": "Fees", "totalPriceWithoutVat": "Total Price Without Vat", "totalPriceWithVat": "Total Price With Vat", "assignedEmployeeName": "Assigned Employee Name", "attachments": "Attachments", "cR": "CR", "CR": "CR", "MainCr": "Main CR", "mainCR": "Main CR", "nationalAddress": "Company National Address", "CompanyNationalAddress": "Company National Address", "all": "All", "workforceMinistryHR": "Workforce data copy from the Ministry of Human Resources", "WorkforceMinistryHR": "Workforce data copy from the Ministry of Human Resources", "investmentLicense": "Investment License", "InvestmentLicense": "Investment License", "bankGuarantee": "A bank guarantee issued by a local bank in Saudi Arabia of no less than 500 thousand riyals.It is irrevocable, unrestricted, and unconditional, and expires 3 months after the license expires", "BankGuarantee": "A bank guarantee issued by a local bank in Saudi Arabia of no less than 500 thousand riyals.It is irrevocable, unrestricted, and unconditional, and expires 3 months after the license expires", "zakatLicense": "Zakat and Transport Authority license for customs clearance", "ZakatLicense": "Zakat and Transport Authority license for customs clearance", "InternationalSafetyManagement": "International Safety Management System Compliance Certificates", "identityRegistered": "Identity and registered trademark", "IdentityRegistered": "Identity and registered trademark", "ListMarineOwned": "List of marine media owned", "AdditionalAttachment": "Additional Attachment ", "contractAuthority": "Contract between the establishment and a licensed maritime tourism agent authorized by the Saudi Red Sea Authority", "ContractAuthority": "Contract between the establishment and a licensed maritime tourism agent authorized by the Saudi Red Sea Authority", "ContractAuthorityTooltip": "Note: If the entity holds a tourist maritime agency license, please attach the license.", "marinaDocument": "Marina tourism policy document", "operatingContract": "An operating contract between the marina owner and the operator", "OperatingContract": "An operating contract between the marina owner and the operator", "insuranceCoverage": "Insurance coverage (including third party liability as a minimum)", "InsuranceCoverage": "Insurance coverage (including third party liability as a minimum)", "commercialRegisterMarinaOwner": "The commercial register of the marina owner", "CommercialRegisterMarinaOwner": "The commercial register of the marina owner", "company": "Company", "workLocation": "Work Location", "assignedEmployee": "Assigned Employee", "licenseProvider": "License Provider", "serviceType": "Service Type", "licenseRequest": "License Request", "COMPANY_FILE": "Company file", "CONSTRUCTION_PERMIT": "Marine Pier Construction Permit", "ENVIRONMENTAL_LICENSE": "Environmental Operating License", "PROOF_OF_OWNERSHIP_OF_THE_MARINA": "Proof of marina ownership", "IDENTITY_OF_THE_OWNER_OF_THE_MARINA": "Marina owner's ID", "CYBER_SECURITY_CERTIFICATE": "Cyber Security certificate", "COMPANY_PROFILE": "Company profile", "cRDetail": "Commercial Registration Details", "additionalInfo": "Additional information", "permitData": "Permit Data", "extraData": "Additional Information", "preview": "Data Review", "targetAudience": "Target Audience", "establishments": "Organizations", "serviceTime": "Service Execution Time", "serviceFees": "Service Fees", "customerService": "Customer Service", "establishmentData": "Establishment Data", "establishmentName": "Establishment Name", "contactNumber": "Contact Number", "subsidiaryCR": "Subsidiary CR Number", "ownerInformation": "Owner Information", "ownerName": "Owner Name", "nationality": "Nationality", "entity": "Entity", "relationshipType": "Type of Relationship", "investmentType": "Type of Investment", "licensingAuthority": "Licensing Authority", "maritimeMediation": "Maritime Mediation", "selectAllApply": "(Please select all that apply)", "servicesList": "List of Services", "marineLocation": "Marina Location", "licensesDuration": "Duration of Required Licenses", "providedLanguages": "Provided Languages", "operationCountries": "Countries Where the Company Has Operated", "noOfOwnedMaritimeMediumsKsa": "Number of Maritime Vessels Owned by the Company in the Kingdom ", "noOfOwnedMaritimeMediumsWorld": "Number of Maritime Vessels Owned by the Company in the Worldwide", "maritimeTourAgent": "Maritime Tour Agent", "yearsOfExperience": "Years of Experience in the Activity", "noOfCountryCompanyOpr": "Number of Countries Where the Company Has Operated", "noOfManagedMaritimeMediumsGeneral": "Number of Maritime Vessels Managed by the Company in General", "workforceData": "Workforce Data", "ministryOfHumanResourcesNumber": "Ministry of Human Resources Number ", "marinaOwnerName": "Marina Owner Name (Arabic)", "marinaOwnerNameEn": "Marina Owner Name (English)", "marinaCrNumber": "Organization number of the company that owns the marina", "marinaCrNameAr": "Marina CR Name (Arabic)", "marinaCrNameEn": "Marina CR Name (English)", "numberOfDocks": "Number of Docks", "dockLength": "Dock Length", "dockDepth": "Dock Depth", "dockWidth": "Dock W<PERSON>th", "noOfWaterStations": "Number of Water Stations", "noOfFuelStations": "Number of Fuel Stations", "noOfWasteStations": "Number of Waste Stations", "noOfElectricChargingStations": "Number of Electric Charging Stations", "noOfControlTowers": "Number of Control Towers", "noOfMarinaFacilities": "Number of Marina Facilities", "boatsCapacity": "Boats Capacity", "yachtsCapacity": "Yachts Capacity", "otherMaritimeMediumCapacity": "Other Maritime Medium Capacity", "fuelTypes": "Fuel Types", "marineTourOperatorServices": "Marine operator Services", "termsAndConditions": "Please review and agree to the terms and conditions", "obligations": "Please review and agree to the Control and obligations", "rqMng": "Request Management", "rqNo": "New Requests", "rqNew": "Request Number", "esnad": "Assign", "cancelEsnad": "<PERSON><PERSON> Assign", "rqPhase": "Request Phases", "rqDetails": "Request Details", "rqDate": "Request Date", "asignedTo": "Assigned To", "crName": "Company Name", "rqType": "Request Type", "rqStatus": "Request Status", "visitDate": "Visit Date", "visitStatus": "Visit Status", "Attachments": "Attachements", "visitReschedule": "Reschedule Visit", "visitComplete": "Complete Visit", "visitEdit": "<PERSON>", "returnRq": "Return Request", "visitSchedule": "Schedule Visit", "rejectRq": "Reject Request", "confirmRq": "Confirm Request", "returnRqOfficer": "Return Request to Inspection Officer", "TameedRq": "Confirm Request", "visitScheduleTitle": "Select Visit Date", "visitScheduleTime": "Select Visit Time", "confirm": "Confirm", "cancel": "Cancel", "confirmVisit": "Confirm <PERSON>", "uploadVisitDocs": "Upload Visit Documents", "returnReqToProvider": "Return Request to Service Provider", "returnToLicensingSupervisor": "Return to Coastal Tourism Licensing Supervisor", "sendRqToMngr": "Send Request to Inspection Manager", "editAndsendRqToMngr": "Edit and resend Inspection Manager", "finalConfirmRq": "Request Final Confirmation", "previewRq": "Please make sure from all details", "exceptionApproval": "If need exceptional approval ?", "feeRequired": "If need to pay fees ?", "exceptionalDoc": "Upload exceptional approval documents ", "EXCEPTIONAL_DOC": "Upload exceptional approval documents ", "sendRqToProvider": "Send request to service provider", "visitReport": "Visit Report", "Visit_Report_DOC": "Visit Report", "edit": "Edit", "visitRequired": "Does the request require a field visit ?", "yes": "Yes", "no": "No", "rejectReason": "Reject Reason", "ifRejectRq": "Are you sure to reject the request ?", "ifReturnRq": "Are you sure to return the request ?", "ifComplatedReq": "Are you sure to the request complated ", "ifComplatedReqNote": "Once you confirm , can not go back", "back": "Back", "licenceOfficer": "License Officer", "selectOfficer": "Select Officer", "inspectionOfficer": "Inspection Officer", "managerlisence": "License Manager", "ifCancelRq": "Are you sure to cancel Assign ?", "history": "History and Notes", "notes": "Notes", "requestPreview": "Request Preview", "requestConfirmDate": "Request Confirm Date", "download": "Download", "editVisit": "Edit Visit Report", "totalFundingOfProject": "Total Funding For The Project", "beachOwnerName": "Beach owner name", "beachTradeName": "Beach trade name", "beachOwnerCommercialRegistrationNumber": "Beach owner's commercial registration number", "numberOfRestrooms": "Number of available restrooms", "numberOfLifeguards": "Number of available lifeguards", "isThereCabinsForRent": "Are there rooms or cabins for rent?", "numberOfCabinsAndRooms": "Number of cabins and rooms", "numberOfFoodTruckParking": "Number of food truck parking", "visitorCapacity": "Visitor capacity", "isThereMarineVessel": "Is there marine vessel?", "numberOfOtherMarineVessel": "Number of other marine vessels", "previouslyOperatedBeachInsideOrOutsideSaudiArabia": "Have you previously operated a beach inside or outside Saudi Arabia?", "beachLocation": "Map Activity Location", "beachEquipment": "Equipment to be used", "beachActivities": "Types of activities", "statusVisit": {"SCHEDULED": "SCHEDULED", "COMPLETED": "COMPLETED", "NOT_VISITED": "NOT VISITED"}, "licenseDuration": {"ONE_YEAR": "One Year", "TWO_YEAR": "Two Year", "THREE_YEAR": "Three Year"}, "saudiLevel": {"PLATINUM": "PLATINUM", "HIGH_GREEN": "HIGH GREEN", "MEDIUM_GREEN": "MEDIUM GREEN", "LOW_GREEN": "LOW GREEN", "RED": "RED"}, "assignedTo": " Assigned To", "attributionStatus": " Attribution Status", "allRequests": " All Requests", "newRequests": " New Requests", "notAssigned": "Not Assigned", "assigned": "Assigned", "inProgressRequests": "In Progress Requests", "issuedRequests": "Issued Requests", "assignedRequests": "Assigned Requests", "noValidPermit": "Sorry, there is no valid navigational permit for this marine, or the permit is expiring within 15 days.", "noValidTourismPermit": "Sorry , there is no valid tourism permit for this marine", "noValidTechnicalPermit": "Sorry, the tourism permit cannot be issued because the technical license has expired or is about to expire.", "serviceChannels": "Service Channels", "webMobileApp": "Web and mobile Applications", "paymentMethods": "Payment Methods", "beachOperator": {"MainCr": "Main CR", "mainCR": "Main CR", "investmentLicense": "Investment License", "InvestmentLicense": "Investment License", "MARINE_VEHICLES_AND_ACTIVITIES": "A statement of the number and licenses of marine vehicles and licensed marine activities carried out in coastal waters by the competent authorities", "WATER_SAFETY_EQUIPMENT": "A statement of water safety tools and equipment for beaches", "SAFETY_MANAGEMENT_PLAN": "Safety and health management plans", "ENVIRONMENTAL_PERMIT": "An environmental operating permit issued by the NCEC", "LIFEGUARDS_LIST": "A statement of the number and names of lifeguards", "MARITIME_SPACE_PLANNING": "Maritime space planning for areas where maritime activities are carried out in coastal waters", "OCCUPANCY_CERTIFICATE": "An occupancy certificate issued by the competent local authority", "SITE_VERIFICATION": "Site verification", "COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH": "Commercial registration or identity of the owner of the site (beach)", "operatingContract": "Copy of operating contract ", "OperatingContract": "Copy of operating contract", "COMPANY_PROFILE": "Company profile", "insuranceCoverage": "Insurance document", "InsuranceCoverage": "Insurance document"}}}}