package com.elm.srsa.repository;

import com.elm.srsa.domain.BeachActivities;
import com.elm.srsa.domain.MarineTourOperatorServices;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the MarineTourOperatorServices entity.
 */
@SuppressWarnings("unused")
@Repository
public interface BeachActivitiesRepository extends JpaRepository<BeachActivities, Long>, JpaSpecificationExecutor<BeachActivities> {}
