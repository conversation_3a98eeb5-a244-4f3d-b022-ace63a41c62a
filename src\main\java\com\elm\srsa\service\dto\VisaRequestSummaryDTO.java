package com.elm.srsa.service.dto;

import com.elm.srsa.domain.visa.enumerations.RequestStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VisaRequestSummaryDTO {

    private Long id;
    private String requestNumber;
    private RequestStatus requestStatus;
    private String visaType;
    private BigDecimal totalFee;
    private LocalDateTime createdAt;
}
