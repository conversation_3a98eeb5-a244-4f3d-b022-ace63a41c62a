{"global": {"lang-ar-en": "ar-ly", "lang-Label": "English", "selectregis": "تحديد نوع التسجيل", "compInfo": "معلومات المنشأة", "benfInfo": "معلومات المستفيد", "empInfo": "معلومات الموظف", "userDate": "بيانات المستخدم", "rEmpInfo": "تسجيل معلومات الموظف", "userDate&com": "بيانات المستخدم و المنشأة", "rUserInfo": "تسجيل معلومات المستخدم", "T&C": "الشروط والأحكام", "privacy-policy": "سياسة الخصوصية", "title": "الهيئة السعودية للبحر الأحمر", "search": "البحث", "table": {"noData": "لا توجد بيانات متاحة", "loading": "جاري التحميل...", "actions": "الإجراءات", "sort": "ترتيب", "page": "صفحة", "of": "من", "items": "عنصر", "itemsPerPage": "عد<PERSON> العناصر في الصفحة"}, "boolean": {"yes": "نعم", "no": "لا"}, "browsehappy": "أنت تستخدم متصفح <strong>قديم</strong> . من فضلك <a href=\"http://browsehappy.com/?locale=en\">قم بتحديث متصفحك</a> لتحسين تجربتك.", "termsAndConditions": {"title": "تطبق الشروط والأحكام الأساسية على استخدامك لموقعنا الإلكتروني والذي يمكن الوصول إليه من خلال الرابط  <a href=\"https://redsea.gov.sa\"></a> تطبق الشروط والأحكام بشكل كامل على استخدامك لهذا الموقع الإلكتروني. باستخدامك لهذا الموقع الإلكتروني فإنك تقبل جميع الشروط والأحكام الواردة به، يجب عدم استخدام هذا الموقع الإلكتروني في حالة عدم قبول أي بند من الشروط والأحكام. تم إنشاء الشروط والأحكام الخاصة بهذا الموقع الإلكتروني.", "text1": "يجب أن تكون قد بلغت 18 عامًا على الأقل لتسجيل حساب.", "text2": "يُطلب منك تقديم معلومات دقيقة وحديثة وكاملة أثناء عملية التسجيل.", "text3": "يجب عليك تحديث معلوماتك على الفور إذا طرأ عليها أي تغيير.", "text4": "قد يؤدي تقديم معلومات خاطئة أو مضللة إلى تعليق أو إنهاء حسابك.", "text5": "أنت وحدك المسؤول عن الحفاظ على سرية بيانات الدخول الخاصة بحسابك، بما في ذلك اسم المستخدم وكلمة المرور.", "text6": "توافق على عدم مشاركة بيانات حسابك مع الآخرين.", "text7": "تتحمل المسؤولية الكاملة عن جميع الأنشطة التي تتم تحت حسابك.", "text8": "إذا كنت تشك في أي استخدام غير مصرح به لحسابك، يجب عليك إخطارنا فورًا.", "text9": "نحن غير مسؤولين عن أي خسارة أو ضرر ينشأ من الوصول غير المصرح به إلى حسابك نتيجة عدم تأمينك لبيانات الدخول الخاصة بك.", "text10": "توافق على استخدام المنصة بطريقة تتوافق مع جميع القوانين واللوائح والتوجيهات السارية.", "text11": "يُحظر الاستخدام غير المصرح به للمنصة، بما في ذلك على سبيل المثال لا الحصر القرصنة، أو التنقيب عن البيانات، أو أي نشاط قد يهدد أمان أو نزاهة المنصة، وقد يؤدي إلى اتخاذ إجراءات قانونية.", "text12": "نحن نأخذ حماية بياناتك الشخصية على محمل الجد ونتعامل معها وفقًا لسياسة الخصوصية الخاصة بنا.", "text13": "بتسجيلك حسابًا، فإنك توافق على جمع وتخزين واستخدام معلوماتك الشخصية كما هو موضح في سياسة الخصوصية.", "text14": "توافق على مشاركة بياناتك مع مزودي خدمات تابعين لجهات خارجية لغرض صيانة المنصة، أو دعم العملاء، أو احتياجات تشغيلية أخرى، وذلك بما يتوافق مع قوانين حماية البيانات السارية.", "text15": "نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت.", "text16": "ستكون أي تغييرات سارية فور نشرها على المنصة.", "text17": "تقع على عاتقك مسؤولية مراجعة الشروط دوريًا للاطلاع على أي تحديثات.", "text18": "إن استمرارك في استخدام حسابك بعد أي تعديل يشكل موافقتك على الشروط المعدلة.", "text19": "إذا لم توافق على التغييرات، يجب عليك التوقف عن استخدام المنصة ويمكنك طلب إغلاق حسابك.", "text20": "قد نقوم بإنهاء أو تعليق حسابك وفقًا لتقديرنا الخاص، دون إشعار مسبق، إذا رأينا أنك قد انتهكت هذه الشروط، أو انخرطت في أنشطة غير قانونية، أو لأي سبب آخر.", "text21": "عند الإنهاء، سيتوقف حقك في استخدام المنصة فورًا، وقد يتم حذف أي محتوى أو بيانات مرتبطة بحسابك.", "text22": "تخضع هذه الشروط والأحكام لقوانين المملكة العربية السعودية، وأي نزاعات تنشأ عن أو تتعلق بهذه الشروط ستكون خاضعة للاختصاص القضائي الحصري لمحاكم المملكة العربية السعودية.", "text23": "بتسجيلك حسابًا، فإنك توافق على الخضوع للاختصاص القضائي لهذه المحاكم في أي مسائل قانونية تتعلق بحسابك أو استخدامك للمنصة.", "text24": "إذا تم اعتبار أي بند من هذه الشروط باطلًا أو غير قابل للتنفيذ، فإن البنود المتبقية ستظل سارية وفعالة بالكامل.", "text25": "عدم تنفيذنا لأي حق أو بند في هذه الشروط لا يشكل تنازلًا عن هذا الحق أو البند.", "accept": "قبول", "reviewTermsAndConditions": "الرجاء الاطلاع والموافقة على الشروط والأحكام", "reviewPrivacyPolicy": "الأطلاع علي سياسة الخصوصية"}, "mobileOtpModal": {"securityCheck": "تحقق أمني", "4digit": "ير<PERSON>ى إدخال الرمز المرسل إلى جوالك و المكون من أربعة أرقام :", "sendAgain": "إعادة الإرسال بعد", "notReceived": "لم يصلك الرمز؟", "securityCheckEmail": "ير<PERSON>ى إدخال الرمز المرسل إلى بريدك الالكتروني :"}, "menu": {"home": "الرئيسية", "ourVisionAndGoals": "رؤيتنا وأهدافنا", "electronicServices": "الخدمات الإلكترونية", "Regulations": "اللوائح التنظيمية", "callUs": "اتصل بنا", "jhipster-needle-menu-add-element": "JHipster will add additional menu entries here (do not translate!)", "sideBar": {"welcome1": "مرحبا بك في", "welcome2": "منصة الهيئة", "welcome3": "السعودية للبحر الأحمر", "welcome4": "لتمكين اقتصاد سياحي مثمر من خلال ساحل البحر الأحمر للمملكة"}, "entities": {"main": "الكيانات", "jhipster-needle-menu-add-entry": "JHipster will add additional entities here (do not translate!)", "attachment": "Attachment", "attachmentType": "Attachment Type", "company": "Company", "country": "Country", "fuelTypes": "Fuel Types", "investmentType": "Investment Type", "licenseProvider": "License Provider", "licenseRequest": "License Request", "licenseRequestCountry": "License Request Country", "licenseRequestCountryHistory": "License Request Country History", "licenseRequestFuelTypes": "License Request Fuel Types", "licenseRequestFuelTypesHistory": "License Request Fuel Types History", "licenseRequestHistory": "License Request History", "licenseRequestMarineTourServices": "License Request Marine Tour Services", "licenseRequestMarineTourServicesHistory": "License Request Marine Tour Services History", "licenseRequestMaritimeMedium": "License Request Maritime Medium", "licenseRequestMaritimeMediumHistory": "License Request Maritime Medium History", "licenseRequestMaritimeServices": "License Request Maritime Services", "licenseRequestMaritimeServicesHistory": "License Request Maritime Services History", "licenseRequestProvidedLanguages": "License Request Provided Languages", "licenseRequestProvidedLanguagesHistory": "License Request Provided Languages History", "marineTourOperatorServices": "Marine Tour Operator Services", "maritimeMedium": "Maritime Medium", "maritimeTourismServices": "Maritime Tourism Services", "nationalAddress": "National Address", "permitRequest": "Permit Request", "permitRequestHistory": "Permit Request History", "permitRequestStatus": "Permit Request Status", "providedLanguages": "Provided Languages", "serviceType": "Service Type", "arrivalRequestNationality": "Arrival Request Nationality", "portCode": "Port Code", "portCountry": "Port Country", "routes": "Routes", "cargoType": "Cargo Type", "container": "Container", "passenger": "Passenger", "arrivalDepNotification": "Arrival Dep Notification", "arrivalRequest": "Arrival Request", "departureRequest": "Departure Request", "containerType": "Container Type", "arrivalDepAttachment": "Arrival Dep Attachment", "marineMedium": "Marine Medium", "techPermitAttachment": "Tech Permit Attachment", "marineAccessoriesItem": "Marine Accessories Item", "technicalPermitStatus": "Technical License (Leisure Tourism) Status", "techPermitPort": "Tech Permit Port", "techPermitNationality": "Tech Permit Nationality", "techPermitPassengers": "Tech Permit Passengers", "technicalPermit": "Technical License (Leisure Tourism)", "technicalPermitActivity": "Technical License (Leisure Tourism) Activity", "technicalPermitHistory": "Technical License (Leisure Tourism) History", "technicalPermitActivityHistory": "Technical License (Leisure Tourism) Activity History", "yachtCharterPermitAttachment": "Yacht Charter Permit Attachment", "yachtCharterPermitStatus": "Yacht Charter Permit Status", "yachtCharterPermitDestination": "Yacht Charter Permit Destination", "yachtCharterPermitNationality": "Yacht Charter Permit Nationality", "yachtCharterPermit": "Yacht Charter Permit", "visaTourism": "التأشيرات السياحية", "yachtCharterPermitHistory": "Yacht Charter Permit History", "transitPermitAttachment": "Transit Permit Attachment", "transitPermitStatus": "Transit Permit Status", "transitPermitDestination": "Transit Permit Destination", "transitPermitNationality": "Transit Permit Nationality", "transitPermit": "Transit Permit", "transitPermitHistory": "Transit Permit History"}, "account": {"main": "الحساب", "settings": "الإعدادات", "password": "كلمه السر", "sessions": "جلسات", "login": "تسجيل الدخول", "logout": "تسجيل الخروج", "register": "التسجيل", "profile": "الملف الشخصي"}, "admin": {"main": "الادارة", "userManagement": "إدارة المستخدم", "permitManagement": "إدارة الطلبات", "techPermit": "إدارة الترخيص الفني (السياحي الترفيهي) ", "transitPermit": "إدارة تصاريح المرور", "tourismPermit": "إدارة تصريح الرحلة السياحية", "yachtCharterPermit": "إدارة تصاريح تأجير اليخوت", "visaTourism": "إدارةالتأشيرات السياحية", "tracker": "تعقب المستخدمين", "metrics": "مقاييس", "health": "الصحة", "configuration": "الإعدادات", "logs": "السجلات", "apidocs": "API", "database": "قاعدة البيانات", "jhipster-needle-menu-add-admin-element": "JHipster will add additional menu entries here (do not translate!)", "paymentRequests": "طلبات الدفع", "invoices": "الفواتير"}, "language": "اللغة", "next": "التالي", "previous": "السابق"}, "form": {"changePhoneNo": "تغيير رقم الجوال؟", "change": "تغيير", "changeEmail": "تغيير البريد الالكتروني؟", "companyName": "معلومات المنشأة", "userInfo": "معلومات الموظف", "individualUserInfo": "معلومات المستفيد", "placeholderMandatory": "إجباري", "username.label": "اسم المستخدم", "username.placeholder": "إسم المستخدم الخاص بك", "currentpassword.label": "كلمة المرور الحالية", "currentpassword.placeholder": "كلمة المرور الحالية", "newpassword.label": "كلمة المرور", "newpassword.placeholder": "كلمة السر الجديدة", "passwordLengthMinCharacters": "الحد الأدنى لعدد الحروف هو 8 حروف", "passwordLengthMaxCharacters": "الحد الأق<PERSON>ى لعدد الحروف هو 50 حرف", "passwordLengthUppercaseLetters": "يجب أن تحتوي على حروف كب<PERSON>ة [A-Z]", "passwordLengthLowercaseLetters": "يجب أن تحتوي على حروف صغيرة [a-z]", "passwordNumbers": "يجب أن تحتوي على أرقام [0-9]", "passwordSpecialCharacters": "يجب أن تحتوي على رموز مميزة [ ! , @ , $ , % , ? , & , * ]", "confirmpassword.label": "إعادة كلمة المرور", "confirmpassword.placeholder": "تأكيد كلمة السر الجديدة", "emailConfirm.label": "تأكيد البريد الإلكتروني", "email.label": "الب<PERSON>يد الإلكتروني", "email.placeholder": "بريدك الالكتروني", "idNumber.label": "رقم هوية المالك/المفوض", "idNumber": "رقم الهوية/الإقامة ", "idNumber.placeholder": "إجباري", "commercialRegistrationNo.label": "رقم السجل التجاري", "mobileNumber.label": "رقم الجوال", "mobileNumber.placeholder": "رقم الجوال", "employeeType.label": "الجهة الوظيفية", "employeeId.label": "الرقم الوظيفي", "employeeId.placeholder": "الرقم الوظيفي", "birthDate.label": "تاريخ الميلاد", "gregorian": "ميل<PERSON>ي", "hijri": "هجري", "verify": "تحقق", "Verifying": "جاري التحقق", "Verified": "تم التحقق", "operationSuccessfully": "تمت العملية بنجاح", "passwordPolicy": "سياسة كلمة المرور :", "nationalNumber": "رقم الهوية الوطنية (في بلدك)", "passportName": "الاسم الرباعي كما هو في الجواز", "nationality": "الجنسية", "passportNumber": "رقم جواز السفر", "passportCountry": "الدولة المصدرة للجواز", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى", "passportExpiryDate": "تاريخ إنتهاء جواز السفر", "nationalIdImage": "صورة الهوية الوطنية", "passportImage": "صورة جواز السفر", "organizationName": "اسم المنشأة", "organizationReferenceNumber": "الرقم المرجعي للمنشأة", "entityIssuingCommercialLicense": "الجهة المصدرة للرخصة التجارية", "businessDescription": "وصف النشاط التجاري", "companyNationality": "جنسية الشركة", "commercialLicenseExpiryDate": "تاريخ انتهاء الرخصة التجارية", "commercialLicenseCertificate": "شهادة الرخصة التجارية", "relationshipWithOrganization": "علاقة المستخدم بالمنشأة", "frequentlyAskedQuestions": "الاسئلة الشائعة", "downloadUserGuide": "تحميل دليل المستخدم", "yes": "نعم", "no": "لا"}, "messages": {"info": {"authenticated": {"prefix": "إذا اردت أن ", "link": "تسجل دخولك", "suffix": ", يمكنك تجربة الحسابات التجريبية:<br/>- المدير (login=\"admin\" و كلمة المرور=\"admin\") <br/>- المستخدم (login=\"user\" و كلمة المرور=\"user\")."}, "register": {"noaccount": "ليس لديك حساب ؟", "link": "تسجيل حساب جديد", "selectRegistration": "الرجاء تحديد نوع التسجيل الخاص بك", "serviceProvider": "م<PERSON>و<PERSON> خدمة", "beneficiary": "مستفيد", "doHaveAccount": "هل لديك حساب ؟", "loginNow": "قم بتسجيل الدخول"}}, "error": {"dontmatch": "كلمة السر وتأكيدها لا تتطابق!", "phoneNoNeeds": "رقم الجوال الخاصة بك مطلوب.", "phoneNoNotValid": "رقم الجوال غير صحيح يجب أن يبدأ ب 9665.", "mobileNotValid": "رقم الجوال غير صحيح يجب أن يكون أرقام فقط.", "userAtleast1Char": "اسم المستخدم الخاصة بك مطلوب ليكون على الأقل 1 حرف.", "userCannotLong50Char": "اسم المستخدم الخاصة بك لا يمكن أن يكون أطول من 50 حرفا.", "emailNeeds": "البريد الإلكتروني الخاصة بك مطلوب.", "emailNotValid": "بريدك الالكتروني خاطئ.", "emailAtleast4Char": "البريد اﻹلكتروني لا يمكن أن يكون اقل من 4 احرف.", "emailCannotLong50Char": "البريد اﻹلكتروني لا يمكن أن يكون اكثر من 50 حرفا."}, "validate": {"newpassword": {"required": "كلمة المرور الخاصة بك مطلوبة.", "minlength": "كلمة السر لا يمكن أن تكون اقل من 8 احرف.", "maxlength": "كلمة السر لا يمكن أن تكون اكثر من 50 حرفا.", "strength": "قوة كلمة المرور:"}, "confirmpassword": {"required": "تأكيد كلمة المرور الخاصة بك مطلوب.", "minlength": "تأكيد كلمة السر لا يمكن أن يكون اقل من 8 احرف.", "maxlength": "تأكيد كلمة السر لا يمكن أن يكون اكثر من 50 حرفا."}, "email": {"required": "البريد الإلكتروني الخاصة بك مطلوب.", "invalid": "بريدك الالكتروني خاطئ.", "minlength": "البريد اﻹلكتروني لا يمكن أن يكون اقل من 4 احرف.", "maxlength": "البريد اﻹلكتروني لا يمكن أن يكون اكثر من 50 حرفا.", "emailPattern": "الرجاء إستخدام بريد إلكتروني صحيح ."}}}, "field": {"id": "رقم"}, "ribbon": {"dev": "تطوير"}, "item-count": "يعرض {{first}} - {{second}} من {{total}} البيانات.", "footer": {"message1": "تواجه أي مشكلة؟", "message2": "اتصل بنا"}}, "entity": {"action": {"addblob": "ا<PERSON><PERSON>", "addimage": "اض<PERSON> صورة", "back": "رجوع", "cancel": "الغاء", "delete": "<PERSON><PERSON><PERSON>", "edit": "تعديل", "suspend": "تعليق", "activate": "تنشيط", "assignToLicensingOfficer": "إسناد المهمة لأحد الموظفين", "returnRequestForData": "إعادة الطلب للإستكمال", "open": "فتح", "save": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "close": "إغلاق", "confirm": "تأكيد", "show": "Show {{otherEntity}}", "print": "طباعة", "writeNotes": "كتابة المرئيات", "inspectionReview": "مراجعة الطلب"}, "detail": {"field": "<PERSON>قل", "value": "قيمة"}, "delete": {"title": "تأكيد عملية الحذف"}, "validation": {"required": "هذا الحقل مطلوب.", "minlength": "هذا الحقل مطلوب أن يكون على الأقل {{ min }} احرف.", "maxlength": "هذا الحقل لا يمكن أن يكون أطول من {{ max }} احرف.", "min": "هذا الحقل ينبغي أن يكون اقل من {{ min }}.", "daysOffLimit": "فرق الايام لا يمكن ان يكون اكثر من {{ days }}.", "minNumber": "هذا الحقل ينبغي أن يكون على الأفل {{ min }}.", "max": "هذا الحقل لا يمكن أن يكون أكثر من {{ max }}.", "minbytes": "هذا الحقل ينبغي أن يكون اقل من {{ min }} بايت.", "maxbytes": "هذا الحقل لا يمكن أن يكون أكثر من {{ max }} بايت.", "pattern": "هذا الحقل يجب أن يتبع نمط {{ pattern }}.", "number": "هذا الحقل يجب أن يكون رقم.", "datetimelocal": "هذا الحقل يجب أن يكون تاريخ ووقت.", "patternAR": "هذا الحقل يجب أن يكون بالحروف العربية.", "patternEN": "هذا الحقل يجب أن يكون بالحروف الانجليزية.", "workersError": "يجب أن يكون مجموع عدد العمال السعوديين والأجانب لا يقل عن 1.", "noNumbers": "الأرقام غير مسموحة في هذا الحقل."}, "filters": {"set": "Following filters are set", "clear": "Clear filter", "clearAll": "Clear all filters"}}, "error": {"internalServerError": "حدث خطأ في الخادم", "server.not.reachable": "الخادم لا يمكن الوصول إليه", "url.not.found": "<PERSON>ير موجود", "NotNull": "الحقل {{ fieldName }} لا يمكن ان يكون فارغ", "Size": "الحقل {{ fieldName }} لا يلبي  متطلبات الحد الأدنى / الحد الأقصى!", "userexists": "اسم الدخول سبق استخدامه!", "emailexists": "البريد الالكتروني قيد الاستخدام بالفعل!", "idexists": "{{ entityName }} جديد لا يمكن أن يكون له معرف بالفعل", "idnull": "Invalid ID", "idinvalid": "Invalid Id", "idnotfound": "ID cannot be found", "passengersIdNodValid": "رقم ID الخاص بالمسافرين خطأ", "saveerror": "Error saving tourism permit", "file": {"could.not.extract": "Could not extract file", "not.image": "File was expected to be an image but was found to be \"{{ fileType }}\""}, "paymentError": "حدث خطأ في خدمة الدفع", "pageNoteFound": "صفحة غير موجودة", "pageNoteFoundMessage": "عذراً. الصفحة المطلوبة غير موجودة في النظام.", "generalError": "خطأ في النظام.", "generalErrorMessage": "عذراً. خطأ في النظام. رجاء تواصل مع الدعم الفني."}, "token": {"remainingTime": "الوقت المتبقي", "body": "الجلسة شارفت على الإنتهاء, الرجاء التمديد او تسجيل الخروج", "extend": "تمديد"}, "payment": {"chooseMethod": "اختر نوع المحرك"}, "footer": {"faq": "الاسئلة الشائعة", "about": "عن رسا", "contactUs": "تواصل معنا", "siteMap": "خريطة الموقع", "copyright": "جميع الحقوق محفوظة للهيئة السعودية للبحر الأحمر © 2025"}}