import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { VisaRequestSearchCriteria } from './visa-request-search-criteria.model';
import { PagedResponse } from './PagedResponse';
import { VisaRequestSummary } from './visa-request-summary.model';
import { ApplicationConfigService } from '../../core/config/application-config.service';

@Injectable({ providedIn: 'root' })
export class VisaRequestService {
  protected http = inject(HttpClient);
  protected applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('/api/visa-requests');

  searchRequests(criteria: VisaRequestSearchCriteria, page: number = 0, size: number = 10): Observable<PagedResponse<VisaRequestSummary>> {
    const params = new HttpParams().set('page', page).set('size', size);

    return this.http.post<PagedResponse<VisaRequestSummary>>(`${this.resourceUrl}/search`, criteria, { params });
  }
}
