package com.elm.srsa.web.rest;

import com.elm.srsa.service.VisaTourismRequestService;
import com.elm.srsa.service.dto.VisaRequestDTO;
import com.elm.srsa.service.dto.VisaRequestSummaryDTO;
import com.elm.srsa.service.dto.VisaTourismRequestDto;
import com.elm.srsa.web.rest.request.VisaRequestSearchCriteria;
import java.util.Collections;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController("api/visaRequest")
public class VisaTourismRequestResource {

    private final VisaTourismRequestService visaRequestService;

    public VisaTourismRequestResource(VisaTourismRequestService visaRequestService) {
        this.visaRequestService = visaRequestService;
    }

    @PostMapping("/search")
    public ResponseEntity<Page<VisaRequestSummaryDTO>> search(
        @RequestBody VisaRequestSearchCriteria criteria,
        @PageableDefault(size = 20) Pageable pageable
    ) {
        Page<VisaRequestSummaryDTO> page = visaRequestService.searchVisaRequests(criteria, pageable);
        return ResponseEntity.ok(page);
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<VisaRequestDTO> create(
        @RequestPart("visaRequest") VisaRequestDTO visaRequest,
        @RequestPart("passportCopy") MultipartFile passportCopy,
        @RequestPart("personalImage") MultipartFile personalImage
    ) {
        VisaRequestDTO saved = visaRequestService.createRequest(visaRequest, passportCopy, personalImage);
        return new ResponseEntity<>(saved, HttpStatus.CREATED);
    }

    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<VisaRequestDTO> update(
        @PathVariable Long id,
        @RequestPart("visaRequest") VisaRequestDTO visaRequest,
        @RequestPart("passportCopy") MultipartFile passportCopy,
        @RequestPart("personalImage") MultipartFile personalImage
    ) {
        VisaRequestDTO updated = visaRequestService.updateRequest(id, visaRequest, passportCopy, personalImage);
        return ResponseEntity.ok(updated);
    }

    @GetMapping("/{id}")
    public ResponseEntity<VisaRequestDTO> getOne(@PathVariable Long id) {
        return visaRequestService.getRequestWithApplicants(id).map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping
    public ResponseEntity<List<VisaRequestSummaryDTO>> getAll() {
        List<VisaRequestSummaryDTO> list = visaRequestService.getAllRequestSummaries();
        return ResponseEntity.ok(list);
    }
}
