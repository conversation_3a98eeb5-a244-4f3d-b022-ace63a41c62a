<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity MaritimeTourismServices.
    -->
    <changeSet id="20250720040811-1" author="faalkanhal">
        <createTable tableName="beach_equipment">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="name_ar" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="name_en" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="record_activity" type="boolean" defaultValue="true">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="varchar(50)" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp" defaultValueDate="${now}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->

    <changeSet id="20250720040211-2-data-v1" author="faalkanhal" >
        <loadData
            file="config/liquibase/data/beach_equipment.csv"
            separator=","
            tableName="beach_equipment"
            usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="code" type="string"/>
            <column name="name_ar" type="string"/>
            <column name="name_en" type="string"/>
            <column name="maritime_medium_id" type="numeric"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
