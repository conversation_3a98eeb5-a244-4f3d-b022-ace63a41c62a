package com.elm.srsa.repository;

import com.elm.srsa.domain.BeachActivities;
import com.elm.srsa.domain.LicenseRequestBeachActivities;
import com.elm.srsa.domain.LicenseRequestMaritimeMedium;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the LicenseRequestMaritimeMedium entity.
 */
@SuppressWarnings("unused")
@Repository
public interface LicenseRequestBeachActivitiesRepository extends JpaRepository<LicenseRequestBeachActivities, Long> {
    void deleteByLicenseRequestIdAndBeachActivitiesId(Long licenseRequestId, Long beachActivitiesId);

    List<LicenseRequestBeachActivities> findByLicenseRequestId(Long licenseRequestId);

    @Query("SELECT lrba.beachActivities FROM LicenseRequestBeachActivities lrba WHERE lrba.licenseRequest.id = :licenseRequestId")
    Set<BeachActivities> findBeachActivitiesByLicenseRequestId(@Param("licenseRequestId") Long licenseRequestId);
}
