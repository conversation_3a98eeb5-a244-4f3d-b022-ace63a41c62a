<div class="logo-Ebhar-sub" *jhiHasAnyAuthority="['ROLE_SERVICE_PROVIDER']">
  <!-- <app-header /> -->
  <div class="mk-permit-container mk-inner-layout-container mk-create-request">
    <div class="p-3">
      <div class="mk-inner-layout-content">
        <form name="editForm" role="form" novalidate (ngSubmit)="save()" [formGroup]="editForm">
          <h2 id="jhi-permit2" data-cy="PermitRequestCreateUpdateHeading" class="d-flex gap-2">
            <!-- <img class="ps-2" src="../../../../content/images/Vector-1.svg" alt="Vector-1" /> -->
            <!-- <span class="fs-4 fw-bold" jhiTranslate="srsaApp.yachtCharterPermit.home.title">تصاريخ تأجير اليوخوت</span> -->
          </h2>

          <div>
            <jhi-alert-error></jhi-alert-error>
            <jhi-srsa-alert source="main"></jhi-srsa-alert>
            <small *ngIf="CrExpired" class="form-text text-danger" jhiTranslate="error.CrExpired"> Cr expired</small>
            <small *ngIf="CrNeedRenewal" class="form-text text-danger" jhiTranslate="error.CrNeedRenewal"> Cr need to be renewed </small>
            <small *ngIf="fileSizeError" class="form-text text-danger" jhiTranslate="error.fileSizeError"> Cr need to be renewed </small>
            <div *ngIf="step == -1">
              <h4 id="jhi-permit" data-cy="PermitRequestCreateUpdateHeading" class="d-flex gap-2">
                <span class="fs-4 fw-bold" jhiTranslate="srsaApp.mta.home.title1">Agents</span>
              </h4>
              @if (maritimeTourismAgentCompanies.length > 0) {
                <div class="row g-4 g-xl-5">
                  <div class="col-lg-4" *ngFor="let m of maritimeTourismAgentCompanies">
                    <div
                      class="bg-white d-flex flex-column justify-content-between mk-permit-card p-4 rounded-4 position-relative hover-y-10 hover-shadow"
                    >
                      <div class="marine-content">
                        <img class="icon" src="../../../../content/images/marine-medium.svg" alt="" />
                        <h3 class="header">{{ m.crName }}</h3>
                        <h5 class="sup-header">{{ m.nationalAddress!.city }}</h5>
                      </div>
                      <div class="d-flex justify-content-center">
                        <a
                          class="w-100 btn btn-outline-primary px-4 stretched-link"
                          (click)="selectAgent(m)"
                          jhiTranslate="srsaApp.technicalPermit.select"
                          >عرض</a
                        >
                      </div>
                    </div>
                  </div>
                </div>
              } @else {
                <div class="alert alert-warning" id="no-result">
                  <span jhiTranslate="srsaApp.marineMedium.error.noRecords">No Records found</span>
                </div>
              }
            </div>
            <div *ngIf="step == 0 && !wizardDisplay">
              <div class="row">
                <div class="d-flex justify-content-end">
                  <div class="card custom">
                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/user.svg" alt="flowbite_user" />
                      <div class="d-grid ms-3">
                        <span class="mt-4" jhiTranslate="srsaApp.permitRequest.targetAudience">الفئة المستهدفة</span>
                        <span class="fw-bold" jhiTranslate="srsaApp.yachtCharterPermit.yachtRentalFacility">yacht Rental Facility</span>
                      </div>
                    </div>
                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/money-04.svg" alt="solar_wallet-money-" />
                      <div class="d-grid ms-3">
                        <span class="mt-4" jhiTranslate="srsaApp.permitRequest.serviceFees">رسوم الخدمة</span>
                        <span class="fw-bold">{{ selectedServiceType?.fees }} {{ isArabic() ? 'ريال' : 'Riyal' }}</span>
                      </div>
                    </div>
                    <hr class="mx-4" />
                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/call.svg" alt="call" />
                      <div class="d-grid ms-3">
                        <span class="item-label" jhiTranslate="global.form.mobileNumber.label">رقم الهاتف</span>
                        <span class="item-value">
                          930 <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" />
                        </span>
                      </div>
                    </div>

                    <div class="d-flex align-items-start">
                      <img src="../../../../content/images/svg-icons/dga/side-card/mail.svg" alt="mail" />
                      <div class="d-grid ms-3">
                        <span class="item-label" jhiTranslate="global.form.email.label">البريد الإلكتروني</span>
                        <a href="mailto:<EMAIL>"
                          ><span class="item-value"
                            >{{ '<EMAIL>' }}
                            <img src="../../../../content/images/svg-icons/dga/side-card/link-square.svg" alt="link" /> </span
                        ></a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="custom-card">
                  <div class="col-lg-8 d-flex-between gap-3 mb-4">
                    <div class="d-flex flex-column justify-content-start my-3 gap-3">
                      <app-page-title [title]="'srsaApp.yachtCharterPermit.home.title' | translate" />

                      <div class="d-grid">
                        <span class="linces-header-2">{{ isArabic() ? selectedServiceType?.nameAr : selectedServiceType?.nameEn }}</span>
                      </div>
                    </div>
                    <div class="w-sm-100">
                      <!-- Button trigger modal -->
                      <button
                        type="button"
                        class="btn btn-primary px-4 px-xl-5 w-sm-100"
                        (click)="checkYachtRentalFacility($event)"
                        jhiTranslate="srsaApp.mta.startService"
                      >
                        بدء الخدمة
                      </button>
                    </div>
                  </div>
                  <p class="">
                    {{ isArabic() ? selectedServiceType?.descAr : selectedServiceType?.descEn }}
                  </p>
                </div>
                <div class="dga-tabs">
                  <p-tabView>
                    <p-tabPanel [header]="'srsaApp.permitRequest.detail.requiredDocuments' | translate">
                      <div class="tab-pane fade animate fadeInRight">
                        <ol class="mb-4 d-flex flex-column gap-4">
                          <li class="gap-3 align-items-start" *ngFor="let requirement of requiredDocuments">
                            <span class="tabs-data pt-1">{{ isArabic() ? requirement.val_ar : requirement.val_en }} </span>
                          </li>
                        </ol>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="'srsaApp.permitRequest.detail.serviceTerms' | translate">
                      <div class="tab-pane fade animate fadeInRight">
                        <ol class="mb-4 d-flex flex-column gap-4">
                          <li class="gap-3 align-items-start">
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.yachtCharterPermit.detail.serviceTerm1"
                              >ان يكون السجل التجاري نشط</span
                            >
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1">{{ 'srsaApp.yachtCharterPermit.detail.serviceTerm2' | translate }}</span>
                          </li>
                        </ol>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="'srsaApp.permitRequest.detail.steps' | translate">
                      <div class="tab-pane fade animate fadeInRight">
                        <ol class="mb-4 d-flex flex-column gap-4">
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.yachtCharterPermit.detail.step1">بيانات السجل التجاري</span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.yachtCharterPermit.detail.step2">بيانات العنوان الوطني</span>
                          </li>
                          <li class="gap-3 align-items-start">
                            <!-- <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> -->
                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.yachtCharterPermit.detail.step3">بيانات الرخصة</span>
                          </li>
                          <!--                          <li class="gap-3 align-items-start">-->
                          <!--                            &lt;!&ndash; <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> &ndash;&gt;-->
                          <!--                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.extraData">بيانات إضافية</span>-->
                          <!--                          </li>-->
                          <!--                          <li class="gap-3 align-items-start">-->
                          <!--                            &lt;!&ndash; <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> &ndash;&gt;-->
                          <!--                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.attachments">المرفقات</span>-->
                          <!--                          </li>-->
                          <!--                          <li class="gap-3 align-items-start">-->
                          <!--                            &lt;!&ndash; <img class="" src="../../../../content/images/redChecked.png" alt="redChecked" /> &ndash;&gt;-->
                          <!--                            <span class="tabs-data pt-1" jhiTranslate="srsaApp.permitRequest.preview">المعاينة</span>-->
                          <!--                          </li>-->
                        </ol>
                      </div>
                    </p-tabPanel>
                  </p-tabView>
                </div>
              </div>
            </div>

            <!-- wizard steps starts from here -->
            <section *ngIf="wizardDisplay">
              <div class="row g-4 mb-4">
                <div class="row p-lg-4">
                  <app-page-title [title]="'srsaApp.yachtCharterPermit.home.title' | translate" />
                </div>
                <div class="row">
                  <div class="d-flex p-lg-4 justify-content-between">
                    <div class="">
                      <span class="linces-header" jhiTranslate="srsaApp.yachtCharterPermit.home.title">رخصة</span>
                    </div>
                    <button type="button" [routerLink]="['/yacht-charter-permit']" class="btn btn-outline-danger-secondary">
                      <svg-icon src="content/images/svg-icons/dga/cancel.svg"></svg-icon>
                      <span jhiTranslate="srsaApp.permitRequest.cancelRequest"></span>
                    </button>
                  </div>
                </div>
                <div class="row stepper-card mb-4">
                  <div class="d-flex-end w-100">
                    <div class="gap-0 mk-wizard-header overflow-hidden p-lg-4 position-relative rounded-4 w-100">
                      <div
                        *ngFor="let wizardStep of shownSteps; let i = index"
                        class="w-100 rounded-4 mk-wizard-step"
                        [ngClass]="i + 1 === currentStep ? 'mk-current-step' : ''"
                      >
                        <div
                          class="px-1 rounded-3 d-flex-center flex-md-column gap-2 rounded-4 text-center mk-wizard-step-inner justify-content-start"
                        >
                          <div class="rounded-circle mk-step-number">{{ i + 1 }}</div>
                          <div class="my-2 mk-step-title">{{ isArabic() ? wizardStep.titleAr : wizardStep.titleEn }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="currentStep === 1">
                <div class="card p-4 mb-5">
                  <div class="row g-3">
                    <div class="align-items-center col-12 d-flex gap-3">
                      <label class="form-label m-0" jhiTranslate="srsaApp.company.mainCR">mainCR</label>
                      <div class="fs-6 item-value">{{ isArabic() ? registeredCompany!.crName : registeredCompany!.crName }}</div>
                    </div>
                    <div class="col-lg-5 col-md-8">
                      <label class="form-label" jhiTranslate="srsaApp.permitRequest.detail.subsidiaryCommercialRegistrationSelect"
                        >يرجى اختيار السجل التجاري الفرعي للمنشأة</label
                      >
                      <select
                        class="form-select"
                        id="relatedCompany"
                        data-cy="relatedCompany"
                        [(ngModel)]="subCompany"
                        #relatedCompany
                        [ngModelOptions]="{ standalone: true }"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="relatedCompany" *ngFor="let relatedCompany of relatedCompanies">
                          {{ isArabic() ? relatedCompany.nameAr : relatedCompany.nameEn }}
                        </option>
                      </select>
                    </div>
                    <div class="align-items-end col-md-4 col-lg-7 d-flex justify-content-end">
                      <button
                        type="button"
                        id="cancel-save"
                        data-cy="entityCreateCancelButton"
                        class="btn btn-primary w-sm-100 px-5"
                        (click)="onSelectedCR(relatedCompany.value); showCrDetails = true"
                      >
                        <span jhiTranslate="srsaApp.permitRequest.detail.confirmation">تاكيد</span>
                      </button>
                    </div>
                  </div>
                  <p id="jhi-permitRq5" data-cy="PermitRequestCreateUpdateHeading" class="headFont">
                    {{ isArabic() ? 'بيانات المنشآت الفرعيه' : 'Branch Establishments Data' }}
                  </p>
                  <hr />
                  <div class="mb-5">
                    <div class="table-responsive">
                      <table class="table table-striped dga-table" aria-describedby="relatedCompaniesTable">
                        <thead>
                          <tr>
                            <th scope="col" class="text-start">{{ isArabic() ? 'اسم المنشأة' : 'Establishment Name' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'الرقم الموحد' : 'CR National Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'رقم السجل التجاري' : 'CR Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'الرقم الموحد الرئيسي' : 'Main CR National Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'رقم السجل التجاري الرئيسي' : 'Main CR Number' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'نوع الكيان' : 'Entity Type' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'رقم الإصدار' : 'Version No' }}</th>
                            <th scope="col" class="text-start">{{ isArabic() ? 'نوع الارتباط' : 'Relationship Type' }}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let branch of relatedCompanies; trackBy: trackIdentity">
                            <td class="text-start">{{ isArabic() ? branch.nameAr : branch.nameEn }}</td>
                            <td class="text-start">{{ branch.crNationalNumber }}</td>
                            <td class="text-start">{{ branch.crNumber || '-' }}</td>
                            <td class="text-start">{{ branch.mainCrNationalNumber }}</td>
                            <td class="text-start">{{ branch.mainCrNumber || '-' }}</td>
                            <td class="text-start">{{ isArabic() ? branch.entityTypeAr || '-' : branch.entityTypeEn || '-' }}</td>
                            <td class="text-start">{{ branch.versionNo }}</td>
                            <td class="text-start">
                              {{ isArabic() ? (branch.main ? 'رئيسي' : 'فرعي') : branch.main ? 'Main' : 'Branch' }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div class="mk-cr-details" *ngIf="showCrDetails">
                  <p
                    id="jhi-permit4"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.establishmentData"
                  >
                    بيانات المنشأه
                  </p>
                  <hr />
                  <jhi-company-details-tables [crNumber]="registeredCompany?.crNumber || ''"></jhi-company-details-tables>

                  <p
                    *ngIf="requiredCrField"
                    id="jhi-permitRq7"
                    data-cy="PermitRequestCreateUpdateHeading"
                    class="headFont"
                    jhiTranslate="srsaApp.permitRequest.investmentLicense"
                  >
                    رخصة الاستثمار
                  </p>
                  <hr />
                  <div class="row g-2" *ngIf="requiredCrField">
                    <div class="form-field">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.investmentType"> نوع الاستثمار</label>

                      <select
                        required
                        class="form-select"
                        id="selectedInvestmentType"
                        name="selectedInvestmentType"
                        formControlName="invTypeId"
                        (change)="onSelectedInvestmentType()"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="investmentType.id" *ngFor="let investmentType of investmentTypes">
                          {{ isArabic() ? investmentType.nameAr : investmentType.nameEn }}
                        </option>
                      </select>
                      <div
                        *ngIf="
                          editForm.get('invTypeId')!.invalid && (editForm.get('invTypeId')!.dirty || editForm.get('invTypeId')!.touched)
                        "
                      >
                        <small
                          class="form-text text-danger"
                          *ngIf="editForm.get('invTypeId')?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <div class="form-field" *ngIf="editForm.get('invTypeId')?.value === 1">
                      <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensingAuthority"> الجهة المرخصة</label>
                      <select
                        #selectedLicenseProv
                        class="form-select"
                        id="selectedLicenseProviderNational"
                        data-cy="selectedLicenseProvider"
                        formControlName="licenseProvider"
                        (ngModelChange)="onSelectedLicenseProvider($event)"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="licenseProviderItem" *ngFor="let licenseProviderItem of licenseProvidersByCategory">
                          {{ isArabic() ? licenseProviderItem.nameAr : licenseProviderItem.nameEn }}
                        </option>
                      </select>
                    </div>
                    <div class="form-field" *ngIf="editForm.get('invTypeId')?.value !== 1">
                      <label class="form-label required" jhiTranslate="srsaApp.permitRequest.licensingAuthority"> الجهة المرخصة</label>

                      <select
                        #selectedLicenseProv
                        class="form-select"
                        id="selectedLicenseProvider"
                        data-cy="selectedLicenseProvider"
                        formControlName="licenseProvider"
                        (ngModelChange)="onSelectedLicenseProvider($event)"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="licenseProviderItem" *ngFor="let licenseProviderItem of licenseProvidersByCategory">
                          {{ isArabic() ? licenseProviderItem.nameAr : licenseProviderItem.nameEn }}
                        </option>
                      </select>
                    </div>
                    <div class="form-field" *ngIf="requiredCrField">
                      <div *ngIf="selectedLicenseProvider?.code == 'MISA' && editForm.get('invTypeId')?.value !== 1">
                        <label
                          class="form-label required"
                          for="field_misaLicenseNumber"
                          jhiTranslate="srsaApp.permitRequest.misaLicenseNumber"
                          >Misa License Number</label
                        >
                        <input
                          required
                          type="text"
                          class="form-control"
                          name="misaLicenseNumber"
                          id="field_misaLicenseNumber"
                          data-cy="misaLicenseNumber"
                          formControlName="misaLicenseNumber"
                          minlength="2"
                          maxlength="20"
                        />
                        <div
                          *ngIf="
                            editForm.get('misaLicenseNumber')!.invalid &&
                            (editForm.get('misaLicenseNumber')!.dirty || editForm.get('misaLicenseNumber')!.touched)
                          "
                        >
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('misaLicenseNumber')?.errors?.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                      <div *ngIf="selectedLicenseProvider?.code == 'ECZA' && editForm.get('invTypeId')?.value !== 1">
                        <label
                          class="form-label required"
                          for="field_eczaLicenseNumberRequired"
                          jhiTranslate="srsaApp.permitRequest.eczaLicenseNumber"
                          >Ecza License Number</label
                        >

                        <input
                          required
                          type="text"
                          class="form-control"
                          name="eczaLicenseNumberRequired"
                          id="field_eczaLicenseNumberRequired"
                          data-cy="eczaLicenseNumber"
                          formControlName="eczaLicenseNumber"
                          minlength="2"
                          maxlength="20"
                        />
                        <div
                          *ngIf="
                            editForm.get('eczaLicenseNumber')!.invalid &&
                            (editForm.get('eczaLicenseNumber')!.dirty || editForm.get('eczaLicenseNumber')!.touched)
                          "
                        >
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('eczaLicenseNumber')?.errors?.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                        </div>
                      </div>
                      <div *ngIf="selectedLicenseProvider?.code == 'ECZA' && editForm.get('invTypeId')?.value === 1">
                        <label class="form-label" for="field_eczaLicenseNumber" jhiTranslate="srsaApp.permitRequest.eczaLicenseNumber"
                          >Ecza License Number</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          name="eczaLicenseNumber"
                          id="field_eczaLicenseNumber"
                          data-cy="eczaLicenseNumber"
                          formControlName="eczaLicenseNumber"
                          minlength="2"
                          maxlength="20"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card" *ngIf="currentStep === 2">
                <div
                  class="alert alert-danger"
                  *ngIf="
                    selectedMarineMediumModel &&
                    (selectedMarineMedium?.permitSaudiShipResponse != null || selectedMarineMedium?.permitForeignShipResponse != null) &&
                    !validPermit
                  "
                >
                  <span jhiTranslate="srsaApp.permitRequest.noValidPermit">No Records found</span>
                </div>
                <div class="card-custome-2 p-4">
                  <div class="row">
                    <div class="col-4">
                      <label class="form-label required" jhiTranslate="srsaApp.mta.marineMedium">الواسطات البحرية</label>

                      <select
                        required
                        class="form-select"
                        id="selectedMarineMedium"
                        data-cy="selectedMarineMedium"
                        [(ngModel)]="selectedMarineMediumModel"
                        [ngModelOptions]="{ standalone: true }"
                        #MarineMedium="ngModel"
                        (change)="getMarineMedium(selectedMarineMediumModel)"
                      >
                        <option [ngValue]="null"></option>
                        <option [ngValue]="marineMedium" *ngFor="let marineMedium of marineMediums">
                          {{ isArabic() ? marineMedium.nameAr : marineMedium.nameEn }}
                        </option>
                      </select>
                      <div *ngIf="MarineMedium!.invalid && (MarineMedium!.dirty || MarineMedium!.touched)">
                        <small
                          class="form-text text-danger"
                          *ngIf="MarineMedium?.errors?.required"
                          jhiTranslate="entity.validation.required"
                        >
                          هذا الحقل مطلوب
                        </small>
                      </div>
                    </div>
                    <!--                    <div class="col-12 mt-3">-->
                    <!--                      <button-->
                    <!--                        type="button"-->
                    <!--                        class="btn btn-primary"-->
                    <!--                        [disabled]="!registrationNumber || !ownerIdNumber"-->
                    <!--                        (click)="searchMarineByRegistrationAndOwnerId()"-->
                    <!--                      >-->
                    <!--                        <span jhiTranslate="srsaApp.marineMedium.search">بحث</span>-->
                    <!--                      </button>-->
                    <!--                    </div>-->
                  </div>
                </div>
                <div class="row g-2 mb-5 mt-3" *ngIf="selectedMarineMedium">
                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label">اسم اليخت (عربي)</label>
                    <p class="fw-bold">
                      {{ selectedMarineMedium.nameAr }}
                    </p>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label">اسم اليخت (انجليزي)</label>
                    <p class="fw-bold">
                      {{ selectedMarineMedium.nameEn }}
                    </p>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.imoNumber">رقم اليخت لدى المنظمة البحرية الدولية</label>
                    <p class="fw-bold">{{ selectedMarineMedium.imoNumber }}</p>
                  </div>

                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.marineMediumType">نوع الواسطة البحرية</label>
                    <p class="fw-bold">
                      {{ isArabic() ? selectedMarineMedium.marineMediumType?.nameAr : selectedMarineMedium.marineMediumType?.nameEn }}
                    </p>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.flagCountry">دولة العلم</label>
                    <p class="fw-bold">
                      {{ isArabic() ? selectedMarineMedium.flagNationality?.nameAr : selectedMarineMedium.flagNationality?.nameEn }}
                    </p>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.registrationNumber">رقم التسجيل</label>
                    <p class="fw-bold">{{ selectedMarineMedium.registrationNumber }}</p>
                  </div>

                  @if (validPermit) {
                    <p
                      id="jhi-permit95"
                      data-cy="PermitRequestCreateUpdateHeading"
                      class="headFont"
                      jhiTranslate="srsaApp.technicalPermit.navigationalData"
                    >
                      بيانات الترخيص الملاحي
                    </p>
                    <hr />

                    <div class="col-md-4 col-lg-3 col-xl-4">
                      <label class="form-label" jhiTranslate="srsaApp.marineMedium.validPermitStartDate">رقم التسجيل</label>
                      <p class="fw-bold">{{ validPermitStartDate | date: 'dd/MM/yyyy' }}</p>
                    </div>

                    <div class="col-md-4 col-lg-3 col-xl-4">
                      <label class="form-label" jhiTranslate="srsaApp.marineMedium.validPermitEndDate">رقم التسجيل</label>
                      <p class="fw-bold">{{ validPermitEndDate | date: 'dd/MM/yyyy' }}</p>
                    </div>

                    @if (
                      selectedMarineMedium!.permitSaudiShipResponse != null &&
                      selectedMarineMedium!.permitSaudiShipResponse!.isSuccess === true &&
                      selectedMarineMedium!.permitSaudiShipResponse!.data != null
                    ) {
                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.cancelReasonId">cancelReasonId</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.cancelReasonId }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.shipId">shipId</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.shipId }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.address">address</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.address }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.ratingAgencyEndDate">ratingAgencyEndDate</label>
                        <p class="fw-bold">
                          {{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.ratingAgencyEndDate }}
                        </p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.insuranceEndDate">insuranceEndDate</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.insuranceEndDate }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.insuranceTypeId">insuranceTypeId</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.insuranceTypeId }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.ismCompany">ismCompany</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.ismCompany }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.lastCheckDate">lastCheckDate</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.lastCheckDate }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.isLritOrAis">isLritOrAis</label>
                        <p class="fw-bold">
                          {{
                            selectedMarineMedium?.permitSaudiShipResponse?.data?.[validPermitIndex]?.isLritOrAis
                              ? ('srsaApp.arrivalRequest.yes' | translate)
                              : ('srsaApp.arrivalRequest.no' | translate)
                          }}
                        </p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.notes">notes</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.notes }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.navigationDomesticPermitStatusId"
                          >navigationDomesticPermitStatusId</label
                        >
                        <p class="fw-bold">
                          {{ selectedMarineMedium!.permitSaudiShipResponse!.data[validPermitIndex]!.navigationDomesticPermitStatusId }}
                        </p>
                      </div>
                    } @else if (
                      selectedMarineMedium!.permitForeignShipResponse != null &&
                      selectedMarineMedium!.permitForeignShipResponse!.isSuccess === true &&
                      selectedMarineMedium!.permitForeignShipResponse!.data != null
                    ) {
                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.agentName">رقم التسجيل</label>
                        <p class="fw-bold">
                          {{
                            isArabic()
                              ? selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.agentNameAr
                              : selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.agentNameEn
                          }}
                        </p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.arrivalMarina">رقم التسجيل</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.arrivalMarina }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.saudiShipActivity">saudiShipActivity</label>
                        <p class="fw-bold">
                          {{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.saudiShipActivity }}
                        </p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.navigationRegionId">saudiShipActivity</label>
                        <p class="fw-bold">
                          {{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.navigationRegionId }}
                        </p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.agentImonumber">agentImonumber</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.agentImonumber }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.agentSaudiAddressCityId"
                          >agentSaudiAddressCityId</label
                        >
                        <p class="fw-bold">
                          {{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.agentSaudiAddressCityId }}
                        </p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.managerName">managerName</label>
                        <p class="fw-bold">{{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.managerName }}</p>
                      </div>

                      <div class="col-md-4 col-lg-3 col-xl-4">
                        <label class="form-label" jhiTranslate="srsaApp.marineMedium.managerBirthday">managerBirthday</label>
                        <p class="fw-bold">
                          {{ selectedMarineMedium!.permitForeignShipResponse!.data[validPermitIndex]!.managerBirthday }}
                        </p>
                      </div>
                    }
                  }

                  <div class="col-md-4 col-lg-3 col-xl-4">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.ownershipType">نوع الملكية</label>
                    <p class="fw-bold">{{ 'srsaApp.marineMedium.' + selectedMarineMedium.ownershipType | translate }}</p>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-4" *ngIf="selectedMarineMedium?.registrationCertificateAttachments!">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.registrationDocuments">شهادة التسجيل</label>
                    <div>
                      @for (attachment of selectedMarineMedium.registrationCertificateAttachments; track $index) {
                        @if (attachment.id) {
                          <button
                            type="button"
                            (click)="getMarineAttachment(attachment.id)"
                            class="btn download-btn d-flex justify-content-between align-items-center"
                          >
                            <p>{{ attachment.docName }}</p>
                            <img src="../../../../content/images/fi_download.svg" />
                          </button>
                        }
                      }
                    </div>
                  </div>
                  <div class="col-md-4 col-lg-3 col-xl-4" *ngIf="selectedMarineMedium?.ownersDocumentsAttachments!">
                    <label class="form-label" jhiTranslate="srsaApp.marineMedium.ownersDocuments">هوية المالك/الملاك</label>

                    <div>
                      @for (attachment of selectedMarineMedium.ownersDocumentsAttachments; track $index) {
                        @if (attachment.id) {
                          <button
                            type="button"
                            (click)="getMarineAttachment(attachment.id)"
                            class="btn download-btn d-flex justify-content-between align-items-center"
                          >
                            <P class="">{{ attachment.docName }}</P>
                            <img src="../../../../content/images/fi_download.svg" />
                          </button>
                        }
                      }
                    </div>
                  </div>
                </div>
              </div>

              <div class="card" *ngIf="currentStep === 3">
                <section class="mb-5">
                  <p id="jhi-permit8" data-cy="PermitRequestCreateUpdateHeading" class="headFont">بيانات الطلب</p>
                  <hr />
                  <div class="row g-4 mb-4">
                    <div>
                      <div class="row g-4 mb-4">
                        <jhi-alert-error></jhi-alert-error>

                        @if (editForm.controls.id.value !== null) {
                          <div class="col-md-6 col-lg-4">
                            <label class="form-label" for="field_id" jhiTranslate="srsaApp.yachtCharterPermit.id">ID</label>
                            <input
                              type="number"
                              class="form-control"
                              name="id"
                              id="field_id"
                              data-cy="id"
                              formControlName="id"
                              [readonly]="true"
                            />
                          </div>
                        }

                        <div class="col-md-6 col-lg-4">
                          <label class="form-label required" jhiTranslate="srsaApp.mta.marineMediumName">صاحب الواسطة البحرية</label>

                          <input
                            type="text"
                            class="form-control"
                            [(ngModel)]="isArabic() ? selectedMarineMedium!.nameAr : selectedMarineMedium!.nameEn"
                            [ngModelOptions]="{ standalone: true }"
                            readonly
                            disabled
                          />
                        </div>
                        <div class="col-md-6 col-lg-4">
                          <label class="form-label required" jhiTranslate="srsaApp.mta.agentName">الوكيل الملاحي السياحي</label>

                          <input
                            max="9999999"
                            required
                            type="text"
                            class="form-control"
                            [(ngModel)]="selectedAgent!.crName"
                            [ngModelOptions]="{ standalone: true }"
                            readonly
                            disabled
                          />
                        </div>

                        <div class="col-md-6 col-lg-4">
                          <label class="form-label required" for="field_captainName" jhiTranslate="srsaApp.yachtCharterPermit.captainName"
                            >Captain Name</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            name="captainName"
                            id="field_captainName"
                            data-cy="captainName"
                            formControlName="captainName"
                          />

                          <!-- Error Messages -->
                          <div class="invalid-feedback" *ngIf="hasFieldError('captainName', 'required')">
                            <span jhiTranslate="entity.validation.required">This field is required.</span>
                          </div>

                          <div class="invalid-feedback" *ngIf="hasFieldError('captainName', 'hasNumbers')">
                            <span jhiTranslate="entity.validation.noNumbers">Numbers are not allowed in this field.</span>
                          </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                          <label
                            class="form-label required"
                            for="field_captainMobileNumber"
                            jhiTranslate="srsaApp.yachtCharterPermit.captainMobileNumber"
                            >Captain Mobile Number</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            name="captainMobileNumber"
                            id="field_captainMobileNumber"
                            data-cy="captainMobileNumber"
                            formControlName="captainMobileNumber"
                            required
                            minlength="9"
                            maxlength="14"
                            pattern="(9665)(5|0|3|6|4|9|1|8|7)([0-9]{7})$"
                          />
                          <div
                            *ngIf="
                              editForm.get('captainMobileNumber')!.invalid &&
                              (editForm.get('captainMobileNumber')!.dirty || editForm.get('captainMobileNumber')!.touched)
                            "
                          >
                            <small
                              class="form-text text-danger"
                              *ngIf="editForm.get('captainMobileNumber')?.errors?.required"
                              jhiTranslate="entity.validation.required"
                            >
                              هذا الحقل مطلوب
                            </small>
                            <small
                              class="form-text text-danger"
                              *ngIf="editForm.get('captainMobileNumber')?.errors?.pattern"
                              jhiTranslate="register.messages.validate.mobileNumber.pattern"
                            >
                              رقم الجوال غير صحيح يجب أن يبدأ ب 9665.
                            </small>
                            <small
                              class="form-text text-danger"
                              *ngIf="editForm.get('captainMobileNumber')?.errors?.minlength"
                              jhiTranslate="register.messages.validate.mobileNumber.minlength"
                            >
                              اسم المستخدم الخاصة بك مطلوب ليكون على الأقل 1 حرف.
                            </small>
                            <small
                              class="form-text text-danger"
                              *ngIf="editForm.get('captainMobileNumber')?.errors?.maxlength"
                              jhiTranslate="register.messages.validate.mobileNumber.maxlength"
                            >
                              اسم المستخدم الخاصة بك لا يمكن أن يكون أطول من 50 حرفا.
                            </small>
                          </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                          <label
                            class="form-label required"
                            for="field_anchorageArea"
                            jhiTranslate="srsaApp.yachtCharterPermit.anchorageArea"
                            >anchorage Area</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            name="anchorageArea"
                            id="field_anchorageArea"
                            data-cy="anchorageArea"
                            formControlName="anchorageArea"
                          />

                          <!-- Error Messages -->
                          <div class="invalid-feedback" *ngIf="hasFieldError('anchorageArea', 'required')">
                            <span jhiTranslate="entity.validation.required">This field is required.</span>
                          </div>

                          <div class="invalid-feedback" *ngIf="hasFieldError('anchorageArea', 'hasNumbers')">
                            <span jhiTranslate="entity.validation.noNumbers">Numbers are not allowed in this field.</span>
                          </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                          <label class="form-label required" for="field_charterName" jhiTranslate="srsaApp.yachtCharterPermit.chartererName"
                            >anchorage Area</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            name="anchorageArea"
                            id="field_charterName"
                            data-cy="charterName"
                            formControlName="charterName"
                          />

                          <!-- Error Messages -->
                          <div class="invalid-feedback" *ngIf="hasFieldError('charterName', 'required')">
                            <span jhiTranslate="entity.validation.required">This field is required.</span>
                          </div>

                          <div class="invalid-feedback" *ngIf="hasFieldError('charterName', 'hasNumbers')">
                            <span jhiTranslate="entity.validation.noNumbers">Numbers are not allowed in this field.</span>
                          </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                          <label
                            class="form-label required"
                            for="field_anchorageArea"
                            jhiTranslate="srsaApp.yachtCharterPermit.chartererNumber"
                            >anchorage Area</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            name="chartererNumber"
                            id="field_chartererNumber"
                            data-cy="chartererNumber"
                            formControlName="charterNumber"
                            required
                            minlength="9"
                            maxlength="14"
                            pattern="(9665)(5|0|3|6|4|9|1|8|7)([0-9]{7})$"
                          />
                        </div>
                        <div
                          *ngIf="
                            editForm.get('charterNumber')!.invalid &&
                            (editForm.get('charterNumber')!.dirty || editForm.get('charterNumber')!.touched)
                          "
                        >
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('charterNumber')?.errors?.required"
                            jhiTranslate="entity.validation.required"
                          >
                            هذا الحقل مطلوب
                          </small>
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('charterNumber')?.errors?.pattern"
                            jhiTranslate="register.messages.validate.mobileNumber.pattern"
                          >
                            رقم الجوال غير صحيح يجب أن يبدأ ب 9665.
                          </small>
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('charterNumber')?.errors?.minlength"
                            jhiTranslate="register.messages.validate.mobileNumber.minlength"
                          >
                          </small>
                          <small
                            class="form-text text-danger"
                            *ngIf="editForm.get('captainMobileNumber')?.errors?.maxlength"
                            jhiTranslate="register.messages.validate.mobileNumber.maxlength"
                          >
                          </small>
                        </div>

                        <div class="col-md-6 col-lg-4">
                          <label class="form-label required" for="field_startDate" jhiTranslate="srsaApp.yachtCharterPermit.startDate"
                            >Start Date</label
                          >
                          <div class="d-flex">
                            <gregorian-calendar
                              name="startDate"
                              id="field_startDate"
                              formControlName="startDate"
                              (gregCalendar)="setStartDate($event)"
                              placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                              [minDateAsToday]="true"
                              [maxDateAsToday]="false"
                            >
                            </gregorian-calendar>
                          </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                          <label class="form-label required" for="field_endDate" jhiTranslate="srsaApp.yachtCharterPermit.endDate"
                            >End Date</label
                          >
                          <div class="d-flex">
                            <gregorian-calendar
                              name="endDate"
                              id="field_endDate"
                              formControlName="endDate"
                              (gregCalendar)="setEndDate($event)"
                              placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                              [minDateAsToday]="true"
                              [maxDateAsToday]="false"
                            >
                            </gregorian-calendar>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>

              <div class="card" *ngIf="currentStep === 4">
                <div class="card-custome-2">
                  <div class="p-4">
                    <div class="row">
                      <div class="col-lg-6 mk-attachments-list d-flex flex-column">
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" checked name="options-base" class="btn-check" id="btnCheck1" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck1"
                              jhiTranslate="srsaApp.yachtCharterPermit.yachtTechnicalLicense"
                              (click)="clickToShow(1)"
                            >
                              الترخيص الفني لليخت
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck2" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck2"
                              jhiTranslate="srsaApp.yachtCharterPermit.yachtNavigationLicense"
                              (click)="clickToShow(2)"
                            >
                              الترخيص الملاحي لليخت
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck3" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck3"
                              jhiTranslate="srsaApp.yachtCharterPermit.validTechnicalInspectionReport"
                              (click)="clickToShow(3)"
                            >
                              تقرير معاينة فنية ساري المفعول
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck4" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck4"
                              jhiTranslate="srsaApp.yachtCharterPermit.copyOfTouristMaritimeAgentLicense"
                              (click)="clickToShow(4)"
                            >
                              رخصة الوكيل الملاحي السياحي
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck5" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck5"
                              (click)="clickToShow(5)"
                              jhiTranslate="srsaApp.yachtCharterPermit.copyOfOwnerIdentificationData"
                            >
                              البيانات التعريفية للمالك
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck6" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck6"
                              jhiTranslate="srsaApp.yachtCharterPermit.yachtRegistrationAndTechnicalCertificates"
                              (click)="clickToShow(6)"
                            >
                              شهادة التسجيل والشهادات الفنية لليخت
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck7" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck7"
                              jhiTranslate="srsaApp.yachtCharterPermit.minimumSafeManningDocument"
                              (click)="clickToShow(7)"
                            >
                              وثيقة الحد الأدنى للتطقيم الامن
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck8" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck8"
                              jhiTranslate="srsaApp.yachtCharterPermit.captainsMaritimeCertificate"
                              (click)="clickToShow(8)"
                            >
                              شهادة القبطان البحرية
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck9" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck9"
                              (click)="clickToShow(9)"
                              jhiTranslate="srsaApp.yachtCharterPermit.insuranceCertificate"
                            >
                              شهادة التأمين
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck10" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck10"
                              jhiTranslate="srsaApp.yachtCharterPermit.copyOfYachtRentalCompanyLicense"
                              (click)="clickToShow(10)"
                            >
                              ترخيص شركة تأجير اليخوت
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck11" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4 required"
                              for="btnCheck11"
                              jhiTranslate="srsaApp.yachtCharterPermit.mooringContractLicense"
                              (click)="clickToShow(11)"
                            >
                              عقد رسو/ ترخيص الرسو
                            </label>
                          </div>
                        </div>
                        <div>
                          <div class="mk-attachments-item">
                            <input type="radio" name="options-base" class="btn-check" id="btnCheck12" autocomplete="off" />
                            <label
                              class="btn btn-outline-primary rounded-4"
                              for="btnCheck12"
                              jhiTranslate="srsaApp.yachtCharterPermit.approvedRentalContract"
                              (click)="clickToShow(12)"
                            >
                              عقد الإيجار المعتمد
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-6 mk-custom-dropzone">
                        <div>
                          <dropzone
                            [hidden]="!hidden1"
                            id="CR"
                            [config]="getFileConfig()"
                            (addedFile)="onUploadFileSuccess($event, 'yachtTechnicalLicense')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'yachtTechnicalLicense')"
                          >
                          </dropzone>

                          <a
                            *ngIf="yachtCharterPermit?.id && yachtTechnicalLicenseAttachment && hidden1"
                            (click)="getAttachment(yachtTechnicalLicenseAttachment!.id)"
                            id="cr1"
                            alt="File"
                            >{{ yachtTechnicalLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="MainCR"
                            [config]="getFileConfig()"
                            [hidden]="!hidden2"
                            (addedFile)="onUploadFileSuccess($event, 'yachtNavigationLicense')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'yachtNavigationLicense')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && yachtNavigationalLicenseAttachment && hidden2"
                            (click)="getAttachment(yachtNavigationalLicenseAttachment!.id)"
                            id="MainCR1"
                            alt="File"
                            >{{ yachtNavigationalLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="CompanyNationalAddress"
                            [config]="getFileConfig()"
                            [hidden]="!hidden3"
                            (addedFile)="onUploadFileSuccess($event, 'validTechnicalInspectionReport')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'validTechnicalInspectionReport')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && validTechnicalInspectionReportAttachment && hidden3"
                            (click)="getAttachment(validTechnicalInspectionReportAttachment!.id)"
                            id="CompanyNationalAddress1"
                            alt="File"
                            >{{ validTechnicalInspectionReportAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="CopyWorkforceMinistryHR"
                            [config]="getFileConfig()"
                            [hidden]="!hidden4"
                            (addedFile)="onUploadFileSuccess($event, 'copyOfTouristMaritimeAgentLicense')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'copyOfTouristMaritimeAgentLicense')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && touristShippingAgentLicenseAttachment && hidden4"
                            (click)="getAttachment(touristShippingAgentLicenseAttachment!.id)"
                            id="CopyWorkforceMinistryHR1"
                            alt="File"
                            >{{ touristShippingAgentLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="InvestmentLicense"
                            [config]="getFileConfig()"
                            [hidden]="!hidden5"
                            (addedFile)="onUploadFileSuccess($event, 'copyOfOwnerIdentificationData')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'copyOfOwnerIdentificationData')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && ownerIdentificationDataAttachment && hidden5"
                            (click)="getAttachment(ownerIdentificationDataAttachment!.id)"
                            id="InvestmentLicense1"
                            alt="File"
                            >{{ ownerIdentificationDataAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="BankGuarantee"
                            [config]="getFileConfig()"
                            [hidden]="!hidden6"
                            (addedFile)="onUploadFileSuccess($event, 'yachtRegistrationAndTechnicalCertificates')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'yachtRegistrationAndTechnicalCertificates')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && registrationCertificateAndTechnicalCertificatesOfYachtAttachment && hidden6"
                            (click)="getAttachment(registrationCertificateAndTechnicalCertificatesOfYachtAttachment!.id)"
                            id="BankGuarantee1"
                            alt="File"
                            >{{ registrationCertificateAndTechnicalCertificatesOfYachtAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="ZakatLicense"
                            [config]="getFileConfig()"
                            [hidden]="!hidden7"
                            (addedFile)="onUploadFileSuccess($event, 'minimumSafeManningDocument')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'minimumSafeManningDocument')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && minimumSafeFineningDocumentAttachment && hidden7"
                            (click)="getAttachment(minimumSafeFineningDocumentAttachment!.id)"
                            id="ZakatLicense1"
                            alt="File"
                            >{{ minimumSafeFineningDocumentAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="InternationalSafetyManagement"
                            [config]="getFileConfig()"
                            [hidden]="!hidden8"
                            (addedFile)="onUploadFileSuccess($event, 'captainsMaritimeCertificate')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'captainsMaritimeCertificate')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && marineCaptainCertificateAttachment && hidden8"
                            (click)="getAttachment(marineCaptainCertificateAttachment!.id)"
                            id="InternationalSafetyManagement1"
                            alt="File"
                            >{{ marineCaptainCertificateAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            required
                            id="COMPANY_FILE"
                            [config]="getFileConfig()"
                            [hidden]="!hidden9"
                            (addedFile)="onUploadFileSuccess($event, 'insuranceCertificate')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'insuranceCertificate')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && insuranceCertificateAttachment && hidden9"
                            (click)="getAttachment(insuranceCertificateAttachment!.id)"
                            id="COMPANY_FILE1"
                            alt="File"
                            >{{ insuranceCertificateAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <dropzone
                            id="ListMarineOwned"
                            [config]="getFileConfig()"
                            [hidden]="!hidden10"
                            (addedFile)="onUploadFileSuccess($event, 'copyOfYachtRentalCompanyLicense')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'copyOfYachtRentalCompanyLicense')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && yachtCharterCompanyLicenseAttachment && hidden10"
                            (click)="getAttachment(yachtCharterCompanyLicenseAttachment!.id)"
                            id="ListMarineOwned1"
                            alt="File"
                            >{{ yachtCharterCompanyLicenseAttachment.docName! }}</a
                          >
                        </div>
                        <div>
                          <div>
                            <dropzone
                              id="General_Inspection_Report"
                              [config]="getFileConfig()"
                              [hidden]="!hidden11"
                              (addedFile)="onUploadFileSuccess($event, 'mooringContractLicense')"
                              (error)="onUploadFileError($event)"
                              (removedFile)="onFileRemoved($event, 'mooringContractLicense')"
                            >
                            </dropzone>
                            <a
                              *ngIf="yachtCharterPermit?.id && dockingContractAttachment && hidden11"
                              (click)="getAttachment(dockingContractAttachment!.id)"
                              id="General_Inspection_Report_"
                              alt="File"
                              >{{ dockingContractAttachment.docName! }}</a
                            >
                          </div>
                        </div>
                        <div>
                          <dropzone
                            required
                            id="ContractAuthority"
                            [config]="getFileConfig()"
                            [hidden]="!hidden12"
                            (addedFile)="onUploadFileSuccess($event, 'approvedRentalContract')"
                            (error)="onUploadFileError($event)"
                            (removedFile)="onFileRemoved($event, 'approvedRentalContract')"
                          >
                          </dropzone>
                          <a
                            *ngIf="yachtCharterPermit?.id && approvedTenancyContractAttachment && hidden12"
                            (click)="getAttachment(approvedTenancyContractAttachment!.id)"
                            id="ContractAuthority1"
                            alt="File"
                            >{{ approvedTenancyContractAttachment.docName! }}</a
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="currentStep === 5">
                <div class="row" *ngIf="yachtCharterPermit?.id">
                  <div class="p-1 mb-3">
                    <div class="card-body p-2">
                      <p class="font-styel-header" jhiTranslate="srsaApp.permitRequest.rqDetails">تفاصيل الطلب</p>
                      <hr />
                      <div class="table-responsive">
                        <table class="table table-responsive dga-table table-borderless mb-0">
                          <thead>
                            <tr>
                              <th jhiTranslate="srsaApp.permitRequest.licenseNumber">رقم طلب</th>
                              <th jhiTranslate="srsaApp.permitRequest.rqType">نوع الطلب</th>
                              <th jhiTranslate="srsaApp.permitRequest.crName">اسم المنشأة</th>
                              <th jhiTranslate="srsaApp.permitRequest.rqStatus">حالة الطلب</th>
                              @if (yachtCharterPermit?.requestStatus != 'COMPLETED' && yachtCharterPermit?.requestStatus != 'REJECTED') {
                                <th *ngIf="yachtCharterPermit?.assignedEmployeeRole" jhiTranslate="srsaApp.permitRequest.asignedTo">
                                  مسندة إلى
                                </th>
                                <th *ngIf="yachtCharterPermit?.assignedEmployeeRole" jhiTranslate="srsaApp.permitRequest.attributionStatus">
                                  حالة الإسناد
                                </th>
                              }
                              <th *ngIf="yachtCharterPermit?.submitDate" jhiTranslate="srsaApp.permitRequest.rqDate">تاريخ الطلب</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ yachtCharterPermit!.requestNumber }}</td>
                              <td>{{ isArabic() ? selectedServiceType?.nameAr : selectedServiceType?.nameEn }}</td>
                              <td>{{ registeredCompany!.crName }}</td>
                              <td [jhiTranslate]="'srsaApp.technicalPermit.status.' + (yachtCharterPermit!.requestStatus ?? 'null')">
                                {{
                                  {
                                    null: '',
                                    DRAFT: 'DRAFT',
                                    PENDING_REVIEW: 'PENDING_REVIEW',
                                    UNDER_PROCESS: 'UNDER_PROCESS',
                                    APPROVED: 'APPROVED',
                                    REJECTED: 'REJECTED',
                                    DELETED: 'DELETED',
                                    COMPLETED: 'COMPLETED',
                                    PENDING_REPORT_APPROVAL: 'PENDING_REPORT_APPROVAL',
                                    PENDING_MANAGEMENT_LICENSING: 'PENDING_MANAGEMENT_LICENSING',
                                    RETURNED_LICENSING_OFFICER: 'RETURNED_LICENSING_OFFICER',
                                    RETURNED_LICENSING_MANAGER: 'RETURNED_LICENSING_MANAGER',
                                    RETURNED_INSPECTION_OFFICER: 'RETURNED_INSPECTION_OFFICER',
                                    PENDING_INSPECTION: 'PENDING_INSPECTION',
                                    UNDER_INSPECTION: 'UNDER_INSPECTION',
                                    PENDING_VISIT: 'PENDING_VISIT',
                                    PENDING_PAYMENT: 'PENDING_PAYMENT'
                                  }[yachtCharterPermit!.requestStatus ?? 'null']
                                }}
                              </td>
                              @if (
                                yachtCharterPermit?.requestStatus != 'COMPLETED' &&
                                yachtCharterPermit?.requestStatus != 'REJECTED' &&
                                yachtCharterPermit?.assignedEmployeeRole &&
                                yachtCharterPermit?.assignedEmployeeRole != ''
                              ) {
                                <td>
                                  {{ 'userManagement.roles.' + yachtCharterPermit?.assignedEmployeeRole | translate }}
                                </td>

                                @if (
                                  yachtCharterPermit?.assignedEmployeeRole == Authority.SRSA_LICENSING_SUPERVISOR ||
                                  yachtCharterPermit?.assignedEmployeeRole == Authority.SRSA_INSPECTION_SUPERVISOR
                                ) {
                                  <td jhiTranslate="srsaApp.permitRequest.notAssigned"></td>
                                } @else {
                                  <td jhiTranslate="srsaApp.permitRequest.assigned"></td>
                                }
                              }
                              <td>{{ yachtCharterPermit!.submitDate | formatMediumDatetime }}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Accordion -->
                <div class="accordion mk-review-step" id="accordionExample">
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img src="../../../../content/images/request-view/ph_building-bold.svg" alt="ph_building-bold." class="mx-4 me-3" />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseOne"
                        aria-expanded="true"
                        aria-controls="collapseOne"
                        jhiTranslate="srsaApp.mta.establishmentData"
                      >
                        السجل التجاري
                      </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <div class="mk-cr-details">
                          <p
                            id="jhi-permit4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="headFont"
                            jhiTranslate="srsaApp.permitRequest.establishmentData"
                          >
                            بيانات المنشأه
                          </p>
                          <jhi-company-details-tables [crNumber]="registeredCompany?.crNumber || ''"></jhi-company-details-tables>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden" *ngIf="requiredCrField">
                    <h2 class="accordion-header d-flex-center">
                      <img src="../../../../content/images/Group.png" alt="Group.png" class="mx-4 me-3" />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseTwo"
                        aria-expanded="false"
                        aria-controls="collapseTwo"
                        jhiTranslate="srsaApp.mta.licenseData"
                      >
                        بيانات العنوان الوطني
                      </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <span *ngIf="invTypeId !== 0">
                          <p
                            id="jhi-permitRq4"
                            data-cy="PermitRequestCreateUpdateHeading"
                            class="accordion-subtitle"
                            jhiTranslate="srsaApp.permitRequest.investmentLicense"
                          >
                            العنوان الوطني
                          </p>
                          <hr />
                          <div class="row g-2 mb-5">
                            <div class="col-md-4 col-lg-3 col-xl-2">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.investmentType">CR</label>
                              <p class="fw-bold">{{ isArabic() ? investmentType!.nameAr : investmentType!.nameEn }}</p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="selectedLicenseProvider">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensingAuthority">الحي</label>
                              <p class="fw-bold">{{ isArabic() ? selectedLicenseProvider.nameAr : selectedLicenseProvider.nameEn }}</p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="selectedLicenseProvider?.code == 'ECZA'">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.id">الرمز البريدي</label>
                              <p class="fw-bold">{{ editForm.get('eczaLicenseNumber')!.value }}</p>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="selectedLicenseProvider?.code == 'MISA'">
                              <label class="form-label" jhiTranslate="srsaApp.permitRequest.id">الرمز البريدي</label>
                              <p class="fw-bold">{{ editForm.get('misaLicenseNumber')!.value }}</p>
                            </div>
                          </div>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/fluent_card-ui-24-regular.svg"
                        alt="fluent_card-ui-24-regular.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseThree"
                        aria-expanded="false"
                        aria-controls="collapseThree"
                        jhiTranslate="srsaApp.mta.marineMediumData"
                      >
                        الواسطات البحرية
                      </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <p
                          id="jhi-permit4"
                          data-cy="PermitRequestCreateUpdateHeading"
                          class="headFont"
                          jhiTranslate="srsaApp.mta.marineMediumData"
                        >
                          بيانات المنشأه
                        </p>
                        <hr />
                        <div class="row g-2 mb-5 mt-3" *ngIf="selectedMarineMedium">
                          <div class="col-md-4 col-lg-4 col-xl-4">
                            <label class="form-label" jhiTranslate="srsaApp.marineMedium.marineMediumType">نوع الواسطة البحرية</label>
                            <p class="fw-bold">
                              {{
                                isArabic() ? selectedMarineMedium.marineMediumType?.nameAr : selectedMarineMedium.marineMediumType?.nameEn
                              }}
                            </p>
                          </div>
                          <div class="col-md-4 col-lg-4 col-xl-4">
                            <label class="form-label" jhiTranslate="srsaApp.marineMedium.flagCountry">دولة العلم</label>
                            <p class="fw-bold">
                              {{ isArabic() ? selectedMarineMedium.flagNationality?.nameAr : selectedMarineMedium.flagNationality?.nameEn }}
                            </p>
                          </div>
                          <div class="col-md-4 col-lg-4 col-xl-4">
                            <label class="form-label" jhiTranslate="srsaApp.marineMedium.imoNumber"
                              >رقم اليخت لدى المنظمة البحرية الدولية</label
                            >
                            <p class="fw-bold">{{ selectedMarineMedium.imoNumber }}</p>
                          </div>
                          <div class="col-md-4 col-lg-4 col-xl-4" *ngIf="selectedMarineMedium?.registrationCertificateAttachments!">
                            <label class="form-label" jhiTranslate="srsaApp.mta.registrationCertificate">شهادة التسجيل</label>
                            <div>
                              @for (attachment of selectedMarineMedium.registrationCertificateAttachments; track $index) {
                                @if (attachment.id) {
                                  <button
                                    type="button"
                                    (click)="getMarineAttachment(attachment.id)"
                                    class="btn download-btn d-flex justify-content-between align-items-center"
                                  >
                                    <p>{{ attachment.docName }}</p>
                                    <img src="../../../../content/images/fi_download.svg" />
                                  </button>
                                }
                              }
                            </div>
                          </div>
                          <div class="col-md-4 col-lg-4 col-xl-4">
                            <label class="form-label" jhiTranslate="srsaApp.marineMedium.registrationNumber">رقم التسجيل</label>
                            <p class="fw-bold">{{ selectedMarineMedium.registrationNumber }}</p>
                          </div>
                          <div class="col-md-4 col-lg-4 col-xl-4">
                            <label class="form-label" jhiTranslate="srsaApp.marineMedium.ownershipType">نوع الملكية</label>
                            <p class="fw-bold">{{ 'srsaApp.marineMedium.' + selectedMarineMedium.ownershipType | translate }}</p>
                          </div>
                          <div class="col-md-4 col-lg-4 col-xl-4" *ngIf="selectedMarineMedium?.ownersDocumentsAttachments!">
                            <label class="form-label" jhiTranslate="srsaApp.marineMedium.ownersDocuments">هوية المالك/الملاك</label>

                            <div>
                              @for (attachment of selectedMarineMedium.ownersDocumentsAttachments; track $index) {
                                @if (attachment.id) {
                                  <button
                                    type="button"
                                    (click)="getMarineAttachment(attachment.id)"
                                    class="btn download-btn d-flex justify-content-between align-items-center"
                                  >
                                    <P class="">{{ attachment.docName }}</P>
                                    <img src="../../../../content/images/fi_download.svg" />
                                  </button>
                                }
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img src="../../../../content/images/Group.png" alt="Group.png" class="mx-4 me-3" />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseFour"
                        aria-expanded="false"
                        aria-controls="collapseFour"
                        jhiTranslate="srsaApp.mta.mtaData"
                      >
                        بيانات العنوان الوطني
                      </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem">
                        <p
                          id="jhi-permitRqt5"
                          data-cy="PermitRequestCreateUpdateHeading"
                          class="accordion-subtitle"
                          jhiTranslate="srsaApp.mta.mtaData"
                        ></p>
                        <hr />
                        <div class="accordion-body mx-2rem">
                          <div class="row g-4 mb-4">
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.technicalPermit.captainName">Captain Name</label>
                              <p class="fw-bold">{{ editForm.get('captainName')!.value }}</p>
                            </div>
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.technicalPermit.captainMobileNumber"
                                >Captain Mobile Number</label
                              >
                              <p class="fw-bold">{{ editForm.get('captainMobileNumber')!.value }}</p>
                            </div>
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.yachtCharterPermit.chartererName"
                                >Captain Mobile Number</label
                              >
                              <p class="fw-bold">{{ editForm.get('charterName')!.value }}</p>
                            </div>
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.yachtCharterPermit.chartererNumber"
                                >Captain Mobile Number</label
                              >
                              <p class="fw-bold">{{ editForm.get('charterNumber')!.value }}</p>
                            </div>
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.yachtCharterPermit.anchorageArea">anchorage Area</label>
                              <p class="fw-bold">{{ editForm.get('anchorageArea')!.value }}</p>
                            </div>
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.yachtCharterPermit.startDate">Captain Mobile Number</label>
                              <p class="fw-bold">{{ editForm.get('startDate')!.value }}</p>
                            </div>
                            <div class="col-md-6 col-lg-4">
                              <label class="form-label" jhiTranslate="srsaApp.yachtCharterPermit.endDate">Captain Mobile Number</label>
                              <p class="fw-bold">{{ editForm.get('endDate')!.value }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                    <h2 class="accordion-header d-flex-center">
                      <img
                        src="../../../../content/images/request-view/mdi_paper-outline.svg"
                        alt="mdi_paper-outline.svg"
                        class="mx-4 me-3"
                      />
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseFive"
                        aria-expanded="false"
                        aria-controls="collapseFive"
                        jhiTranslate="srsaApp.permitRequest.attachments"
                      >
                        المرفقات
                      </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body mx-2rem pt-0">
                        <hr />
                        <div class="row g-2 mb-5">
                          <div class="col-4" *ngFor="let docType of documentTypes">
                            <p class="form-label" jhiTranslate="srsaApp.yachtCharterPermit.{{ docType }}"></p>
                            <span *ngFor="let doc of documents">
                              <button type="button" *ngIf="doc.key == docType" disabled class="btn btn-light d-flex">
                                <span class="fw-bold doc-name">{{ doc.file.name }}</span>
                              </button>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mk-wizard-actions d-flex-between gap-3 mk-wizard-btns">
                <div class="d-flex-between gap-3">
                  <a
                    href="javascript:void(0)"
                    (click)="wizardPrevStep()"
                    *ngIf="(currentStep !== 1 && !this.editMode) || (currentStep !== 4 && this.editMode)"
                    class="btn btn-outline-primary mk-btn-prev"
                    jhiTranslate="global.menu.previous"
                  >
                    السابق
                  </a>
                  <a
                    href="javascript:void(0)"
                    (click)="wizardNextStep()"
                    [ngClass]="(currentStep === 1 && !showCrDetails) || isCurrentWizardInvalid() ? 'disabled' : ''"
                    *ngIf="currentStep !== steps.length"
                    class="btn btn-outline-primary mk-btn-next"
                    jhiTranslate="global.menu.next"
                  >
                    التالي
                  </a>

                  <div *ngIf="currentStep === steps.length">
                    <button
                      *ngIf="yachtCharterPermit!.requestStatus == 'DRAFT' || yachtCharterPermit!.requestStatus == null"
                      type="button"
                      data-cy="entityCreateCancelButton"
                      class="btn btn-primary"
                      data-bs-toggle="modal"
                      data-bs-target="#liabilityModal"
                    >
                      <span jhiTranslate="srsaApp.permitRequest.detail.sendRequest">إرسال الطلب</span>
                    </button>
                    <button
                      *ngIf="
                        yachtCharterPermit!.requestStatus == 'RETURNED_LICENSING_MANAGER' ||
                        yachtCharterPermit!.requestStatus == 'RETURNED_LICENSING_OFFICER' ||
                        yachtCharterPermit!.requestStatus == 'RETURNED_INSPECTION_OFFICER'
                      "
                      type="submit"
                      data-cy="entityCreateCancelButton"
                      class="btn btn-primary"
                      (click)="prepareRequestStatus('RETURNED')"
                      jhiTranslate="srsaApp.permitRequest.detail.resendRequest"
                    >
                      إعادة إرسال الطلب
                    </button>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal الشروط والاحكام -->
<div
  class="modal fade"
  id="exampleModal"
  tabindex="-1"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="d-flex my-3">
          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />
          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="global.termsAndConditions.reviewTermsAndConditions">
            الرجاء الاطلاع والموافقة على الشروط والاحكام
          </p>
        </div>
        <div class="card-new-popUp">
          <div class="card-body">
            <jhi-terms-conditions [type]="'yacht_charter_permit'"></jhi-terms-conditions>

            <div>
              <div class="check-form d-flex my-3 gap-3">
                <input
                  class="form-check-input"
                  style="margin-top: 0.1em"
                  type="checkbox"
                  [checked]="disclaimerChecked"
                  (change)="disclaimerChecked = !disclaimerChecked"
                  id="disclaimer1"
                />
                <label class="fs-5" for="disclaimer1" jhiTranslate="srsaApp.permitRequest.termsAndConditions"
                  >اوافق واقر على الشروط والاحكام وسياسات الهيئة السعودية للبحر الاحمر</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          id="cancel-save1"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!disclaimerChecked"
          (click)="showWizard()"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          jhiTranslate="global.termsAndConditions.accept"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="liabilityModal"
  tabindex="-1"
  aria-labelledby="liabilityModalLabel"
  aria-hidden="true"
  style="--bs-modal-width: 780px"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="d-flex my-3">
          <img src="../../../../content/images/T&C.svg" alt="T&C.svg" />
          <p class="figmaTC ms-2" style="margin-bottom: 0rem" jhiTranslate="srsaApp.permitRequest.detail.obligations">
            الرجاء الاطلاع والموافقة على الضوابط والالتزامات
          </p>
        </div>
        <div class="card-new-popUp">
          <div class="card-body">
            <jhi-controls-obligations [type]="'technical_permit'"></jhi-controls-obligations>
            <div>
              <div class="check-form d-flex my-3 gap-3">
                <input
                  class="form-check-input"
                  style="margin-top: 0.1em"
                  type="checkbox"
                  [checked]="liabilityChecked"
                  (change)="liabilityChecked = !liabilityChecked"
                  id="disclaimer2"
                />
                <label class="fs-5" for="disclaimer2" jhiTranslate="srsaApp.permitRequest.termsAndConditions"
                  >اوافق واقر على الشروط والاحكام وسياسات الهيئة السعودية للبحر الاحمر</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-4">
        <button
          type="button"
          id="cancel-save2"
          data-cy="entityCreateCancelButton"
          class="btn btn-primary ms-3"
          [disabled]="!liabilityChecked"
          (click)="prepareRequestStatus('PENDING_REVIEW'); save()"
          style="width: 8rem; cursor: pointer"
          data-bs-dismiss="modal"
          data-bs-target="successModal"
          jhiTranslate="global.termsAndConditions.accept"
        >
          قبول
        </button>
        <button type="button" class="btn" data-bs-dismiss="modal" style="width: 8rem" jhiTranslate="entity.action.cancel">إلغاء</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="successModal"
  #successModal
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="successModal"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered mk-modal-fix">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <div>
          <img src="../../../../content/images/checked.svg" alt="checked" />
        </div>
        @if (yachtCharterPermit?.id) {
          <p class="sendSubmit mt-4">{{ 'srsaApp.mta.updated' | translate }} {{ permitNumber }}</p>
        } @else {
          <p class="sendSubmit mt-4">{{ 'srsaApp.mta.created' | translate }} {{ permitNumber }}</p>
        }

        <p class="sendSubmitSub mx-2rem">
          {{ 'srsaApp.mta.createdNumber' | translate }}
        </p>
        <button
          type="button"
          class="btn btn-primary"
          data-bs-dismiss="modal"
          (click)="previousState()"
          jhiTranslate="srsaApp.permitRequest.detail.myRequest"
        >
          اذهب الى طلباتي
        </button>
      </div>
    </div>
  </div>
</div>

<div
  class="modal fade"
  id="saveAsDraft"
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="saveAsDraft"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <div>
          <img src="../../../../content/images/checked.svg" alt="checked" />
        </div>
        <p class="sendSubmit mt-4">
          {{ 'srsaApp.mta.drafted' | translate }}
        </p>
        <p class="sendSubmitSub mx-2rem">
          {{ 'srsaApp.mta.draftedDetails' | translate }}
        </p>
        <button
          (click)="previousState()"
          type="button"
          class="btn btn-primary"
          data-bs-dismiss="modal"
          jhiTranslate="srsaApp.permitRequest.detail.myRequest"
        >
          اذهب الى طلباتي
        </button>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="deleteMTA"
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="deleteMTA"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <p class="sendSubmit mt-4">
          {{ 'srsaApp.mta.deleteAsk' | translate }}
        </p>
        <p class="sendSubmitSub mx-2rem">
          {{ 'srsaApp.mta.noComeBackConfirm' | translate }}
        </p>
        <button type="button" class="btn btn-outline-primary px-5" jhiTranslate="srsaApp.permitRequest.no" data-bs-dismiss="modal"></button>
        <button
          type="button"
          jhiTranslate="srsaApp.permitRequest.yes"
          class="btn btn-primary px-5 mx-2"
          data-bs-toggle="modal"
          (click)="previousState()"
        >
          نعم
        </button>
      </div>
    </div>
  </div>
</div>
