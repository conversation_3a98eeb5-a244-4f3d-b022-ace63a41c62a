package com.elm.srsa.tga.rest.client.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TgaBaseResponse<T> {

    private boolean hasData;
    private String message;
    private String details;
    private String exText;
    private int statusCode;
    private String statusText;
    private T data;

    @JsonProperty("isSuccess")
    private boolean success;

    @JsonProperty("isFailed")
    private boolean failed;
}
