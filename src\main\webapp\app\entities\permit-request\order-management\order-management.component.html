<div class="logo-Ebhar-sub min-vh-100">
  <!--  <div *ngIf="fail" class="card card-body mx-3 mb-2" style="height: 2rem;border: 4px solid darkred">-->
  <!--    <div class="d-flex justify-content-between mx-2 mt-n2">-->
  <!--      <h6>حدث خطأ ! الرجاء المحاولة لاحقاً</h6>-->
  <!--      <span class="mt-n1" style="cursor: pointer" (click)="fail=false">X</span>-->
  <!--    </div>-->
  <!--  </div>-->
  <div class="card mx-3 mb-2 p-3">
    <div class="card-body p-2">
      <div class="d-flex-between">
        <div class="d-flex align-items-baseline">
          <img src="../../../../content/images/4squre.svg" alt="4squre" />
          <nav style="--bs-breadcrumb-divider: '>'" aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item breadcrumbStep1" jhiTranslate="srsaApp.permitRequest.rqMng">إدارة الطلبات</li>
              <li class="breadcrumb-item breadcrumbStep2" aria-current="page" jhiTranslate="srsaApp.permitRequest.licenseNumber">
                طلبات جديدة
              </li>
              {{
                permitRequest!.requestNumber
              }}
              <li class="breadcrumb-item breadcrumbStep2" aria-current="page" jhiTranslate="srsaApp.permitRequest.rqDetails">
                رقم طلب {{ permitRequest!.requestNumber }}
              </li>
            </ol>
          </nav>
        </div>
        <span *jhiHasAnyAuthority="'ROLE_SERVICE_PROVIDER'" style="display: inline">
          <span *ngIf="hasPaymentRequest" class="d-flex">
            <div *ngIf="loading" class="loader"></div>
            <button
              *ngIf="permitRequest?.requestStatus == 'COMPLETED'"
              type="button"
              [disabled]="loading"
              (click)="printInvoicePdf()"
              class="btn btn-primary"
            >
              <fa-icon icon="print"></fa-icon>&nbsp;<span jhiTranslate="srsaApp.payment.printInvoice.label">طباعة الفاتورة</span>
            </button>
          </span>
          <span *ngIf="!hasPaymentRequest" class="d-flex">
            <div *ngIf="isLoading" class="loader"></div>
            <button
              *ngIf="permitRequest?.requestStatus == 'PENDING_PAYMENT'"
              type="button"
              class="btn btn-primary"
              [disabled]="isLoading || success"
              (click)="showDialog()"
            >
              <span jhiTranslate="srsaApp.payment.label">دفع</span>
            </button>
          </span>
        </span>
        <button
          *ngIf="permitRequest!.requestStatus == 'PENDING_REVIEW' && availableActions.includes('assignToLicensingOfficer')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#licenceOfficerModal"
        >
          <fa-icon icon="plus"></fa-icon>
          <span jhiTranslate="srsaApp.permitRequest.esnad">إسناد</span>
        </button>
        <button
          *ngIf="permitRequest!.requestStatus == 'UNDER_PROCESS' && availableActions.includes('unAssign')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#cancelLicenceOfficerModal"
        >
          <span jhiTranslate="srsaApp.permitRequest.cancelEsnad">إلغاء الإسناد</span>
        </button>
        <button
          *ngIf="permitRequest!.requestStatus == 'PENDING_INSPECTION' && availableActions.includes('assignToInspectionOfficer')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#inspectionOfficerModal"
        >
          <fa-icon icon="plus"></fa-icon>
          <span jhiTranslate="srsaApp.permitRequest.esnad">إسناد</span>
        </button>
        <button
          *ngIf="permitRequest!.requestStatus == 'UNDER_INSPECTION' && availableActions.includes('unAssign')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#cancelInspectionOfficerModal"
        >
          <span jhiTranslate="srsaApp.permitRequest.cancelEsnad">إلغاء الإسناد</span>
        </button>
      </div>

      <hr class="customMT0" />

      <jhi-alert-error></jhi-alert-error>

      <jhi-alert></jhi-alert>

      <div>
        <div class="row">
          <div class="col-lg-3">
            <div class="card-white p-1 shadow mb-4 mb-lg-0">
              <div class="card-body p-2">
                <p class="font-styel-header" jhiTranslate="srsaApp.permitRequest.rqPhase">مراحل سير الطلب</p>
                <hr />
                <div class="d-flex align-items-start">
                  <img *ngIf="permitRequestStatus?.length === 3" src="../../../../content/images/steps/2.png" alt="GroupSteps" />
                  <img *ngIf="permitRequestStatus?.length === 4" src="../../../../content/images/steps/3.png" alt="GroupSteps" />
                  <img *ngIf="permitRequestStatus?.length === 5" src="../../../../content/images/steps/4.png" alt="GroupSteps" />
                  <img *ngIf="permitRequestStatus?.length === 6" src="../../../../content/images/steps/5.png" alt="GroupSteps" />
                  <img *ngIf="permitRequestStatus?.length === 7" src="../../../../content/images/steps/6.png" alt="GroupSteps" />
                  <img *ngIf="permitRequestStatus?.length === 8" src="../../../../content/images/steps/7.png" alt="GroupSteps" />
                  <img
                    *ngIf="permitRequestStatus?.length === 9"
                    src="../../../../content/images/steps/8.png"
                    onerror="this.onerror=null; this.src='../../../../content/images/steps/7.png';"
                    alt="GroupSteps"
                  />

                  <ul class="mk-steps-list">
                    @for (menuItem of permitRequestStatus; track menuItem) {
                      <li>
                        <div class="d-flex gap-2 align-items-center">
                          <img
                            *ngIf="
                              menuItem!.requestStatus == 'DRAFT' ||
                              menuItem!.requestStatus == 'RETURNED_LICENSING_OFFICER' ||
                              menuItem!.requestStatus == 'RETURNED_LICENSING_MANAGER' ||
                              menuItem!.requestStatus == 'RETURNED_INSPECTION_OFFICER'
                            "
                            src="../../../../content/images/plus.png"
                          />
                          <img
                            *ngIf="
                              menuItem!.requestStatus == 'PENDING_REVIEW' ||
                              menuItem!.requestStatus == 'PENDING_REPORT_APPROVAL' ||
                              menuItem!.requestStatus == 'PENDING_PAYMENT' ||
                              menuItem!.requestStatus == 'REJECTED' ||
                              menuItem!.requestStatus == 'COMPLETED' ||
                              menuItem!.requestStatus == 'UNDER_PROCESS'
                            "
                            src="../../../../content/images/wallet.png"
                          />
                          <img
                            *ngIf="
                              menuItem!.requestStatus == 'PENDING_INSPECTION' ||
                              menuItem!.requestStatus == 'PENDING_MANAGEMENT_LICENSING' ||
                              menuItem!.requestStatus == 'UNDER_INSPECTION' ||
                              menuItem!.requestStatus == 'PENDING_VISIT'
                            "
                            src="../../../../content/images/search.png"
                          />
                          <span class="fw-bold" [jhiTranslate]="'srsaApp.StatusType.' + (menuItem!.requestStatus ?? 'null')"></span>
                        </div>
                        <div *ngIf="menuItem!.requestStatus == 'DRAFT'" class="d-inline-grid">
                          <div class="ps-1" [jhiTranslate]="'srsaApp.DATE_OF_REQUEST'"></div>
                          <div class="ps-1">{{ menuItem.createdDate | date: 'dd/MM/yyyy HH:mm' }}</div>
                        </div>
                      </li>
                    }
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-9">
            <div class="p-1 mb-3">
              <div class="card-body p-2">
                <p class="font-styel-header" jhiTranslate="srsaApp.permitRequest.rqDetails">تفاصيل الطلب</p>
                <hr />
                <div class="table-responsive">
                  <table class="table table-responsive dga-table table-borderless mb-0">
                    <thead>
                      <tr>
                        <th jhiTranslate="srsaApp.permitRequest.licenseNumber">رقم طلب</th>
                        <th jhiTranslate="srsaApp.permitRequest.rqType">نوع الطلب</th>
                        <th jhiTranslate="srsaApp.permitRequest.crName">اسم المنشأة</th>
                        <th jhiTranslate="srsaApp.permitRequest.rqStatus">حالة الطلب</th>
                        @if (permitRequest?.requestStatus != 'COMPLETED' && permitRequest?.requestStatus != 'REJECTED') {
                          <th *ngIf="permitRequest?.assignedEmployeeRole" jhiTranslate="srsaApp.permitRequest.asignedTo">مسندة إلى</th>
                          <th *ngIf="permitRequest?.assignedEmployeeRole" jhiTranslate="srsaApp.permitRequest.attributionStatus">
                            حالة الإسناد
                          </th>
                          <th
                            *jhiHasAnyAuthority="[
                              'ROLE_SRSA_INSPECTION_OFFICER',
                              'ROLE_SRSA_INSPECTION_MANAGER',
                              'ROLE_SRSA_INSPECTION_SUPERVISOR'
                            ]"
                            jhiTranslate="srsaApp.permitRequest.visitStatus"
                          >
                            حالة الزيارة
                          </th>
                        }
                        <th *ngIf="permitRequest?.submitDate" jhiTranslate="srsaApp.permitRequest.rqDate">تاريخ الطلب</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>{{ permitRequest!.requestNumber }}</td>
                        <td>{{ isArabic() ? permitRequest!.serviceType!.nameAr : permitRequest!.serviceType!.nameEn }}</td>
                        <td>{{ permitRequest!.company!.crName }}</td>
                        <td>
                          <img src="../../../../content/images/underProcess.svg" alt="underProcess" />
                          <span
                            class="ellipse lightStyle"
                            [jhiTranslate]="'srsaApp.StatusType.' + (permitRequest!.requestStatus ?? 'null')"
                          >
                            {{
                              {
                                null: '',
                                DRAFT: 'DRAFT',
                                PENDING_REVIEW: 'PENDING_REVIEW',
                                UNDER_PROCESS: 'UNDER_PROCESS',
                                PENDING_INSPECTION: 'PENDING_INSPECTION',
                                UNDER_INSPECTION: 'UNDER_INSPECTION',
                                PENDING_VISIT: 'PENDING_VISIT',
                                PENDING_REPORT_APPROVAL: 'PENDING_REPORT_APPROVAL',
                                PENDING_MANAGEMENT_LICENSING: 'PENDING_MANAGEMENT_LICENSING',
                                RETURNED_LICENSING_OFFICER: 'RETURNED_LICENSING_OFFICER',
                                RETURNED_LICENSING_MANAGER: 'RETURNED_LICENSING_MANAGER',
                                RETURNED_INSPECTION_OFFICER: 'RETURNED_INSPECTION_OFFICER',
                                REJECTED: 'REJECTED',
                                PENDING_PAYMENT: 'PENDING_PAYMENT',
                                COMPLETED: 'COMPLETED'
                              }[permitRequest!.requestStatus ?? 'null']
                            }}</span
                          >
                        </td>
                        @if (
                          permitRequest?.requestStatus != 'COMPLETED' &&
                          permitRequest?.requestStatus != 'REJECTED' &&
                          permitRequest?.assignedEmployeeRole &&
                          permitRequest?.assignedEmployeeRole != ''
                        ) {
                          <td>
                            {{ 'userManagement.roles.' + permitRequest?.assignedEmployeeRole | translate }}
                          </td>

                          @if (
                            permitRequest?.assignedEmployeeRole == Authority.SRSA_LICENSING_SUPERVISOR ||
                            permitRequest?.assignedEmployeeRole == Authority.SRSA_INSPECTION_SUPERVISOR
                          ) {
                            <td jhiTranslate="srsaApp.permitRequest.notAssigned"></td>
                          } @else {
                            <td jhiTranslate="srsaApp.permitRequest.assigned"></td>
                          }
                          <td
                            *jhiHasAnyAuthority="[
                              'ROLE_SRSA_INSPECTION_OFFICER',
                              'ROLE_SRSA_INSPECTION_MANAGER',
                              'ROLE_SRSA_INSPECTION_SUPERVISOR'
                            ]"
                            class="
{{
                              allPermitVisits[allPermitVisits.length - 1]
                                ? allPermitVisits[allPermitVisits.length - 1].visitStatus === 'COMPLETED'
                                  ? 'text-success'
                                  : 'text-dark'
                                : 'text-danger'
                            }}"
                            [jhiTranslate]="
                              'srsaApp.permitRequest.statusVisit.' +
                              (allPermitVisits[allPermitVisits.length - 1]
                                ? allPermitVisits[allPermitVisits.length - 1].visitStatus
                                : 'NOT_VISITED')
                            "
                          ></td>
                        } @else {
                          <!--                          <td></td>-->
                        }
                        <td>{{ permitRequest!.submitDate | formatMediumDatetime }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <div class="card-body px-3">
                <p-tabView>
                  <p-tabPanel header="{{ 'srsaApp.permitRequest.requestPreview' | translate }}">
                    <div class="accordion mk-review-step" id="accordionExample">
                      <div class="accordion-item mb-4 overflow-hidden">
                        <h2 class="accordion-header d-flex-center">
                          <img
                            src="../../../../content/images/request-view/ph_building-bold.svg"
                            alt="ph_building-bold."
                            class="mx-4 me-3"
                          />
                          <button
                            class="accordion-button collapsed"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#collapseOne"
                            aria-expanded="false"
                            aria-controls="collapseOne"
                            jhiTranslate="srsaApp.permitRequest.establishmentData"
                          >
                            بيانات المنشأة
                          </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                          <jhi-company-details-tables [crNumber]="permitRequest?.company?.crNumber || ''"></jhi-company-details-tables>
                          <div class="accordion-body mx-2rem" *ngIf="permitRequest!.secondaryCompany">
                            <p
                              id="jhi-permitRq4"
                              data-cy="PermitRequestCreateUpdateHeading"
                              class="accordion-subtitle"
                              jhiTranslate="srsaApp.company.subCR"
                            >
                              subCR
                            </p>
                            <hr />
                            <div class="row g-2 mb-2">
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.establishmentName">CR</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.crName }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.crEntityNumber">
                                <label class="form-label" jhiTranslate="srsaApp.company.cr700Number">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.crEntityNumber }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.cR">الرمز البريدي</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.crNumber }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.crIssueDate">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.issueDate">رقم المبني</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.crIssueDate }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.crExpiryDate">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.expiryDate">المدينة</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.crExpiryDate }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.companyStatus">
                                <label class="form-label" jhiTranslate="srsaApp.company.detail.crStatus">حالة السجل التجاري</label>
                                <p
                                  class="fw-bold"
                                  [ngClass]="{ class_one: step1 === true, class_two: step2 === true, class_three: step3 === true }"
                                >
                                  {{ permitRequest!.secondaryCompany!.companyStatus }}
                                </p>
                              </div>
                            </div>
                            <div class="row g-2 mb-2">
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.moiNumber">
                                <label class="form-label" jhiTranslate="srsaApp.company.moiNumber">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.moiNumber }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.businessType?.name">
                                <label class="form-label" jhiTranslate="srsaApp.company.entity">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.businessType!.name }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.mobileNumber">
                                <label class="form-label" jhiTranslate="srsaApp.company.mobileNumber">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.mobileNumber }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.secondaryCompany!.crActivities">
                                <label class="form-label" jhiTranslate="srsaApp.company.activityName">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.secondaryCompany!.crActivities }}</p>
                              </div>
                            </div>
                          </div>
                          <div class="accordion-body mx-2rem" *ngIf="permitRequest!.licenseProvider">
                            <p
                              id="jhi-permitRq4"
                              data-cy="PermitRequestCreateUpdateHeading"
                              class="accordion-subtitle"
                              jhiTranslate="srsaApp.permitRequest.investmentLicense"
                            >
                              العنوان الوطني
                            </p>
                            <hr />
                            <div class="row g-2 mb-2">
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.investmentType">CR</label>
                                <p class="fw-bold">{{ permitRequest!.licenseProvider!.investmentType!.nameAr }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensingAuthority">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.company!.crName }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseProvider?.code == 'ECZA'">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.id">الرمز البريدي</label>
                                <p class="fw-bold">{{ permitRequest!.company!.crNumber }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseProvider?.code == 'MISA'">
                                <label class="form-label" jhiTranslate="srsaApp.permitRequest.id">الرمز البريدي</label>
                                <p class="fw-bold">{{ permitRequest!.company!.crNumber }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                        <h2 class="accordion-header d-flex-center">
                          <img src="../../../../content/images/request-view/akar-icons_location.svg" alt="Group.png" class="mx-4 me-3" />
                          <button
                            class="accordion-button collapsed"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#collapseTwo"
                            aria-expanded="false"
                            aria-controls="collapseTwo"
                            jhiTranslate="srsaApp.nationalAddress.detail.nationalAddressData"
                          >
                            بيانات العنوان الوطني
                          </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                          <div class="accordion-body mx-2rem">
                            <p
                              id="jhi-permitRq4"
                              data-cy="PermitRequestCreateUpdateHeading"
                              class="accordion-subtitle"
                              jhiTranslate="srsaApp.nationalAddress.detail.title"
                            >
                              العنوان الوطني
                            </p>
                            <hr />
                            <div class="row g-2 mb-2">
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.street">الشارع</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.streetName! }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.district">الحي</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.district! }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.zipCode">الرمز البريدي</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.zipcode! }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.buildingNumber">رقم المبني</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.buildingNumber! }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.city">المدينة</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.city! }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.additionalNumber">الرقم الإضافي</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.additionalNumber! }}</p>
                              </div>
                              <div class="col-md-4 col-lg-3 col-xl-2">
                                <label class="form-label" jhiTranslate="srsaApp.nationalAddress.shortNumber">الرقم المختصر</label>
                                <p class="fw-bold">{{ permitRequest!.company!.nationalAddress!.additionalNumber! }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                        <h2 class="accordion-header d-flex-center">
                          <img
                            src="../../../../content/images/request-view/fluent_card-ui-24-regular.svg"
                            alt="fluent_card-ui-24-regular.svg"
                            class="mx-4 me-3"
                          />
                          <button
                            class="accordion-button collapsed"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#collapseThree"
                            aria-expanded="false"
                            aria-controls="collapseThree"
                            jhiTranslate="srsaApp.permitRequest.permitData"
                          >
                            بيانات الرخصة
                          </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                          <div class="accordion-body mx-2rem">
                            <div
                              *ngIf="
                                permitRequest!.licenseRequest!.licenseRequestMaritimeMediums &&
                                permitRequest!.licenseRequest!.licenseRequestMaritimeMediums!.length > 0
                              "
                            >
                              <p
                                id="jhi-permitRq4"
                                data-cy="PermitRequestCreateUpdateHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.maritimeMediation"
                              >
                                الواسطات البحرية
                              </p>
                              <hr />
                              <div class="row g-4 mb-5" [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestMaritimeMediums }">
                                @for (
                                  maritimeMedium of permitRequest!.licenseRequest!.licenseRequestMaritimeMediums;
                                  track trackMaritimeMedium
                                ) {
                                  <div
                                    class="col-md-6 col-lg-4"
                                    *ngIf="
                                      ((maritimeMedium!.maritimeMedium!.code == 'CruiseShips' ||
                                        maritimeMedium!.maritimeMedium!.code == 'Yachts') &&
                                        permitRequest!.serviceType!!.code == 'MA') ||
                                      (maritimeMedium!.maritimeMedium!.code != 'CruiseShips' && permitRequest!.serviceType!!.code == 'MO')
                                    "
                                  >
                                    <input
                                      required
                                      type="checkbox"
                                      class="btn-check"
                                      id="btn-check-{{ maritimeMedium!.maritimeMedium!.id }}"
                                      autocomplete="off"
                                      [value]="maritimeMedium!.maritimeMedium!.id"
                                      [checked]="true"
                                      disabled
                                    />
                                    <label
                                      class="align-items-center btn d-flex flex-row fs-5 gap-3 new-custom py-4 text-primary background-selected"
                                      for="btn-check-{{ maritimeMedium!.maritimeMedium!.id }}"
                                    >
                                      <img
                                        src="../../../../content/images/{{ maritimeMedium!.maritimeMedium!.imageName }}"
                                        alt="{{ maritimeMedium!.maritimeMedium!.imageName }}"
                                      />
                                      <span class="fw-bold text-start">{{
                                        isArabic() ? maritimeMedium!.maritimeMedium!.nameAr : maritimeMedium!.maritimeMedium!.nameEn
                                      }}</span>
                                    </label>
                                  </div>
                                }
                              </div>

                              <div>
                                <p
                                  id="jhi-permitRq4"
                                  data-cy="PermitRequestCreateUpdateHeading"
                                  class="accordion-subtitle"
                                  jhiTranslate="srsaApp.permitRequest.servicesList"
                                  *ngIf="
                                    permitRequest!.licenseRequest!.licenseRequestMaritimeServices &&
                                    permitRequest!.licenseRequest!.licenseRequestMaritimeServices.length > 0
                                  "
                                >
                                  قائمة الخدمات
                                </p>
                                <hr />
                                <div class="bd-example tooltip-demo mb-5">
                                  <div class="bd-example-tooltips">
                                    <div
                                      class="form-check form-check-inline"
                                      [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestMaritimeServices }"
                                      *ngFor="
                                        let selectedMaritimeTourismService of permitRequest!.licenseRequest!.licenseRequestMaritimeServices
                                      "
                                    >
                                      <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="" disabled checked />
                                      <label class="form-check-label pt-1 ps-2" for="inlineCheckbox1">
                                        {{
                                          isArabic()
                                            ? selectedMaritimeTourismService!.maritimeTourismServices!.nameAr
                                            : selectedMaritimeTourismService!.maritimeTourismServices!.nameEn
                                        }}</label
                                      >
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Beach Activities Section -->
                            <div>
                              <p
                                id="jhi-permitRq-beach-activities"
                                data-cy="PermitRequestBeachActivitiesHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.beachActivities"
                                *ngIf="
                                  permitRequest!.licenseRequest!.beachActivities &&
                                  permitRequest!.licenseRequest!.beachActivities.length > 0
                                "
                              >
                                قائمة أنشطة الشاطئ
                              </p>
                              <hr
                                *ngIf="
                                  permitRequest!.licenseRequest!.beachActivities &&
                                  permitRequest!.licenseRequest!.beachActivities.length > 0
                                "
                              />
                              <div
                                class="bd-example tooltip-demo mb-5"
                                *ngIf="
                                  permitRequest!.licenseRequest!.beachActivities &&
                                  permitRequest!.licenseRequest!.beachActivities.length > 0
                                "
                              >
                                <div class="bd-example-tooltips">
                                  <div
                                    class="form-check form-check-inline"
                                    [ngClass]="{ newChangesInput: permitChanges?.BeachActivities }"
                                    *ngFor="let selectedBeachActivity of permitRequest!.licenseRequest!.beachActivities"
                                  >
                                    <input
                                      class="form-check-input"
                                      type="checkbox"
                                      id="beachActivity_{{ selectedBeachActivity.id }}"
                                      value=""
                                      disabled
                                      checked
                                    />
                                    <label class="form-check-label pt-1 ps-2" for="beachActivity_{{ selectedBeachActivity.id }}">
                                      {{ isArabic() ? selectedBeachActivity!.nameAr : selectedBeachActivity!.nameEn }}
                                    </label>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Beach Equipment Section -->
                            <div>
                              <p
                                id="jhi-permitRq-beach-equipment"
                                data-cy="PermitRequestBeachEquipmentHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.beachEquipment"
                                *ngIf="
                                  permitRequest!.licenseRequest!.beachEquipment && permitRequest!.licenseRequest!.beachEquipment.length > 0
                                "
                              >
                                قائمة معدات الشاطئ
                              </p>
                              <hr
                                *ngIf="
                                  permitRequest!.licenseRequest!.beachEquipment && permitRequest!.licenseRequest!.beachEquipment.length > 0
                                "
                              />
                              <div
                                class="bd-example tooltip-demo mb-5"
                                *ngIf="
                                  permitRequest!.licenseRequest!.beachEquipment && permitRequest!.licenseRequest!.beachEquipment.length > 0
                                "
                              >
                                <div class="bd-example-tooltips">
                                  <div
                                    class="form-check form-check-inline"
                                    [ngClass]="{ newChangesInput: permitChanges?.BeachEquipment }"
                                    *ngFor="let selectedBeachEquipment of permitRequest!.licenseRequest!.beachEquipment"
                                  >
                                    <input
                                      class="form-check-input"
                                      type="checkbox"
                                      id="beachEquipment_{{ selectedBeachEquipment.id }}"
                                      value=""
                                      disabled
                                      checked
                                    />
                                    <label class="form-check-label pt-1 ps-2" for="beachEquipment_{{ selectedBeachEquipment.id }}">
                                      {{ isArabic() ? selectedBeachEquipment!.nameAr : selectedBeachEquipment!.nameEn }}
                                    </label>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div>
                              <p
                                id="jhi-permitRq4"
                                data-cy="PermitRequestCreateUpdateHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.permitData"
                              >
                                بيانات الرخصة
                              </p>
                              <hr />
                              <div class="row g-2 mb-2">
                                <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="permitRequest!.licenseRequest!.requestedLicenseDuration">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.licensesDuration"
                                    >مدة الترخيص المطلوبة</label
                                  >
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.RequestedLicenseDuration }">
                                    {{
                                      'srsaApp.permitRequest.licenseDuration.' + permitRequest!.licenseRequest!.requestedLicenseDuration
                                        | translate
                                    }}
                                  </p>
                                </div>
                                <div
                                  class="col-md-4 col-lg-3 col-xl-2"
                                  *ngIf="permitRequest!.licenseRequest!.licenseRequestProvidedLanguages"
                                >
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.providedLanguages">الرمز البريدي</label>
                                  <div
                                    [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestProvidedLanguages }"
                                    *ngFor="let language of permitRequest!.licenseRequest!.licenseRequestProvidedLanguages; let x = index"
                                  >
                                    <p class="fw-bold">
                                      {{ isArabic() ? language!.providedLanguages!.nameAr : language!.providedLanguages!.nameEn }}
                                    </p>
                                  </div>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseRequest!.noOfYearsExperience">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.yearsOfExperience">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfYearsExperience }">
                                    {{ permitRequest!.licenseRequest!.noOfYearsExperience }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.totalFundingOfProject">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.totalFundingOfProject">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.TotalFundingOfProject }">
                                    {{ permitRequest!.totalFundingOfProject }}
                                  </p>
                                </div>

                                <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="permitRequest!.serviceType!.code === 'BO'">
                                  <label
                                    class="form-label"
                                    jhiTranslate="srsaApp.permitRequest.previouslyOperatedBeachInsideOrOutsideSaudiArabia"
                                    >هل سبق وان تم تشغيل شاطئ قبل ذلك داخل او خارج المملكة العربية السعودية</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.PreviouslyOperatedBeach }">
                                    <span
                                      *ngIf="permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia === true"
                                      jhiTranslate="global.form.yes"
                                      >نعم</span
                                    >
                                    <span
                                      *ngIf="permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia === false"
                                      jhiTranslate="global.form.no"
                                      >لا</span
                                    >
                                  </p>
                                </div>

                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseRequest!.licenseRequestCountries">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.operationCountries">رقم المبني</label>
                                  <div
                                    [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestCountries }"
                                    *ngFor="let country of permitRequest!.licenseRequest!.licenseRequestCountries; let x = index"
                                  >
                                    <p class="fw-bold">
                                      {{ isArabic() ? country!.countryOfWork!.nameAr : country!.countryOfWork!.nameEn }}
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div class="row g-2 mb-2">
                                <div
                                  class="col-md-4 col-lg-3 col-xl-3"
                                  *ngIf="permitRequest!.licenseRequest!.noOfManagedMaritimeMediumsGeneral"
                                >
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfManagedMaritimeMediumsGeneral"
                                    >الرقم المختصر</label
                                  >
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfManagedMaritimeMediumsGeneral }">
                                    {{ permitRequest!.licenseRequest!.noOfManagedMaritimeMediumsGeneral }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseRequest!.noOfCountryCompanyOpr">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfCountryCompanyOpr">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfCountryCompanyOpr }">
                                    {{ permitRequest!.licenseRequest!.noOfCountryCompanyOpr }}
                                  </p>
                                </div>
                                <div
                                  class="col-md-4 col-lg-3 col-xl-2"
                                  *ngIf="permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsWorld"
                                >
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfOwnedMaritimeMediumsWorld"
                                    >المدينة</label
                                  >
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfOwnedMaritimeMediumsWorld }">
                                    {{ permitRequest!.licenseRequest!.noOfOwnedMaritimeMediumsWorld }}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="accordion-item mb-4 rounded-4 overflow-hidden">
                        <h2 class="accordion-header d-flex-center">
                          <img
                            src="../../../../content/images/request-view/ic_round-add-card.svg"
                            alt="ic_round-add-card.svg"
                            class="mx-4 me-3"
                          />
                          <button
                            class="accordion-button collapsed"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#collapseFour"
                            aria-expanded="false"
                            aria-controls="collapseFour"
                            jhiTranslate="srsaApp.permitRequest.extraData"
                          >
                            بيانات إضافية
                          </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                          <div class="accordion-body mx-2rem">
                            <div>
                              <p
                                id="jhi-permitRq4"
                                data-cy="PermitRequestCreateUpdateHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.workforceData"
                              >
                                بيانات القوى العاملة
                              </p>
                              <hr />
                              <div class="row g-2 mb-2">
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.ministryOfHumanResourcesNumber">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.ministryOfHumanResourcesNumber">CR</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.MinistryOfHumanResourcesNumber }">
                                    {{ permitRequest!.ministryOfHumanResourcesNumber }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.saudiLaborCount">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.saudiLaborCount">الحي</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.SaudiLaborCount }">
                                    {{ permitRequest!.saudiLaborCount }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.foreignersLaborCount">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.foreignersLaborCount">الرمز البريدي</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.ForeignersLaborCount }">
                                    {{ permitRequest!.foreignersLaborCount }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.numberOfMaleEmployees">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfMaleEmployees">رقم المبني</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfMaleEmployees }">
                                    {{ permitRequest!.numberOfMaleEmployees }}
                                  </p>
                                </div>
                              </div>
                              <div class="row g-2 mb-2">
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.numberOfFemaleEmployees">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfFemaleEmployees">المدينة</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfFemaleEmployees }">
                                    {{ permitRequest!.numberOfFemaleEmployees }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.saudizationLevel">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.saudizationLevel">الرقم الإضافي</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.SaudizationLevel }">
                                    {{ 'srsaApp.permitRequest.saudiLevel.' + permitRequest!.saudizationLevel | translate }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.rateRange">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.rateRange">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.RateRange }">
                                    {{ permitRequest!.rateRange }}
                                  </p>
                                </div>
                              </div>
                              <div>
                                <p
                                  id="jhi-permitRq4"
                                  data-cy="PermitRequestCreateUpdateHeading"
                                  class="accordion-subtitle"
                                  jhiTranslate="srsaApp.permitRequest.contactInformation"
                                >
                                  العنوان الوطني
                                </p>
                                <hr />
                                <div class="row g-2 mb-2">
                                  <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.contactOfficerName">
                                    <label class="form-label" jhiTranslate="srsaApp.permitRequest.contactOfficerName">الرقم المختصر</label>
                                    <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.ContactOfficerName }">
                                      {{ permitRequest!.contactOfficerName }}
                                    </p>
                                  </div>
                                  <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.contactOfficerMobileNumber">
                                    <label class="form-label" jhiTranslate="srsaApp.permitRequest.contactOfficerMobileNumber"
                                      >الرقم المختصر</label
                                    >
                                    <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.ContactOfficerMobileNumber }">
                                      {{ permitRequest!.contactOfficerMobileNumber }}
                                    </p>
                                  </div>
                                  <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.contactOfficerEmailAddress">
                                    <label class="form-label" jhiTranslate="srsaApp.permitRequest.contactOfficerEmailAddress"
                                      >الرقم المختصر</label
                                    >
                                    <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.ContactOfficerEmailAddress }">
                                      {{ permitRequest!.contactOfficerEmailAddress }}
                                    </p>
                                  </div>
                                  <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.vatRegistrationNumber">
                                    <label class="form-label" jhiTranslate="srsaApp.permitRequest.vatRegistrationNumber"
                                      >الرقم المختصر</label
                                    >
                                    <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.VatRegistrationNumber }">
                                      {{ permitRequest!.vatRegistrationNumber }}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div *ngIf="permitRequest!.serviceType!.code == 'MO'">
                              <p
                                id="jhi-permitRq4"
                                data-cy="PermitRequestCreateUpdateHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.extraData"
                              >
                                معلومات إضافية
                              </p>
                              <hr />
                              <div class="row g-2 mb-2">
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaOwnerName">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.MarinaOwnerNameAr }">
                                    {{ permitRequest!.licenseRequest!.marinaOwnerNameAr }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaCrNumber">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.MarinaCrNumber }">
                                    {{ permitRequest!.licenseRequest!.marinaCrNumber }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaCrNameAr">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.MarinaCrNameAr }">
                                    {{ permitRequest!.licenseRequest!.marinaCrNameAr }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.marinaCrNameEn">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.MarinaCrNameEn }">
                                    {{ permitRequest!.licenseRequest!.marinaCrNameEn }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfDocks">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfDocks }">
                                    {{ permitRequest!.licenseRequest!.numberOfDocks }}
                                  </p>
                                </div>
                                <div class="row" *ngIf="permitRequest!.licenseRequest!.licenseRequestDocks!.length > 0">
                                  <div class="table-responsive" [ngClass]="{ newChangesInput: permitChanges?.LicenseRequestDocks }">
                                    <table class="table table-striped dga-table" aria-describedby="endpointsRequestsMetrics">
                                      <thead>
                                        <tr>
                                          <th scope="col" class="text-center" jhiTranslate="srsaApp.permitRequest.dockLength"></th>
                                          <th scope="col" class="text-center" jhiTranslate="srsaApp.permitRequest.dockDepth"></th>
                                          <th scope="col" class="text-center" jhiTranslate="srsaApp.permitRequest.dockWidth"></th>
                                          <th scope="col" class="text-center" jhiTranslate="srsaApp.permitRequest.noOfWaterStations"></th>
                                          <th
                                            scope="col"
                                            class="text-center"
                                            jhiTranslate="srsaApp.permitRequest.noOfElectricChargingStations"
                                          ></th>
                                          <th scope="col" class="text-center" jhiTranslate="srsaApp.permitRequest.noOfWasteStations"></th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr
                                          class="text-center"
                                          *ngFor="let licenseRequestDocks of permitRequest!.licenseRequest!.licenseRequestDocks!"
                                        >
                                          <td>{{ licenseRequestDocks.docks!.dockLength }}</td>
                                          <td>{{ licenseRequestDocks.docks!.dockDepth }}</td>
                                          <td>{{ licenseRequestDocks.docks!.dockWidth }}</td>
                                          <td>{{ licenseRequestDocks.docks!.noOfWaterStations }}</td>
                                          <td>{{ licenseRequestDocks.docks!.noOfElectricChargingStations }}</td>
                                          <td>{{ licenseRequestDocks.docks!.noOfWasteStations }}</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfControlTowers">��لرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfControlTowers }">
                                    {{ permitRequest!.licenseRequest!.noOfControlTowers }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfMarinaFacilities">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfMarinaFacilities }">
                                    {{ permitRequest!.licenseRequest!.noOfMarinaFacilities }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.boatsCapacity">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.BoatsCapacity }">
                                    {{ permitRequest!.licenseRequest!.boatsCapacity }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.yachtsCapacity">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.YachtsCapacity }">
                                    {{ permitRequest!.licenseRequest!.yachtsCapacity }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.otherMaritimeMediumCapacity"
                                    >الرقم المختصر</label
                                  >
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.OtherMaritimeMediumCapacity }">
                                    {{ permitRequest!.licenseRequest!.numberOfOtherMarineVessels }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.marineTourOperatorServices"
                                    >الرقم المختصر</label
                                  >
                                  <div
                                    *ngFor="
                                      let marineTourOperatorService of permitRequest!.licenseRequest!.licenseRequestMarineTourServices;
                                      let x = index
                                    "
                                  >
                                    <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestMarineTourServices }">
                                      {{
                                        isArabic()
                                          ? marineTourOperatorService!.marineTourOperatorServices!.nameAr
                                          : marineTourOperatorService!.marineTourOperatorServices!.nameEn
                                      }}
                                    </p>
                                  </div>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseRequest!.noOfFuelStations">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfFuelStations">الرقم المختصر</label>
                                  <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.NoOfFuelStations }">
                                    {{ permitRequest!.licenseRequest!.noOfFuelStations }}
                                  </p>
                                </div>
                                <div class="col-md-4 col-lg-3 col-xl-2" *ngIf="permitRequest!.licenseRequest!.licenseRequestFuelTypes">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.fuelTypes">الرقم المختصر</label>
                                  <div *ngFor="let fuelType of permitRequest!.licenseRequest!.licenseRequestFuelTypes; let x = index">
                                    <p class="fw-bold" [ngClass]="{ newChangesLabel: permitChanges?.LicenseRequestFuelTypes }">
                                      {{ isArabic() ? fuelType!.fuelTypes!.nameAr : fuelType!.fuelTypes!.nameEn }}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div *ngIf="permitRequest!.serviceType!.code == 'BO'">
                              <p
                                id="jhi-permitRq5"
                                data-cy="PermitRequestCreateUpdateHeading"
                                class="accordion-subtitle"
                                jhiTranslate="srsaApp.permitRequest.extraData"
                              >
                                معلومات إضافية
                              </p>
                              <hr />

                              <!-- Beach Operator Information Preview -->
                              <div class="row" *ngIf="permitRequest!.serviceType!.code === 'BO'">
                                <!-- Beach Owner Name -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.beachOwnerName">اسم مالك الشاطئ</label>
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.BeachOwnerName }">
                                    {{ permitRequest?.licenseRequest?.beachOwnerName }}
                                  </p>
                                </div>

                                <!-- Beach Trade Name -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.beachTradeName">الاسم التجاري للشاطئ</label>
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.BeachTradeName }">
                                    {{ permitRequest?.licenseRequest?.beachTradeName }}
                                  </p>
                                </div>

                                <!-- Commercial Registration Number -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.beachOwnerCommercialRegistrationNumber"
                                    >رقم السجل التجاري التابع لمالك الشاطئ</label
                                  >
                                  <p
                                    class="item-value"
                                    [ngClass]="{ newChangesLabel: permitChanges?.BeachOwnerCommercialRegistrationNumber }"
                                  >
                                    {{ permitRequest?.licenseRequest?.beachOwnerCommercialRegistrationNumber }}
                                  </p>
                                </div>

                                <!-- Number of Restrooms -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfRestrooms"
                                    >كم عدد دورات المياه الموجودة</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfRestrooms }">
                                    {{ permitRequest?.licenseRequest?.numberOfRestrooms }}
                                  </p>
                                </div>

                                <!-- Number of Lifeguards -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfLifeguards"
                                    >كم عدد المنقذين الموجودة</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfLifeguards }">
                                    {{ permitRequest?.licenseRequest?.numberOfLifeguards }}
                                  </p>
                                </div>

                                <!-- Food Truck Parking -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfFoodTruckParking"
                                    >عدد مواقف الفوود تراك</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfFoodTruckParking }">
                                    {{ permitRequest?.licenseRequest?.numberOfFoodTruckParking }}
                                  </p>
                                </div>

                                <!-- Visitor Capacity -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.visitorCapacity"
                                    >عدد الطاقة الاستيعابية للزوار</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.VisitorCapacity }">
                                    {{ permitRequest?.licenseRequest?.visitorCapacity }}
                                  </p>
                                </div>

                                <!-- Number of Water Stations -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.noOfWaterStations"
                                    >كم عدد محطات المياه الموجودة</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NoOfWaterStations }">
                                    {{ permitRequest?.licenseRequest?.noOfWaterStations }}
                                  </p>
                                </div>

                                <!-- Cabins for Rent -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.isThereCabinsForRent"
                                    >هل يوجد غرف او كبائن للأيجار</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.IsThereCabinsForRent }">
                                    <span
                                      *ngIf="permitRequest?.licenseRequest?.isThereCabinsForRent === true"
                                      jhiTranslate="global.form.yes"
                                      >نعم</span
                                    >
                                    <span
                                      *ngIf="permitRequest?.licenseRequest?.isThereCabinsForRent === false"
                                      jhiTranslate="global.form.no"
                                      >لا</span
                                    >
                                  </p>
                                </div>

                                <!-- Number of Cabins (Conditional) -->
                                <div
                                  class="col-md-4 col-lg-3 col-xl-3"
                                  *ngIf="permitRequest?.licenseRequest?.isThereCabinsForRent === true"
                                >
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfCabinsAndRooms"
                                    >عدد الكبائن والغرف</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfCabinsAndRooms }">
                                    {{ permitRequest?.licenseRequest?.numberOfCabinsAndRooms }}
                                  </p>
                                </div>

                                <!-- Marine Vessel -->
                                <div class="col-md-4 col-lg-3 col-xl-3">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.isThereMarineVessel"
                                    >هل يوجد وسائط بحرية</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.IsThereMarineVessel }">
                                    <span *ngIf="permitRequest?.licenseRequest?.isThereMarineVessel === true" jhiTranslate="global.form.yes"
                                      >نعم</span
                                    >
                                    <span *ngIf="permitRequest?.licenseRequest?.isThereMarineVessel === false" jhiTranslate="global.form.no"
                                      >لا</span
                                    >
                                  </p>
                                </div>

                                <!-- Number of Marine Vessels (Conditional) -->
                                <div class="col-md-4 col-lg-3 col-xl-3" *ngIf="permitRequest?.licenseRequest?.isThereMarineVessel === true">
                                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.numberOfOtherMarineVessel"
                                    >كم عدد الوسائط البحرية</label
                                  >
                                  <p class="item-value" [ngClass]="{ newChangesLabel: permitChanges?.NumberOfOtherMarineVessel }">
                                    {{ permitRequest?.licenseRequest?.numberOfOtherMarineVessels }}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="accordion-item mb-4 rounded-4 overflow-hidden" *ngIf="documentsDisplay">
                        <h2 class="accordion-header d-flex-center">
                          <img
                            src="../../../../content/images/request-view/mdi_paper-outline.svg"
                            alt="mdi_paper-outline.svg"
                            class="mx-4 me-3"
                          />
                          <button
                            class="accordion-button collapsed"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#collapseFive"
                            aria-expanded="false"
                            aria-controls="collapseFive"
                            jhiTranslate="srsaApp.permitRequest.attachments"
                          >
                            المرفقات
                          </button>
                        </h2>
                        <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                          <div class="accordion-body mx-2rem pt-0">
                            <hr />
                            <div class="row g-2 mb-2" [ngClass]="{ newChangesInput: permitChanges?.Attachments }">
                              <div class="col-4" *ngFor="let docType of documentTypes">
                                <p
                                  *ngIf="permitRequest!.serviceType!.code !== 'BO'"
                                  class="form-label"
                                  jhiTranslate="srsaApp.permitRequest.{{ docType }}"
                                ></p>
                                <p
                                  *ngIf="permitRequest!.serviceType!.code === 'BO'"
                                  class="form-label"
                                  jhiTranslate="srsaApp.permitRequest.beachOperator.{{ docType }}"
                                ></p>
                                <span *ngFor="let doc of documentsDisplay; let x = index">
                                  <button
                                    type="button"
                                    *ngIf="doc.attachmentTypeCode == docType"
                                    (click)="getAttachment(doc!.id)"
                                    class="btn btn-light d-flex"
                                  >
                                    <span class="fw-bold doc-name">{{ doc.docName }}</span>
                                    <input
                                      type="image"
                                      id="myimage"
                                      src="../../../../content/images/fi_download.svg"
                                      class="btn btn-gray"
                                    />
                                  </button>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </p-tabPanel>
                  <p-tabPanel header="{{ 'srsaApp.permitRequest.history' | translate }}">
                    <div class="row g-3 mb-3" *ngFor="let requestStatus of permitRequestStatus; let i = index; let last = last">
                      <div class="col-md-3">
                        <div class="mb-2">
                          <div class="d-flex gap-2">
                            <img src="../../../../content/images/wallet.png" alt="wallet" />
                            <span
                              class="font-styel-header"
                              [jhiTranslate]="'srsaApp.StatusType.' + (requestStatus!.requestStatus ?? 'null')"
                            ></span>
                          </div>
                        </div>

                        <div class="d-inline-grid">
                          <span>{{ requestStatus.assignedEmployeeName }}</span>
                          <span jhiTranslate="srsaApp.permitRequest.requestConfirmDate">تاريخ تأكيد الطلب</span>
                          <span>{{ requestStatus.createdDate | date: 'dd/MM/yyyy HH:mm' }}</span>
                        </div>
                      </div>

                      <!-- Check to display notes -->
                      <div class="col-md-9" *ngIf="shouldDisplayNote(requestStatus, last)">
                        <span class="font-styel-header" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</span>
                        <article>
                          {{ requestStatus.requestNote }}
                        </article>
                      </div>
                    </div>
                  </p-tabPanel>
                </p-tabView>
              </div>
            </div>
            <div class="p-1" *ngIf="allPermitVisits.length > 0">
              <div class="card-body p-2">
                <p class="font-styel-header" jhiTranslate="srsaApp.permitRequest.visitDate">موعد الزيارة</p>
                <hr />
                <div class="table-responsive">
                  <table class="table dga-table table-borderless mb-0">
                    <thead>
                      <tr>
                        <th jhiTranslate="srsaApp.permitRequest.visitDate">موعد الزيارة</th>
                        <th jhiTranslate="srsaApp.permitRequest.attachments">المرفقات</th>
                        <th jhiTranslate="srsaApp.permitRequest.visitStatus">الحالة</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let visit of allPermitVisits; index as i">
                        <!-- for a reason , 'a' by default arabic, that's why this condition exist                       -->
                        <td>
                          {{ visit.visitDateG | date: 'dd/MM/yyyy hh:mm' }}
                          {{ isArabic() ? (visit.visitDateG | date: 'a') : (visit.visitDateG | date: 'a') === 'ص' ? 'AM' : 'PM' }}
                        </td>
                        <td>
                          <button
                            *ngIf="visit.visitStatus == 'COMPLETED'"
                            type="button"
                            class="btn btn-light"
                            (click)="getAttachmentVisit(visit.id)"
                            jhiTranslate="srsaApp.permitRequest.download"
                          >
                            <img src="../../../../content/images/editPn.svg" alt="editPn" />
                          </button>
                        </td>
                        <td>
                          <span jhiTranslate="srsaApp.permitRequest.statusVisit.{{ visit.visitStatus }}"> </span>
                          <img class="imageTable" src="../../../../content/images/check (1).svg" alt="check" />
                        </td>
                        <td>
                          <button
                            *ngIf="
                              i == allPermitVisits.length - 1 &&
                              permitRequest!.requestStatus == 'PENDING_VISIT' &&
                              completedPermitVisits.length == 0 &&
                              visit.visitStatus == 'SCHEDULED' &&
                              hasAction &&
                              availableActions.includes('scheduleVisit')
                            "
                            type="button"
                            class="btn btn-primary px-5"
                            data-bs-toggle="modal"
                            data-bs-target="#scheduleVisitModal"
                            jhiTranslate="srsaApp.permitRequest.visitReschedule"
                          >
                            إعادة جدولة زيارة ميدانية
                          </button>
                          <button
                            *ngIf="
                              i == allPermitVisits.length - 1 &&
                              hasAction &&
                              ((permitRequest!.requestStatus == 'PENDING_VISIT' && visit.visitStatus == 'SCHEDULED') ||
                                (permitRequest!.requestStatus == 'UNDER_INSPECTION' && availableActions.includes('completeVisit')))
                            "
                            type="button"
                            class="btn btn-primary px-5"
                            data-bs-toggle="modal"
                            data-bs-target="#completeVisitModal"
                            jhiTranslate="srsaApp.permitRequest.visitComplete"
                          >
                            إتمام الزيارة الميدانية
                          </button>
                          <button
                            *ngIf="i == allPermitVisits.length - 1 && returnToOrganizationAfterVisit()"
                            type="button"
                            data-bs-toggle="modal"
                            data-bs-target="#retrunPermitModal"
                            class="btn btn-outline-primary px-4"
                            jhiTranslate="srsaApp.permitRequest.returnReqToProvider"
                          >
                            اعادة الطلب الى المنشأة للاستكمال
                          </button>
                          <button
                            *ngIf="
                              i == allPermitVisits.length - 1 &&
                              permitRequest!.requestStatus == 'UNDER_INSPECTION' &&
                              completedPermitVisits.length > 0 &&
                              visit.visitStatus == 'COMPLETED' &&
                              hasAction
                            "
                            type="button"
                            class="btn btn-primary px-5"
                            data-bs-toggle="modal"
                            data-bs-target="#editCompleteVisitModal"
                            jhiTranslate="srsaApp.permitRequest.visitEdit"
                          >
                            تعديل محضر وتقرير الزيارة
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="d-flex-end mt-4 gap-4">
        <button
          *ngIf="
            (permitRequest!.requestStatus == 'UNDER_PROCESS' &&
              availableActions.includes('returnRequest') &&
              account?.login === permitRequest?.assignedEmployee?.login) ||
            (permitRequest!.requestStatus == 'PENDING_MANAGEMENT_LICENSING' && availableActions.includes('returnRequest'))
          "
          data-bs-toggle="modal"
          data-bs-target="#retrunPermitModal"
          type="button"
          class="btn btn-outline-primary px-5"
          jhiTranslate="srsaApp.permitRequest.returnRq"
        >
          إعادة الطلب
        </button>

        <button
          *ngIf="
            permitRequest!.requestStatus == 'UNDER_INSPECTION' &&
            availableActions.includes('returnRequest') &&
            completedPermitVisits.length === 0 &&
            account?.login === permitRequest?.assignedEmployee?.login
          "
          type="button"
          data-bs-toggle="modal"
          data-bs-target="#retrunPermitModal"
          class="btn btn-outline-primary px-4"
          jhiTranslate="srsaApp.permitRequest.returnReqToProvider"
        >
          اعادة الطلب الى المنشأة للاستكمال
        </button>
        <button
          *ngIf="
            permitRequest!.requestStatus == 'UNDER_INSPECTION' &&
            availableActions.includes('scheduleVisit') &&
            hasAction &&
            account?.login === permitRequest?.assignedEmployee?.login
          "
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#scheduleVisitModal"
          jhiTranslate="srsaApp.permitRequest.visitSchedule"
        >
          جدولة زيارة ميدانية
        </button>
        <button
          *ngIf="permitRequest!.requestStatus == 'PENDING_MANAGEMENT_LICENSING' && availableActions.includes('rejectRequest')"
          type="button"
          class="btn btn-danger px-5 mx-2"
          data-bs-toggle="modal"
          data-bs-target="#rejectPermitModal"
          jhiTranslate="srsaApp.permitRequest.rejectRq"
        >
          رفض الطلب
        </button>
        <button
          *ngIf="
            permitRequest!.requestStatus == 'UNDER_PROCESS' &&
            availableActions.includes('confirmRequest') &&
            account?.login === permitRequest?.assignedEmployee?.login
          "
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#licenceOfficerConfirmModal"
          jhiTranslate="srsaApp.permitRequest.confirmRq"
        >
          تأكيد الطلب
        </button>
        <button
          *ngIf="availableActions.includes('returnToInspectionOfficer')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#inspectionOfficerModal"
          jhiTranslate="srsaApp.permitRequest.returnRqOfficer"
        >
          إرجاع الطلب لموظف المعاينة
        </button>
        <button
          *ngIf="permitRequest!.requestStatus == 'PENDING_REPORT_APPROVAL' && availableActions.includes('assignToLicensingManager')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#licenceMngrModal"
        >
          <span jhiTranslate="srsaApp.permitRequest.confirmRq">تأكيد الطلب</span>
        </button>
        <button
          *ngIf="permitRequest!.requestStatus == 'PENDING_MANAGEMENT_LICENSING' && availableActions.includes('approveRequest')"
          type="button"
          class="btn btn-primary px-5"
          data-bs-toggle="modal"
          data-bs-target="#permitConfirmModal"
          jhiTranslate="srsaApp.permitRequest.TameedRq"
        >
          تعميد الطلب
        </button>
      </div>
      <div class="modal fade" id="scheduleVisitModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form [formGroup]="entityForm">
              <div class="modal-body">
                <div class="my-2">
                  <label class="head-style" jhiTranslate="srsaApp.permitRequest.visitScheduleTitle">تحديد موعد الزيارة الميدانية</label>
                  <span class="text-danger">*</span>
                  <gregorian-calendar
                    name="birthDateGregorian"
                    id="birthDateGregorian"
                    formControlName="visitDate"
                    placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                    [minDateAsToday]="true"
                    [maxDateAsToday]="false"
                  >
                  </gregorian-calendar>

                  <div class="my-2">
                    <label class="head-style" jhiTranslate="srsaApp.permitRequest.visitScheduleTime">وقت الزيارة الميدانية</label>
                    <span class="text-danger">*</span>
                    <div class="d-flex">
                      <input
                        id="field_visitTime"
                        data-cy="visitTime"
                        type="time"
                        class="form-control"
                        name="visitTime"
                        formControlName="visitTime"
                        placeholder="HH:mm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex-center mb-4">
                <button
                  type="button"
                  (click)="scheduleVisit()"
                  jhiTranslate="srsaApp.permitRequest.confirm"
                  class="btn btn-outline-primary px-5"
                  data-bs-dismiss="modal"
                >
                  تأكيد
                </button>
                <button
                  type="button"
                  class="btn btn-outline-primary px-5 ms-3"
                  jhiTranslate="srsaApp.permitRequest.cancel"
                  data-bs-dismiss="modal"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <div class="modal fade" id="completeVisitModal" tabindex="-1" aria-labelledby="completeVisitModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <label class="head-style" jhiTranslate="srsaApp.permitRequest.confirmVisit">تأكيد الزيارة</label>
                <label *ngIf="completedPermitVisits.length > 0" jhiTranslate="srsaApp.permitRequest.uploadVisitDocs">
                  رفع محضر و تقرير الزيارة الميدانية</label
                >
                <span class="text-danger">*</span>
                <dropzone
                  [config]="fileConfig"
                  (addedFile)="onVistReportUploadFileSuccess($event, 'Visit_Report_DOC')"
                  (error)="onUploadFileError($event)"
                  (removedFile)="onFileRemoved($event)"
                >
                </dropzone>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button
                data-bs-dismiss="modal"
                type="button"
                class="btn btn-outline-primary px-4"
                [disabled]="visitReportDocuments.length == 0"
                (click)="sendToInspectionManager()"
                jhiTranslate="srsaApp.permitRequest.sendRqToMngr"
              >
                ارسال الطلب الى مدير المعاينة
              </button>
              <button
                type="button"
                class="btn btn-outline-primary px-5 ms-3"
                jhiTranslate="srsaApp.permitRequest.cancel"
                data-bs-dismiss="modal"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="permitConfirmModal" tabindex="-1" aria-labelledby="permitConfirmModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered custom-width">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="d-flex-center mb-3">
                <img src="../../../../content/images/Question mark.svg" alt="?" />
              </div>
              <div class="my-2">
                <h2 class="head-style-popup text-center" jhiTranslate="srsaApp.permitRequest.finalConfirmRq">الإعتماد النهائي للطلب</h2>
                <p class="subHead-style-popup text-center" jhiTranslate="srsaApp.permitRequest.previewRq">يرجي التأكد من كل التفاصيل</p>
                <div class="d-flex justify-content-center align-items-center" style="height: 50px">
                  <div class="me-3">
                    <label class="form-label question" jhiTranslate="srsaApp.permitRequest.exceptionApproval">
                      هل يحتاج إلي موافقة إستثنائية ؟
                    </label>
                    <div class="d-flex justify-content-evenly">
                      <input
                        type="checkbox"
                        class="form-check-input"
                        name="mandatoryYes"
                        data-cy="mandatoryYes"
                        id="exampleCheckbox1"
                        [checked]="permitRequest!.exceptionalApproval === true"
                        (change)="toggleExceptionalApproval(true)"
                      />
                      <label class="form-check-label me-5" for="exampleCheckbox1" jhiTranslate="srsaApp.permitRequest.yes"> نعم </label>
                      <input
                        type="checkbox"
                        class="form-check-input"
                        name="mandatoryNo"
                        data-cy="mandatoryNo"
                        id="exampleCheckbox2"
                        [checked]="permitRequest!.exceptionalApproval === false"
                        (change)="toggleExceptionalApproval(false)"
                      />
                      <label class="form-check-label" for="exampleCheckbox2" jhiTranslate="srsaApp.permitRequest.no"> لا </label>
                    </div>
                  </div>
                  <div class="vr"></div>
                  <div class="ms-3">
                    <label class="form-label question" jhiTranslate="srsaApp.permitRequest.feeRequired"> هل يلزم دفع رسوم الخدمة ؟ </label>
                    <div class="d-flex justify-content-evenly">
                      <input
                        type="checkbox"
                        class="form-check-input"
                        name="mandatoryYesFees"
                        data-cy="mandatoryYesFees"
                        id="exampleCheckboxFees1"
                        [checked]="permitRequest!.requiredPayFees === true"
                        [disabled]="!permitRequest!.exceptionalApproval"
                        (change)="toggleRequiredPayFees(true)"
                      />
                      <label class="form-check-label me-5" for="exampleCheckboxFees1" jhiTranslate="srsaApp.permitRequest.yes"> نعم </label>
                      <input
                        type="checkbox"
                        class="form-check-input"
                        name="mandatoryNoFees"
                        data-cy="mandatoryNoFees"
                        id="exampleCheckboxFees2"
                        [checked]="permitRequest!.requiredPayFees === false"
                        [disabled]="!permitRequest!.exceptionalApproval"
                        (change)="toggleRequiredPayFees(false)"
                      />
                      <label class="form-check-label" for="exampleCheckboxFees2" jhiTranslate="srsaApp.permitRequest.no"> لا </label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="my-2" *ngIf="permitRequest!.exceptionalApproval">
                <div *ngIf="permitRequest!.exceptionalApproval">
                  <label class="form-label mb-0" jhiTranslate="srsaApp.permitRequest.exceptionalDoc">رفع مستند مبررات الإستثناء</label>
                  <div class="custom-dropzone">
                    <dropzone
                      *ngIf="permitRequest!.exceptionalApproval"
                      required
                      id="CR"
                      [config]="fileConfig"
                      (addedFile)="onUploadFileSuccess($event, 'EXCEPTIONAL_DOC')"
                      (error)="onUploadFileError($event)"
                      (removedFile)="onFileRemoved($event)"
                    >
                    </dropzone>
                  </div>
                  <p class="supportImages mb-0 text-end me-2">Only support .jpg, .png and .svg and zip files</p>
                </div>
                <div class="my-2">
                  <label class="p-style" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</label><span class="text-danger">*</span>
                  <textarea class="form-control" id="notes" rows="3" [(ngModel)]="permitRequest!.requestNote" name="notes"></textarea>
                </div>
              </div>
              <div class="d-flex-center mb-4 gap-4">
                <button
                  type="button"
                  class="btn btn-primary px-4"
                  [disabled]="!sendRequestBtnEnabled()"
                  (click)="sendToPayPermitRequest()"
                  jhiTranslate="srsaApp.permitRequest.sendRqToProvider"
                  data-bs-dismiss="modal"
                >
                  إرسال الطلب للمنشأة
                </button>
                <button
                  type="button"
                  class="btn btn-outline-primary px-5"
                  data-bs-dismiss="modal"
                  jhiTranslate="srsaApp.permitRequest.cancel"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="editCompleteVisitModal" tabindex="-1" aria-labelledby="editCompleteVisitModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <p class="head-style" jhiTranslate="srsaApp.permitRequest.editVisit">تعديل محضر وتقرير الزيارة الميدانية الاخيرة</p>
                <label *ngIf="completedPermitVisits.length > 0" jhiTranslate="srsaApp.permitRequest.visitReport">
                  محضر وتقرير الزيارة الميدانية
                </label>
                <span class="text-danger">*</span>
                <dropzone
                  [config]="fileConfig"
                  (addedFile)="onVistReportUploadFileSuccess($event, 'Visit_Report_DOC')"
                  (error)="onUploadFileError($event)"
                  (removedFile)="onFileRemoved($event)"
                >
                </dropzone>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button
                type="button"
                class="btn btn-primary px-5"
                [disabled]="visitReportDocuments.length == 0"
                (click)="editVisitReport()"
                jhiTranslate="srsaApp.permitRequest.editAndsendRqToMngr"
                data-bs-dismiss="modal"
              >
                تعديل وارسال الطلب الى مدير المعاينة
              </button>
              <button
                type="button"
                class="btn btn-outline-primary px-4"
                data-bs-dismiss="modal"
                jhiTranslate="srsaApp.permitRequest.cancel"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="licenceOfficerConfirmModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" (click)="confirm(false)"></button>
            </div>
            @if (!licenceOfficerConfirmModal) {
              <div class="modal-body">
                <div class="text-center mb-4">
                  <img src="../../../../content/images/QuestionMark.svg" alt="?" />
                </div>
                <div class="my-2 text-center">
                  <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.ifComplatedReq">هل أنت متأكد أن الطلب مكتمل؟</h2>
                  <p class="second-text" jhiTranslate="srsaApp.permitRequest.ifComplatedReqNote">بمجرد تأكيدك، لا يمكنك إرجاع الطلب.</p>
                </div>
              </div>
              <div class="d-flex-center mb-4 gap-4">
                <button
                  type="button"
                  class="btn btn-outline-primary px-5"
                  jhiTranslate="srsaApp.permitRequest.back"
                  data-bs-dismiss="modal"
                  (click)="confirm(false)"
                >
                  لا
                </button>
                <button type="button" jhiTranslate="srsaApp.permitRequest.confirm" class="btn btn-primary px-5" (click)="confirm(true)">
                  نعم
                </button>
              </div>
            }
            @if (licenceOfficerConfirmModal) {
              <div class="modal-body">
                <div class="my-2">
                  <div class="d-flex align-items-baseline gap-2">
                    <img src="../../../../content/images/editPn.svg" alt="" />
                    <h2 class="notes" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</h2>
                  </div>

                  <textarea
                    class="form-control"
                    id="notes"
                    rows="3"
                    placeholder="الرجاء كتابة أي تعليق أو ملاحظة ترغب في مشاركتها"
                    [(ngModel)]="permitRequest!.requestNote"
                    name="notes"
                    style="
                      border: 1px solid #bfbfbf;

                      background: #f7f7f7;
                    "
                  ></textarea>
                </div>
                <div class="my-2">
                  <label class="form-label" jhiTranslate="srsaApp.permitRequest.visitRequired">هل الطلب يتطلب زيارة ميدانية؟</label>
                  <div class="d-flex">
                    <input
                      class="form-check-input"
                      type="radio"
                      id="visitRequiredYes"
                      value="true"
                      [(ngModel)]="permitRequest!.needVisit"
                    />
                    <label class="form-check-label me-5 ms-4" for="visitRequiredYes" jhiTranslate="srsaApp.permitRequest.yes"> نعم </label>
                    <input
                      class="form-check-input"
                      type="radio"
                      id="visitRequiredNo"
                      value="false"
                      [(ngModel)]="permitRequest!.needVisit"
                    />
                    <label class="form-check-label ms-4" for="visitRequiredNo" jhiTranslate="srsaApp.permitRequest.no"> لا </label>
                  </div>
                </div>
              </div>
              <div class="d-flex-center mb-4 gap-4">
                <button
                  type="button"
                  (click)="confirm(false)"
                  class="btn btn-outline-primary px-5"
                  jhiTranslate="srsaApp.permitRequest.cancel"
                  data-bs-dismiss="modal"
                >
                  إلغاء
                </button>
                <button
                  [disabled]="
                    permitRequest!.requestNote == null || permitRequest!.requestNote.length == 0 || permitRequest!.requestNote == ''
                  "
                  type="button"
                  (click)="licensingOfficerSubmit(permitRequest!.needVisit)"
                  jhiTranslate="srsaApp.permitRequest.confirm"
                  class="btn btn-primary px-5"
                  data-bs-dismiss="modal"
                >
                  تأكيد
                </button>
              </div>
            }
          </div>
        </div>
      </div>
      <div class="modal fade" id="rejectPermitModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.ifRejectRq">هل متأكد من رفض الطلب؟</h2>
                <label class="head-style" jhiTranslate="srsaApp.permitRequest.rejectReason">سبب الرفض</label
                ><span class="text-danger">*</span>
                <textarea
                  class="form-control"
                  id="rejectField"
                  rows="3"
                  [(ngModel)]="permitRequest!.requestNote"
                  name="rejectField"
                ></textarea>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button type="button" class="btn btn-primary px-5" data-bs-dismiss="modal" jhiTranslate="srsaApp.permitRequest.no">لا</button>
              <button
                type="button"
                (click)="rejectPermitRequest()"
                class="btn btn-outline-primary px-5"
                jhiTranslate="srsaApp.permitRequest.yes"
                data-bs-dismiss="modal"
              >
                نعم
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="modal fade" id="retrunPermitModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2 text-center">
                <h2 class="head-style mt-2" jhiTranslate="srsaApp.permitRequest.ifReturnRq">هل متأكد من إعادة الطلب للإستكمال؟</h2>

                <!-- Radio buttons for PENDING_MANAGEMENT_LICENSING status -->
                <div *ngIf="permitRequest!.requestStatus === 'PENDING_MANAGEMENT_LICENSING'" class="mt-3">
                  <div class="options d-flex justify-content-center my-4 gap-4">
                    <div class="d-flex align-items-center gap-4">
                      <p-radioButton
                        [inputId]="'returnToRequestProvider'"
                        name="returnOption"
                        value="REQUEST_PROVIDER"
                        [(ngModel)]="selectedReturnOption"
                        [label]="'srsaApp.permitRequest.returnReqToProvider' | translate"
                      />
                    </div>
                    <div class="d-flex align-items-center gap-4">
                      <p-radioButton
                        [inputId]="'returnToLicensingSupervisor'"
                        name="returnOption"
                        value="LICENSING_SUPERVISOR"
                        [(ngModel)]="selectedReturnOption"
                        [label]="'srsaApp.permitRequest.returnToLicensingSupervisor' | translate"
                      />
                    </div>
                  </div>
                </div>

                <div class="my-2">
                  <label class="p-style" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</label>
                  <textarea class="form-control" id="notes" rows="3" [(ngModel)]="permitRequest!.requestNote" name="notes"></textarea>
                </div>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button type="button" class="btn btn-primary px-5" jhiTranslate="srsaApp.permitRequest.no" data-bs-dismiss="modal">لا</button>
              <button
                [disabled]="
                  permitRequest!.requestNote == null || permitRequest!.requestNote.length == 0 || permitRequest!.requestNote == ''
                "
                type="button"
                (click)="returnPermitRequest()"
                class="btn btn-outline-primary px-5"
                jhiTranslate="srsaApp.permitRequest.yes"
                data-bs-dismiss="modal"
              >
                نعم
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="licenceOfficerModal" tabindex="-1" aria-labelledby="licenceOfficerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.esnad">
                  <fa-icon icon="plus"></fa-icon>
                  إسناد
                </h2>
                <label class="p-style" jhiTranslate="srsaApp.permitRequest.licenceOfficer">مسؤول إدارة التراخيص</label>
                <span class="text-danger">*</span>
              </div>
              <select
                class="form-select"
                data-cy="selectedlicenseDurationValues"
                [(ngModel)]="selectedOfficer"
                [ngModelOptions]="{ standalone: true }"
              >
                <option [ngValue]="null"></option>
                <option [ngValue]="officer" *ngFor="let officer of licensingOfficers">
                  {{ officer['firstName'] + ' ' + officer['lastName'] }}
                </option>
              </select>
              <div class="my-2">
                <label class="p-style" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</label><span class="text-danger">*</span>
                <textarea class="form-control" id="notes2" rows="3" [(ngModel)]="permitRequest!.requestNote" name="notes"></textarea>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button
                class="btn btn-primary px-5"
                [disabled]="
                  !selectedOfficer ||
                  permitRequest!.requestNote == null ||
                  permitRequest!.requestNote.length == 0 ||
                  permitRequest!.requestNote == ''
                "
                data-bs-dismiss="modal"
                (click)="selectOfficer()"
              >
                <span jhiTranslate="srsaApp.permitRequest.selectOfficer">اختيار الموظف</span>
              </button>
              <button type="button" class="btn btn-outline-primary px-5" data-bs-dismiss="modal">
                <span jhiTranslate="srsaApp.permitRequest.cancel">الغاء</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="inspectionOfficerModal" tabindex="-1" aria-labelledby="inspectionOfficerModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.esnad">
                  <fa-icon icon="plus"></fa-icon>
                  إسناد
                </h2>
                <label class="p-style" jhiTranslate="srsaApp.permitRequest.inspectionOfficer">مسؤول إدارة المعاينة</label>
                <span class="text-danger">*</span>
              </div>
              <select
                class="form-select"
                id="selectedInspection"
                data-cy="selectedInspection"
                [(ngModel)]="selectedInspectionOfficer"
                [ngModelOptions]="{ standalone: true }"
              >
                <option [ngValue]="null"></option>
                <option [ngValue]="officer" *ngFor="let officer of inspectionOfficers">
                  {{ officer['firstName'] + ' ' + officer['lastName'] }}
                </option>
              </select>
              <div class="my-2">
                <label class="p-style" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</label><span class="text-danger">*</span>
                <textarea class="form-control" id="notes1" rows="3" [(ngModel)]="permitRequest!.requestNote" name="notes"></textarea>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button
                [disabled]="
                  permitRequest!.requestNote == null ||
                  permitRequest!.requestNote.length == 0 ||
                  permitRequest!.requestNote == '' ||
                  !selectedInspectionOfficer
                "
                type="submit"
                class="btn btn-primary px-4"
                (click)="selectInspectionOfficer()"
                data-bs-dismiss="modal"
              >
                <span jhiTranslate="srsaApp.permitRequest.selectOfficer">اختيار الموظف</span>
              </button>
              <button type="button" class="btn btn-outline-primary px-5" data-bs-dismiss="modal">
                <span jhiTranslate="srsaApp.permitRequest.cancel">الغاء</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="licenceMngrModal" tabindex="-1" aria-labelledby="inspectionOfficerModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.managerlisence">
                  <fa-icon icon="plus"></fa-icon>
                  إسناد
                </h2>
              </div>
              <div class="my-2">
                <label class="p-style" jhiTranslate="srsaApp.permitRequest.notes">ملاحظات</label><span class="text-danger">*</span>
                <textarea class="form-control" id="notes" rows="3" [(ngModel)]="permitRequest!.requestNote" name="notes"></textarea>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button
                data-bs-dismiss="modal"
                type="submit"
                class="btn btn-primary px-4"
                [disabled]="
                  permitRequest!.requestNote == null || permitRequest!.requestNote.length == 0 || permitRequest!.requestNote == ''
                "
                (click)="selectLicensingManagers()"
              >
                <span jhiTranslate="srsaApp.permitRequest.confirm">اختيار المدير</span>
              </button>
              <button type="button" class="btn btn-outline-primary px-5" data-bs-dismiss="modal">
                <span jhiTranslate="srsaApp.permitRequest.cancel">الغاء</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="cancelLicenceOfficerModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="my-2">
                <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.ifCancelRq">هل متأكد من إلغاء الإسناد؟</h2>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button type="button" class="btn btn-primary px-5" jhiTranslate="srsaApp.permitRequest.no" data-bs-dismiss="modal">لا</button>
              <button
                data-bs-dismiss="modal"
                type="button"
                (click)="unAssignPermitRequest(permitRequest)"
                jhiTranslate="srsaApp.permitRequest.yes"
                class="btn btn-outline-primary px-5"
              >
                نعم
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade" id="cancelInspectionOfficerModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="d-flex-end">
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="text-center mb-4">
                <img src="../../../../content/images/Question mark.svg" alt="?" />
              </div>
              <div class="my-2 text-center">
                <h2 class="head-style" jhiTranslate="srsaApp.permitRequest.ifCancelRq">هل متأكد من إلغاء الإسناد؟</h2>
              </div>
            </div>
            <div class="d-flex-center mb-4 gap-4">
              <button type="button" class="btn btn-outline-primary px-5" jhiTranslate="srsaApp.permitRequest.no" data-bs-dismiss="modal">
                لا
              </button>
              <button
                data-bs-dismiss="modal"
                type="button"
                (click)="unAssignPermitRequest(permitRequest)"
                jhiTranslate="srsaApp.permitRequest.yes"
                class="btn btn-primary px-5"
              >
                نعم
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
