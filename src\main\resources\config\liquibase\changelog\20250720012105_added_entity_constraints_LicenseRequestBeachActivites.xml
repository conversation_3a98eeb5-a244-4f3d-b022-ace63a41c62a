<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <!--
        Added the constraints for entity LicenseRequestMaritimeServices.
    -->
    <changeSet id="20250720040005" author="faalkanhal">

        <addForeignKeyConstraint baseColumnNames="license_request_id"
                                 baseTableName="license_request_beach_activities"
                                 constraintName="fk_license_request_beach_activities__license_request_id"
                                 referencedColumnNames="id"
                                 referencedTableName="license_request"
                                 />

        <addForeignKeyConstraint baseColumnNames="beach_activities_id"
                                 baseTableName="license_request_beach_activities"
                                 constraintName="fk_license_request_beach_activities__beach_activities_id"
                                 referencedColumnNames="id"
                                 referencedTableName="maritime_tourism_services"
                                 />
    </changeSet>
</databaseChangeLog>
