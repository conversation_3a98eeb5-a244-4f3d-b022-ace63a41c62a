<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ServiceType.
    -->
    <changeSet id="20240429102517-1" author="jhipster">
        <createTable tableName="service_type">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name_ar" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="name_en" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="isic_code" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="desc_ar" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="desc_en" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="fees" type="decimal(21,2)">
                <constraints nullable="true" />
            </column>
            <column name="record_activity" type="boolean" defaultValue="true">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="varchar(50)" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp" defaultValueDate="${now}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20240429102517-1-data" author="jhipster" >
        <loadData
                  file="config/liquibase/fake-data/service_type.csv"
                  separator=";"
                  tableName="service_type"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="name_ar" type="string"/>
            <column name="name_en" type="string"/>
            <column name="isic_code" type="numeric"/>
            <column name="desc_ar" type="string"/>
            <column name="desc_en" type="string"/>
            <column name="fees" type="numeric"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>

    <changeSet id="20210103883-add-code" author="awagdy">
        <addColumn tableName="service_type">
            <column name="code" type="varchar(100)">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="20210607000001-update-code" author="awagdy">
        <sql>
            <![CDATA[
            update service_type set code='MaritimeTourismAgent' where id=1;
            update service_type set code='LargeYachtCharteringLicense' where id=2;
            update service_type set code='MarinaOperatorLicense' where id=3;
            update service_type set code='MarineTourOperatorLicense' where id=4;
            update service_type set code='CruiseShipsOperatorLicense' where id=5;
            ]]>
        </sql>
    </changeSet>
    <changeSet id="20210103883-add-new-fields" author="awagdy">
        <addColumn tableName="service_type">
            <column name="service_entry" type="varchar(100)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <addColumn tableName="service_type">
            <column name="service_identifier" type="varchar(100)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <addColumn tableName="service_type">
            <column name="target_entity" type="varchar(100)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <addColumn tableName="service_type">
            <column name="expected_execution_days" type="integer">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20210607000001-update-fields" author="awagdy">
        <sql>
            <![CDATA[
            update service_type set code='MA' ,service_entry='L' ,service_identifier='100' where id=1;
            update service_type set code='YC' ,service_entry='L' ,service_identifier='100' where id=2;
            update service_type set code='MO' ,service_entry='L' ,service_identifier='100' where id=3;
            update service_type set code='TO' ,service_entry='L' ,service_identifier='100'   where id=4;
            update service_type set code='CO' ,service_entry='L' ,service_identifier='100' where id=5;
            ]]>
        </sql>
    </changeSet>
    <changeSet id="20240226164600-sequence-generator-00100" author="aw">
        <createSequence sequenceName="sequence_rq_number" startValue="1000" incrementBy="50"/>
    </changeSet>
    <changeSet id="20210607000001-update-fieldsV1" author="awagdy">
        <sql>
            <![CDATA[
            update service_type set target_entity='شركات' , expected_execution_days= 3 ;
            ]]>
        </sql>
    </changeSet>
    <changeSet id="20210607000001-add-period-days" author="awagdy">
        <addColumn tableName="service_type">
            <column name="payment_period_days" type="integer">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <sql>
            <![CDATA[
            update service_type set payment_period_days= 3 ;
            ]]>
        </sql>
    </changeSet>

    <changeSet id="20240521000001-modify-service-catalog" author="hasan" runOnChange="true">
        <sqlFile dbms="mssql"
                 encoding="UTF-8"
                 endDelimiter="\nGO"
                 path="../sql/20240521000001-modify-service-catalog.sql"
                 relativeToChangelogFile="true"
                 splitStatements="true"
                 stripComments="true"/>
    </changeSet>

    <changeSet id="20240521000001-add-service-activities" author="hasan">
        <addColumn tableName="service_type">
            <column name="activity_ar" type="varchar(255)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <addColumn tableName="service_type">
            <column name="activity_en" type="varchar(255)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <sql>
            <![CDATA[
            UPDATE service_type SET activity_ar = N'وكالات الملاحة السياحية', activity_en = N'Tourist maritime agencies' WHERE id = 1;
            UPDATE service_type SET activity_ar = N'تأجير اليخوت', activity_en = N'Yacht chartering' WHERE id = 2;
            UPDATE service_type SET activity_ar = N'تشغيل المراسي البحرية السياحية', activity_en = N'Operating tourist marinas' WHERE id = 3;
            ]]>
        </sql>
    </changeSet>

    <changeSet id="20240521000001-modify-service-catalog-2" author="hasan">
        <sqlFile dbms="mssql"
                 encoding="UTF-8"
                 endDelimiter="\nGO"
                 path="../sql/20240521000001-modify-service-catalog-2.sql"
                 relativeToChangelogFile="true"
                 splitStatements="true"
                 stripComments="true"/>
    </changeSet>

    <changeSet id="20210607000001-add-tech-permit" author="awagdy">
        <sql>
            <![CDATA[
            INSERT service_type ([id], [name_ar], [name_en], [isic_code], [desc_ar], [desc_en], [fees], [record_activity], [created_by], [created_date], [last_modified_by], [last_modified_date],
[code], [service_entry], [service_identifier], [target_entity], [expected_execution_days], [payment_period_days], [activity_ar], [activity_en])
VALUES (6, N'الترخيص الفني', N'Technical Permit', 0000
, N'الترخيص الفني',
N'Technical Permit',
CAST(11000.00 AS Decimal(21, 2)), 1, N'1', CAST(N'2024-08-20T15:40:39.6900000' AS DateTime2), NULL, NULL,
N'TL', N'L', N'194', N'شركات', 10, 3, N'الترخيص الفني', N'Technical Permit')

            ]]>
        </sql>
    </changeSet>

    <changeSet id="20250205000001-update-tech-permit-fees" author="faalkanhal">
        <sql>
        <![CDATA[
            UPDATE service_type
            SET fees = CAST(4000.00 AS Decimal(21, 2))
            WHERE id = 6;
            ]]>
    </sql>
    </changeSet>

    <changeSet id="20240521000001-update-isic" author="aw">
        <sql>
            <![CDATA[
            UPDATE service_type SET isic_code = 522208  WHERE id = 1;
            UPDATE service_type SET isic_code = 501104 WHERE id = 2;
            UPDATE service_type SET isic_code = 522207 WHERE id = 3;
            ]]>
        </sql>
    </changeSet>
    <changeSet id="update-service-type-names-2" author="osama">
        <update tableName="service_type">
            <column name="name_ar" value="منشأة تأجير يخوت"/>
            <column name="name_en" value="Yacht Rental Facility"/>
            <where>id = 2</where>
        </update>
    </changeSet>


    <changeSet id="20250715170000-add-yacht-charter-license" author="rsobh">
        <insert tableName="service_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM service_type)"/>
            <column name="name_ar" value="رخصة تأجير يخوت"/>
            <column name="name_en" value="Yacht Charter License"/>
            <column name="isic_code" valueNumeric="0"/>
            <column name="desc_ar" value="تتيح الخدمة لمنشأة تأجير يخوت من إصدار تصريح تأجير يخت والتي تخوله لتقديم الخدمات الخاصة باليخت بناء على تفاصيل التصريح."/>
            <column name="desc_en" value="Written approval issued by the Saudi Red Sea Authority enabling Yacht Rental Facility to practice the activity of renting yachts to beneficiaries within a specified period of time, subject to renewal."/>
            <column name="fees" valueNumeric="4000.00"/>
            <column name="record_activity" valueNumeric="1"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
            <column name="last_modified_by" valueComputed="NULL"/>
            <column name="last_modified_date" valueComputed="NULL"/>
            <column name="code" value="TC"/>
            <column name="service_entry" value="L"/>
            <column name="service_identifier" value="194"/>
            <column name="target_entity" value="شركات"/>
            <column name="expected_execution_days" valueNumeric="10"/>
            <column name="payment_period_days" valueNumeric="3"/>
            <column name="activity_ar" value="رخصة تأجير يخوت"/>
            <column name="activity_en" value="Yacht Charter License"/>
        </insert>
    </changeSet>

    <changeSet id="2025072001510-add-beach-operator-license" author="faalkanhal">
        <insert tableName="service_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM service_type)"/>
            <column name="name_ar" value="رخصة مشغل شاطئ"/>
            <column name="name_en" value="Beach Operator License"/>
            <column name="isic_code" valueNumeric="141015"/>
            <column name="desc_ar" value="تتيح الخدمة لمنشأة تأجير يخوت من إصدار تصريح تأجير يخت والتي تخوله لتقديم الخدمات الخاصة باليخت بناء على تفاصيل التصريح."/>
            <column name="desc_en" value="Written approval issued by the Saudi Red Sea Authority enabling Yacht Rental Facility to practice the activity of renting yachts to beneficiaries within a specified period of time, subject to renewal."/>
            <column name="fees" valueNumeric="1600.00"/>
            <column name="record_activity" valueNumeric="1"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
            <column name="last_modified_by" valueComputed="NULL"/>
            <column name="last_modified_date" valueComputed="NULL"/>
            <column name="code" value="BO"/>
            <column name="service_entry" value="L"/>
            <column name="service_identifier" value="194"/>
            <column name="target_entity" value="شركات"/>
            <column name="expected_execution_days" valueNumeric="10"/>
            <column name="payment_period_days" valueNumeric="3"/>
            <column name="activity_ar" value="رخصة مشغل شاطئ"/>
            <column name="activity_en" value="Beach Operator License"/>
        </insert>
    </changeSet>

    <changeSet id="update-service-type-names-3" author="faalkanhal">
        <update tableName="service_type">
            <column name="name_ar">
                <![CDATA[رخصة تأجير سياحي]]>
            </column>
            <column name="desc_ar">
                <![CDATA[تتيح الخدمة لمنشأة تأجير يخوت من إصدار ترخيص تأجير سياحي والتي تخوله لتقديم الخدمات الخاصة باليخت بناء على تفاصيل الترخيص.]]>
            </column>
            <where>code = 'TC'</where>
        </update>
    </changeSet>

    <changeSet id="update-service-type-names-4" author="faalkanhal">
        <sql>
        <![CDATA[
            UPDATE service_type
            SET isic_code = '932922',
                desc_ar = N'تهدف هذه الخدمة بأن تخول مشغل الشاطئ للقيام بأعمال تشغيل الشاطئ والقيام بأعمال التشغيل اللازمة والتي تضمن حماية البيئة والسلامة والصحة العامة.',
                desc_en = N'This service aims to authorize the beach operator to carry out beach operation activities and perform the necessary operational tasks that ensure the protection of the environment, safety, and public health',
                service_identifier = '100'
            WHERE code = 'BO';
            ]]>
    </sql>
    </changeSet>
    <changeSet id="update-service-type-names-5" author="faalkanhal">
        <sql>
        <![CDATA[
            UPDATE service_type
            SET  service_identifier = '194'
            WHERE code = 'BO';
            ]]>
    </sql>
    </changeSet>
    <changeSet id="20250805120000-add-visa-tourism-request" author="osama">
        <insert tableName="service_type">
            <column name="id" valueComputed="(SELECT MAX(id) + 1 FROM service_type)"/>
            <column name="name_ar" value="تأشيرة سياحية"/>
            <column name="name_en" value="Tourism Visa"/>
            <column name="isic_code" valueNumeric="141016"/>
            <column name="desc_ar" value="تتيح الخدمة التقديم على تأشيرة سياحية عبر منشآت مرخصة لنقل السائحين إلى المملكة عن طريق الوسائل البحرية."/>
            <column name="desc_en" value="This service allows applying for a tourism visa through licensed entities to transport tourists to the Kingdom via marine vessels."/>
            <column name="fees" valueNumeric="300"/>
            <column name="record_activity" valueNumeric="1"/>
            <column name="created_by" value="system"/>
            <column name="created_date" value="${now}"/>
            <column name="last_modified_by" valueComputed="NULL"/>
            <column name="last_modified_date" valueComputed="NULL"/>
            <column name="code" value="TVR"/>
            <column name="service_entry" value="L"/>
            <column name="service_identifier" value="195"/>
            <column name="target_entity" value="شركات"/>
            <column name="expected_execution_days" valueNumeric="7"/>
            <column name="payment_period_days" valueNumeric="3"/>
            <column name="activity_ar" value="تأشيرة سياحية"/>
            <column name="activity_en" value="Tourism Visa"/>
        </insert>
    </changeSet>


</databaseChangeLog>
