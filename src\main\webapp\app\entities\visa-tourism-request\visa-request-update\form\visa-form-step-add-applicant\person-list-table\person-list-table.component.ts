import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import TranslateDirective from 'app/shared/language/translate.directive';
import { VisaApplicant } from 'app/entities/visa-tourism-request/visa-applicant.model';

@Component({
  selector: 'jhi-person-list-table',
  standalone: true,
  imports: [CommonModule, TranslateModule, TranslateDirective],
  templateUrl: './person-list-table.component.html',
  styleUrl: './person-list-table.component.scss',
})
export class PersonListTableComponent implements OnInit {
  @Input() persons: VisaApplicant[] = [];
  @Output() personRemoved = new EventEmitter<string>();
  @Output() personEdited = new EventEmitter<VisaApplicant>();

  constructor() {}

  ngOnInit(): void {
    // Component initialization
  }

  getDisplayName(person: VisaApplicant): string {
    return person.fullName || 'N/A';
  }

  getDisplayNationality(person: VisaApplicant): string {
    return person.nationality || 'N/A';
  }

  getDisplayPhoneNumber(person: VisaApplicant): string {
    return person.phoneNumber || 'N/A';
  }

  getDisplayPassportNumber(person: VisaApplicant): string {
    return person.passportNumber || 'N/A';
  }

  onRemovePerson(person: VisaApplicant): void {
    if (person.id) {
      this.personRemoved.emit(person.id);
    }
  }

  onEditPerson(person: VisaApplicant): void {
    this.personEdited.emit(person);
  }

  confirmRemovePerson(person: VisaApplicant): void {
    const confirmMessage = `Are you sure you want to remove ${person.fullName} from the list?`;
    if (confirm(confirmMessage)) {
      this.onRemovePerson(person);
    }
  }
}
