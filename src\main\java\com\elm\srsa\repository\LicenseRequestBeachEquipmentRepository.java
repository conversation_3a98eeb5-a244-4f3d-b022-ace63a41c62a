package com.elm.srsa.repository;

import com.elm.srsa.domain.*;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the LicenseRequestMaritimeMedium entity.
 */
@SuppressWarnings("unused")
@Repository
public interface LicenseRequestBeachEquipmentRepository extends JpaRepository<LicenseRequestBeachEquipments, Long> {
    void deleteByLicenseRequestIdAndBeachEquipmentId(Long licenseRequestId, Long beachEquipmentId);

    List<LicenseRequestBeachEquipments> findByLicenseRequestId(Long licenseRequestId);

    @Query("SELECT lrbe.beachEquipment FROM LicenseRequestBeachEquipments lrbe WHERE lrbe.licenseRequest.id = :licenseRequestId")
    Set<BeachEquipment> findBeachEquipmentByLicenseRequestId(@Param("licenseRequestId") Long licenseRequestId);
}
