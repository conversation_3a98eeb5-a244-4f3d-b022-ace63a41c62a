package com.elm.srsa.domain.visa;

import com.elm.srsa.domain.AbstractAuditingEntity;
import jakarta.persistence.*;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "visa_tourism_request_attachment")
@Getter
@Setter
public class VisaTourismRequestAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "jhi_desc")
    private String desc; // وصف الملف

    @Column(name = "doc_name")
    private String docName; // اسم الملف

    @Column(name = "doc_url")
    private String docUrl; // رابط الملف

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "visa_request_id")
    private VisaRequest visaRequest;
}
