package com.elm.srsa.mofa.client.response;

public class ApplicantResponse {

    private String passportNumber;
    private Integer visaStatus;
    private Long visaNumber;
    private String applicantNumber;
    private Object errors; // Could be more specific if error structure is known
    private VisaResponseInsurance inusrance;

    // Constructors, getters, and setters
    public ApplicantResponse() {}

    // Getters and Setters
    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    public Integer getVisaStatus() {
        return visaStatus;
    }

    public void setVisaStatus(Integer visaStatus) {
        this.visaStatus = visaStatus;
    }

    public Long getVisaNumber() {
        return visaNumber;
    }

    public void setVisaNumber(Long visaNumber) {
        this.visaNumber = visaNumber;
    }

    public String getApplicantNumber() {
        return applicantNumber;
    }

    public void setApplicantNumber(String applicantNumber) {
        this.applicantNumber = applicantNumber;
    }

    public Object getErrors() {
        return errors;
    }

    public void setErrors(Object errors) {
        this.errors = errors;
    }

    public VisaResponseInsurance getInusrance() {
        return inusrance;
    }

    public void setInusrance(VisaResponseInsurance inusrance) {
        this.inusrance = inusrance;
    }
}
