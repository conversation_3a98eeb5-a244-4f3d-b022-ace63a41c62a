import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { VisaRequestViewModel } from './visa-request-view/visa-request-view.model';

const VisaTourismResolve = (route: ActivatedRouteSnapshot): Observable<null | VisaRequestViewModel> => {
  const id = route.params.id;
  // if (id) {
  //   return inject(VisaTourismRequestService)
  //     .find(id)
  //     .pipe(
  //       mergeMap((response: HttpResponse<VisaRequestViewModel>) => {
  //         if (response.body) {
  //           return of(response.body);
  //         }
  //         inject(Router).navigate(['404']);
  //         return EMPTY;
  //       }),
  //     );
  // }
  return of(null);
};

export default VisaTourismResolve;
