package com.elm.srsa.domain.visa;

import com.elm.srsa.domain.MarineMedium;
import com.elm.srsa.domain.User;
import com.elm.srsa.domain.visa.enumerations.RequestStatus;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "visa_request")
@Getter
@Setter
public class VisaRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // المعرف

    private String requestNumber; // رقم الطلب

    @Enumerated(EnumType.STRING)
    private RequestStatus requestStatus; // حالة الطلب

    private String visaType; // نوع التأشيرة

    private LocalDateTime createdAt; // تاريخ الإنشاء
    private LocalDateTime updatedAt; // تاريخ التحديث

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user; // مقدم الطلب

    @ManyToOne
    @JoinColumn(name = "marine_medium_id")
    private MarineMedium marineMedium; // الوسيلة البحرية

    private BigDecimal totalFee; // إجمالي الرسوم

    @OneToMany(mappedBy = "visaRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<VisaApplicant> applicants;

    @OneToMany(mappedBy = "visaRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private Set<VisaTourismRequestAttachment> attachments;
}
