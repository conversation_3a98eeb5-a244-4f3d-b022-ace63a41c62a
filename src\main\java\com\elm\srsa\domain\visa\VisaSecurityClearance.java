package com.elm.srsa.domain.visa;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "visa_security_clearance")
@Getter
@Setter
public class VisaSecurityClearance {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne
    @JoinColumn(name = "visa_applicant_id")
    private VisaApplicant visaApplicant;

    private Boolean interpolArrest;
    private Boolean deported;
    private Boolean hasTravelRestrictions;
    private Boolean hasBeenArrestedForFinance;
    private Boolean sentencedInHomeCountry;
    private Boolean servedInMilitary;
    private Boolean workedInPoliticalOrMedia;
    private Boolean linkedToTerroristOrg;
    private String clarification;
}
