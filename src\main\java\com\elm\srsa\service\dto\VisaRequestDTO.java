package com.elm.srsa.service.dto;

import com.elm.srsa.domain.visa.enumerations.RequestStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VisaRequestDTO {

    private Long id;
    private String requestNumber;
    private RequestStatus requestStatus;
    private String visaType;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long userId;
    private Long marineMediumId;
    private BigDecimal totalFee;
    private List<VisaApplicantDTO> applicants;
}
