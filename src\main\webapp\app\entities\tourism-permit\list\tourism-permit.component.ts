import { Component, NgZone, OnInit, inject } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { ActivatedRoute, Data, ParamMap, Router, RouterModule } from '@angular/router';
import { Observable, Subscription, combineLatest, filter, tap } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import SharedModule from 'app/shared/shared.module';
import { SortByDirective, SortDirective, SortService, type SortState, sortStateSignal } from 'app/shared/sort';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { ItemCountComponent } from 'app/shared/pagination';
import { FormsModule } from '@angular/forms';
import { ITEMS_PER_PAGE, PAGE_HEADER, TOTAL_COUNT_RESPONSE_HEADER } from 'app/config/pagination.constants';
import { DEFAULT_SORT_DATA, ITEM_DELETED_EVENT, SORT } from 'app/config/navigation.constants';
import { FilterComponent, FilterOptions, IFilterOption, IFilterOptions } from 'app/shared/filter';
import { ITourismPermit } from '../tourism-permit.model';

import { EntityArrayResponseType, TourismPermitService } from '../service/tourism-permit.service';
import { TourismPermitDeleteDialogComponent } from '../delete/tourism-permit-delete-dialog.component';
import { TechnicalPermitDeleteDialogComponent } from '../../technical-permit/delete/technical-permit-delete-dialog.component';
import { TranslateService } from '@ngx-translate/core';
import HasAnyAuthorityDirective from '../../../shared/auth/has-any-authority.directive';
import { Authority } from '../../../config/authority.constants';
import { TourismPermitStatusType } from '../../enumerations/tourism-permit-status-type.model';
import { MarineStatusType } from '../../enumerations/marine-status-type.model';
import { TableComponent } from 'app/layouts/table/table.component';
import { DATE_FORMAT, DATE_TIME_FORMAT } from 'app/config/input.constants';
import { MarineMedium } from '../../marine-medium/model/marine-medium.model';
import { MarineMediumService } from '../../marine-medium/marine-medium.service';
import { PaginatorModule } from 'primeng/paginator';
import { AccountService } from '../../../core/auth/account.service';
import { Account } from '../../../core/auth/account.model';

type TableAction = {
  label: string;
  action: string;
  id: number;
  routerLink?: string; // Make routerLink optional
  handler?: () => void; // Add handler as an optional property
};

@Component({
  standalone: true,
  selector: 'jhi-tourism-permit',
  templateUrl: './tourism-permit.component.html',
  styleUrl: './tourism-permit.component.scss',
  imports: [
    RouterModule,
    FormsModule,
    SharedModule,
    SortDirective,
    SortByDirective,
    DurationPipe,
    FormatMediumDatetimePipe,
    FormatMediumDatePipe,
    FilterComponent,
    ItemCountComponent,
    HasAnyAuthorityDirective,
    TableComponent,
    PaginatorModule,
  ],
  providers: [FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class TourismPermitComponent implements OnInit {
  protected readonly Authority = Authority;
  subscription: Subscription | null = null;
  tourismPermits!: ITourismPermit[];
  protected translateService = inject(TranslateService);
  tourismPermit?: ITourismPermit;
  isLoading = false;
  searchRqNumber = '';
  sortState = sortStateSignal({});
  filters: IFilterOptions = new FilterOptions();
  account?: Account | null;

  itemsPerPage = ITEMS_PER_PAGE;
  totalItems = 0;
  page = 0;
  itemToBeDeleted = 0;

  public router = inject(Router);
  protected tourismPermitService = inject(TourismPermitService);
  protected activatedRoute = inject(ActivatedRoute);
  protected marineMediumService = inject(MarineMediumService);
  protected sortService = inject(SortService);
  protected modalService = inject(NgbModal);
  protected ngZone = inject(NgZone);
  protected accountService = inject(AccountService);
  activeTab: string = 'allRequests'; // Default active tab
  criteria: any;
  trackId = (item: ITourismPermit): number => this.tourismPermitService.getTourismPermitIdentifier(item);
  tableActions: any[] = [];
  tableColumns = [
    { defaultField: 'requestNumber', header: 'srsaApp.technicalPermit.permitNumber', sortable: false },
    {
      defaultField: 'marineMedium.nameAr',
      enField: 'marineMedium.nameEn',
      header: 'srsaApp.technicalPermit.marineMedium',
      sortable: false,
      localeValues: true,
    },
    {
      defaultField: 'requestStatus',
      fieldJhiTranslate: 'srsaApp.tourismPermit.status',
      header: 'srsaApp.technicalPermit.requestStatus',
      sortable: false,
    },
    { defaultField: 'submitDate', header: 'srsaApp.permitRequest.submitDate', sortable: false, type: 'date', format: 'formatMediumDate' },
    { defaultField: 'company.crName', header: 'srsaApp.permitRequest.company', sortable: false },
  ];

  ngOnInit(): void {
    this.subscription = combineLatest([this.activatedRoute.queryParamMap, this.activatedRoute.data])
      .pipe(
        tap(([params, data]) => this.fillComponentAttributeFromRoute(params, data)),
        tap(() => this.loadDraftRequests()),
      )
      .subscribe();

    this.accountService.identity().subscribe(
      account => {
        this.account = account;
        // Any additional code that depends on account can go here
      },
      error => {
        console.error('Error loading account:', error);
      },
    );
    this.filters.filterChanges.subscribe(filterOptions => this.handleNavigation(1, this.sortState(), filterOptions));
  }

  delete(tourismPermit: { action: string; item: ITourismPermit }): void {
    const modalRef = this.modalService.open(TourismPermitDeleteDialogComponent, { size: 'lg', backdrop: 'static' });
    modalRef.componentInstance.tourismPermit = tourismPermit.item;
    // unsubscribe not needed because closed completes on modal close
    modalRef.closed
      .pipe(
        filter(reason => reason === ITEM_DELETED_EVENT),
        tap(() => this.load()),
      )
      .subscribe();
  }
  // Method to get the class for a tab
  getTabClass(tabName: string): string {
    return this.activeTab === tabName ? 'figma-button-selected' : 'figma-button-sign';
  }
  loadAllInProgressRequests(): void {
    this.page = 0;
    this.allInProgressRequests();
  }

  allInProgressRequests(): void {
    this.activeTab = 'allRequests';
    this.criteria = {};
    const searchStatus = [];
    searchStatus.push('ACTIVE');
    searchStatus.push('ISSUED');
    searchStatus.push('PENDING_APPROVAL');

    this.searchByStatus(searchStatus);
  }
  loadDraftRequests(): void {
    this.page = 0;
    this.allDraftRequests();
  }

  allDraftRequests(): void {
    this.activeTab = 'inProgressRequests';
    let searchStatus = [];
    searchStatus.push('ISSUED');
    searchStatus.push('ACTIVE');
    searchStatus.push('EXPIRED');
    searchStatus.push('PENDING_APPROVAL');
    searchStatus.push('APPROVED');
    searchStatus.push('REJECTED');
    this.searchByStatus(searchStatus);
  }
  searchByStatus(searchStatus?: any): void {
    this.criteria = {};
    if (searchStatus != null && searchStatus !== '' && searchStatus !== undefined) {
      const requestStatusArray = Array.isArray(searchStatus) ? searchStatus : [searchStatus];
      const requestStatus = requestStatusArray.map(status => ({
        'requestStatus.equals': status,
      }));
      this.criteria = { ...this.criteria, requestStatus };
    }
    this.load();
  }
  onSearch(): void {
    if (this.searchRqNumber !== '') {
      this.criteria = {};
    }
    const requestNumber = {
      'requestNumber.contains': this.searchRqNumber,
    };
    this.criteria = { ...this.criteria, ...requestNumber };
    this.load();
  }
  load(): void {
    this.queryBackend().subscribe({
      next: (res: EntityArrayResponseType) => {
        this.onResponseSuccess(res);
      },
    });
  }

  navigateToWithComponentValues(event: SortState | any): void {
    this.handleNavigation(this.page, event, this.filters.filterOptions);
  }

  navigateToPage(page: any): void {
    this.page = page.page;
    this.handleNavigation(page, this.sortState(), this.filters.filterOptions);
  }

  protected fillComponentAttributeFromRoute(params: ParamMap, data: Data): void {
    const page = params.get(PAGE_HEADER);
    this.page = +(page ?? 0);
    this.sortState.set(this.sortService.parseSortParam(params.get(SORT) ?? data[DEFAULT_SORT_DATA]));
    this.filters.initializeFromParams(params);
  }

  protected onResponseSuccess(response: EntityArrayResponseType): void {
    this.fillComponentAttributesFromResponseHeader(response.headers);
    const dataFromBody = this.fillComponentAttributesFromResponseBody(response.body);

    this.tourismPermits = [...dataFromBody];
    this.getAllTableActions();
  }

  protected fillComponentAttributesFromResponseBody(data: ITourismPermit[] | null): ITourismPermit[] {
    return data ?? [];
  }

  protected fillComponentAttributesFromResponseHeader(headers: HttpHeaders): void {
    this.totalItems = Number(headers.get(TOTAL_COUNT_RESPONSE_HEADER));
  }

  protected queryBackend(): Observable<EntityArrayResponseType> {
    const { page, filters } = this;
    this.isLoading = true;

    const queryObject: any = {
      page: page,
      size: this.itemsPerPage,
      sort: this.sortService.buildSortParam(this.sortState()),
    };

    filters.filterOptions.forEach(filterOption => {
      queryObject[filterOption.nameAsQueryParam()] = filterOption.values;
    });

    // Add criteria to the query object
    if (this.criteria?.requestStatus && this.criteria?.requestStatus.length > 0) {
      this.criteria.requestStatus.forEach((status: any, index: number) => {
        queryObject[`requestStatus[${index}].equals`] = status['requestStatus.equals'];
      });
    }

    if (this.criteria?.['requestNumber.contains']) {
      queryObject['requestNumber.contains'] = this.criteria['requestNumber.contains'];
    }

    return this.tourismPermitService.query(queryObject).pipe(
      tap(() => {
        this.isLoading = false;
      }),
    );
  }

  protected handleNavigation(page: number, sortState: SortState, filterOptions?: IFilterOption[]): void {
    this.isLoading = true;

    this.load();
  }
  getDeletedRq(tourismPermit: any) {
    this.tourismPermit = tourismPermit;
  }
  updatePermit(id: number) {
    if (this.tourismPermit) {
      this.tourismPermit.requestStatus = TourismPermitStatusType.CANCELLED;
      // let tourismPermit: ITourismPermit;
      // tourismPermit.id = this.tourismPermit!.id;
      // tourismPermit.requestStatus = TourismPermitStatusType.CANCELLED;
      // tourismPermit. = this.tourismPermit.notes || '';
      this.tourismPermitService.update(this.tourismPermit).subscribe({
        next: res => {},
        error: (err: any) => {},
        complete() {},
      });
    }
  }
  previousState(): void {
    this.router.navigate(['/tourism-permit']);
  }
  isArabic(): boolean {
    let currentLanguage = this.translateService.currentLang;
    currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
    return currentLanguage.startsWith('ar', 0);
  }

  protected readonly TourismPermitStatusType = TourismPermitStatusType;
  protected readonly MarineStatusType = MarineStatusType;

  getTableActions(tourismPermit: ITourismPermit): any[] {
    const actions: TableAction[] = [{ label: 'entity.action.view', action: 'view', routerLink: '/tourism-permit', id: tourismPermit.id }];

    this.canEdit(tourismPermit).then(canEdit => {
      if (canEdit) {
        actions.push({
          label: 'entity.action.edit',
          action: 'edit',
          routerLink: '/tourism-permit',
          id: tourismPermit.id,
        });
      }
    });

    // if (this.canDelete(tourismPermit)) {
    //   actions.push({
    //     label: 'entity.action.delete',
    //     action: 'delete',
    //     id: tourismPermit.id,
    //     handler: () => {
    //       this.getDeletedRq(tourismPermit);
    //
    //       const modalElement = document.getElementById('deleteMTA');
    //       if (modalElement) {
    //         modalElement.setAttribute('aria-hidden', 'false');
    //         modalElement.setAttribute('tabindex', '1');
    //       }
    //     }
    //   });
    // }

    return actions;
  }

  async canEdit(tourismPermit: ITourismPermit): Promise<boolean> {
    // Check if marine medium ID is missing
    if (!tourismPermit?.marineMedium?.id) {
      return false;
    }

    // Check if user has required authority AND is the owner
    const hasRequiredRole = this.accountService.hasAnyAuthority([
      Authority.ROLE_SERVICE_PROVIDER,
      Authority.ROLE_INDIVIDUAL_USER_NON_FOREIGN,
    ]);

    const isOwner = this.account?.login === tourismPermit.requestUserOwner?.login;

    if (!hasRequiredRole || !isOwner) {
      return false;
    }

    // Check permit status conditions
    return (
      tourismPermit.requestStatus === TourismPermitStatusType.ACTIVE ||
      tourismPermit.requestStatus === TourismPermitStatusType.PENDING_APPROVAL ||
      (tourismPermit.requestStatus === TourismPermitStatusType.ISSUED && tourismPermit.departureMarineStatus === MarineStatusType.ANCHORED)
    );
  }
  canDelete(tourismPermit: ITourismPermit): boolean {
    return (
      tourismPermit.requestStatus === TourismPermitStatusType.PENDING_APPROVAL ||
      tourismPermit.requestStatus === TourismPermitStatusType.ISSUED
    );
  }

  getAllTableActions(): void {
    if (this.tourismPermits) {
      this.tableActions = this.tourismPermits?.map(permit => ({
        id: permit.id,
        actions: this.getTableActions(permit),
      }));
    }
  }
}
