package com.elm.srsa.mofa.client.request;

import java.util.List;

public class IssueVisaRequest {

    private List<VisaRequestApplicant> applicants;
    private String referenceNumber;

    // Constructors, getters, and setters
    public IssueVisaRequest() {}

    public IssueVisaRequest(List<VisaRequestApplicant> applicants, String referenceNumber) {
        this.applicants = applicants;
        this.referenceNumber = referenceNumber;
    }

    // Getters and Setters
    public List<VisaRequestApplicant> getApplicants() {
        return applicants;
    }

    public void setApplicants(List<VisaRequestApplicant> applicants) {
        this.applicants = applicants;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
}
