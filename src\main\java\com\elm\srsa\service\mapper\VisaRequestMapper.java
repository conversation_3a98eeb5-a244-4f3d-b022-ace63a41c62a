package com.elm.srsa.service.mapper;

import com.elm.srsa.domain.visa.VisaApplicant;
import com.elm.srsa.domain.visa.VisaRequest;
import com.elm.srsa.service.dto.VisaApplicantDTO;
import com.elm.srsa.service.dto.VisaRequestDTO;
import com.elm.srsa.service.dto.VisaRequestSummaryDTO;
import java.util.List;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface VisaRequestMapper {
    VisaRequest toEntity(VisaRequestDTO dto);

    VisaRequestDTO toDto(VisaRequest entity);

    List<VisaRequestDTO> toDtoList(List<VisaRequest> entities);
    List<VisaRequest> toEntityList(List<VisaRequestDTO> dtos);

    VisaApplicant toEntity(VisaApplicantDTO dto);
    VisaApplicantDTO toDto(VisaApplicant entity);
    VisaRequestSummaryDTO toSummaryDto(VisaRequest entity);

    List<VisaRequestSummaryDTO> toSummaryDtoList(List<VisaRequest> entities);
}
