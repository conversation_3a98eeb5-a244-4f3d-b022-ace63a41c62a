import { Component, inject, OnInit } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { ICompany } from 'app/entities/company/company.model';
import { CompanyService } from 'app/entities/company/service/company.service';
import { IUser } from 'app/entities/user/user.model';
import { UserService } from 'app/entities/user/service/user.service';
import { ILicenseProvider } from 'app/entities/license-provider/license-provider.model';
import { LicenseProviderService } from 'app/entities/license-provider/service/license-provider.service';
import { IServiceType } from 'app/entities/service-type/service-type.model';
import { ServiceTypeService } from 'app/entities/service-type/service/service-type.service';
import { ILicenseRequest } from 'app/entities/license-request/license-request.model';
import { LicenseRequestService } from 'app/entities/license-request/service/license-request.service';
import { StatusType } from 'app/entities/enumerations/status-type.model';
import { SaudizationLevel } from 'app/entities/enumerations/saudization-level.model';
import { PermitRequestService } from '../service/permit-request.service';
import { IPermitRequest, NewPermitRequest } from '../permit-request.model';
import { PermitRequestFormGroup, PermitRequestFormService } from './permit-request-form.service';
import { IInvestmentType } from '../../investment-type/investment-type.model';
import { InvestmentTypeService } from '../../investment-type/service/investment-type.service';
import { IOwner } from '../../company/owner.model';
import { INationalAddress } from '../../../account/register/model/national.address.model';
import { IMaritimeMedium } from '../../maritime-medium/maritime-medium.model';
import { MaritimeMediumService } from '../../maritime-medium/service/maritime-medium.service';
import { IMaritimeTourismServices } from '../../maritime-tourism-services/maritime-tourism-services.model';
import { MaritimeTourismServicesService } from '../../maritime-tourism-services/service/maritime-tourism-services.service';
import { ICountry } from '../../country/country.model';
import { CountryService } from '../../country/service/country.service';
import { IProvidedLanguages } from '../../provided-languages/provided-languages.model';
import { ProvidedLanguagesService } from '../../provided-languages/service/provided-languages.service';
import { LicenseDuration } from '../../enumerations/license-duration.model';
import { ServiceTypeDocumentTypeService } from '../../service-type-document-type/service/service-type-document-type.service';
import { IServiceTypeDocumentType } from '../../service-type-document-type/service-type-document-type.model';
import { IServiceTypeTermsConditions } from '../../service-type-terms-conditions/service-type-terms-conditions.model';
import { ServiceTypeTermsConditionsService } from '../../service-type-terms-conditions/service/service-type-terms-conditions.service';
import { ICompanyOwner } from '../../../account/register/model/company.owner.model';
import { CompanyFormGroup, CompanyFormService } from '../../company/update/company-form.service';
import { ILicenseRequestMaritimeMedium } from '../../license-request-maritime-medium/license-request-maritime-medium.model';
import { ILicenseRequestMaritimeServices } from '../../license-request-maritime-services/license-request-maritime-services.model';
import { ILicenseRequestCountry } from '../../license-request-country/license-request-country.model';
import { ILicenseRequestProvidedLanguages } from '../../license-request-provided-languages/license-request-provided-languages.model';
import { IMarineTourOperatorServices } from '../../marine-tour-operator-services/marine-tour-operator-services.model';
import { IFuelTypes } from '../../fuel-types/fuel-types.model';
import { FuelTypesService } from '../../fuel-types/service/fuel-types.service';
import { MarineTourOperatorServicesService } from '../../marine-tour-operator-services/service/marine-tour-operator-services.service';
import { ILicenseRequestMarineTourServices } from '../../license-request-marine-tour-services/license-request-marine-tour-services.model';
import { ILicenseRequestFuelTypes } from '../../license-request-fuel-types/license-request-fuel-types.model';
import { DropzoneConfigInterface, DropzoneModule } from 'ngx-dropzone-wrapper';
import { TranslateService } from '@ngx-translate/core';
import { IServiceTypeLiabilityType } from '../../service-type-liability-type/service-type-liability-type.model';
import { ServiceTypeLiabilityTypeService } from '../../service-type-liability-type/service/service-type-liability-type.service';

import { IAttachment } from '../../attachment/attachment.model';
import { MultiSelectModule } from 'primeng/multiselect';
import { IDocks } from '../../license-request/docks.model';
import { ILicenseRequestDocks } from '../license-request-docks.model';
import { IPermitChanges } from '../permit-changes.model';
import { WizardStepComponent } from '../../../layouts/wizard-step/wizard-step.component';
import { TabViewModule } from 'primeng/tabview';
import { PageTitleComponent } from 'app/layouts/sub/page-title/page-title.component';
import { PageTitleService } from 'app/core/util/page-title.service';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { SrsaAlertService } from '../../../shared/alert/srsa-alert/srsa.alert.service';
import { IBeachActivities } from '../beach-activities.model';
import { IBeachEquipment } from '../beach-equipment.model';

export type FileKeyValuePair = { key: string; file: File };

@Component({
  standalone: true,
  selector: 'jhi-permit-request-update',
  templateUrl: './permit-request-update.component.html',
  styleUrl: './permit-request-update.component.scss',
  imports: [
    SharedModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    GoogleMapsModule,
    DropzoneModule,
    WizardStepComponent,
    MultiSelectModule,
    TabViewModule,
    PageTitleComponent,
    AngularSvgIconModule,
  ],
})
export class PermitRequestUpdateComponent implements OnInit {
  isSuccess = false;
  editMode: boolean = false;
  noOfOwnedMaritimeMediumsKsaWorld = 0;
  noOfManagedMaritimeMediumsGeneral = 0;
  noOfManagedMaritimeMediums = 0;
  noOfCountryCompanyOpr = 0;
  noOfYearsExperience = 0;
  disclaimerChecked = false;
  liabilityChecked = false;
  haveMaritimeTourismAgent = false;
  permitRequest: IPermitRequest | null = null;
  permitChanges: any = {};
  previewPermitRequest: IPermitRequest | NewPermitRequest | null = null;
  licenseRequest?: ILicenseRequest | null = null;
  statusTypeValues = Object.keys(StatusType);
  requestTypeStatusEnum?: keyof typeof StatusType | null;
  saudizationLevelValues = Object.keys(SaudizationLevel);
  selectedSaudizationLevel?: keyof typeof SaudizationLevel | null;
  step = 0;
  marinaOwnerName?: string | null = null;
  marinaCrNumber?: string | null = null;
  marinaCrNameAr?: string | null = null;
  marinaCrNameEn?: string | null = null;
  numberOfDocks?: number | null = null;
  dockLength?: number | null = null;
  dockDepth?: number | null = null;
  noOfWaterStations?: number | null = null;
  noOfWaterStationsPerPlatform?: number | null = null;
  noOfFuelStations?: number | null = null;
  noOfWasteStations?: number | null = null;
  noOfWasteStationsPerPlatform?: number | null = null;
  noOfElectricChargingStations?: number | null = null;
  noOfControlTowers?: number | null = null;
  noOfMarinaFacilities?: number | null = null;
  boatsCapacity?: number | null = null;
  yachtsCapacity?: number | null = null;
  otherMaritimeMediumCapacity?: number | null = null;
  marinaLocation?: INationalAddress | null = null;
  maritimeTourismAgent?: ICompany | null = null;
  licenseMaritimeMedium: ILicenseRequestMaritimeMedium | null = null;
  licenseRequestMaritimeService: ILicenseRequestMaritimeServices | null = null;
  licenseRequestCountries: ILicenseRequestCountry | null = null;
  licenseRequestProvidedLanguages: ILicenseRequestProvidedLanguages | null = null;
  licenseMarineTourServices: ILicenseRequestMarineTourServices | null = null;
  licenseFuelTypes: ILicenseRequestFuelTypes | null = null;
  requiredCrField = false;
  crOwners: IOwner[] = [];
  companyOwners: any[] | null | undefined = [];
  registeredCompany: ICompany | null = null;
  managersAr: any | null = null;
  managersEn: any | null = null;
  workLocation: INationalAddress | null = null;
  companiesSharedCollection: ICompany[] = [];
  relatedCompanies: any[] = [];
  maritimeTourismAgentCompanies: ICompany[] = [];
  selectedMaritimeTourismAgent?: ICompany | null;
  subCompany: any | null = null;
  selectedSubCompany: ICompany | null = null;
  selectedLicenseProvider: ILicenseProvider | null = null;
  selectedServiceType: IServiceType | null = null;
  nationalAddressesSharedCollection: INationalAddress[] = [];
  usersSharedCollection: IUser[] = [];
  licenseProvidersSharedCollection: ILicenseProvider[] = [];
  licenseProvidersByCategory: ILicenseProvider[] = [];
  availableServiceTypeList: IServiceType[] = [];
  serviceTypesSharedCollection: IServiceType[] = [];
  licenseRequestsSharedCollection: ILicenseRequest[] = [];
  investmentTypes: IInvestmentType[] = [];
  selectedInvestmentType: IInvestmentType | null = null;
  invTypeId = 0;
  maritimeMediums?: IMaritimeMedium[];
  selectedMaritimeMediums?: IMaritimeMedium[] | null | undefined;
  selectedMaritimeMediumsId?: number[] | null | undefined;
  maritimeTourismServices?: IMaritimeTourismServices[] = [];
  selectedMaritimeTourismServices?: IMaritimeTourismServices[] | null;
  beachActivities?: IBeachActivities[] = [];
  selectedBeachActivities?: IBeachActivities[] = [];
  beachEquipments?: IBeachEquipment[] = [];
  selectedBeachEquipments?: IBeachEquipment[] = [];
  countries?: ICountry[] | null;
  selectedCountries?: ICountry[] | null;
  displayOtherTextField = false;
  otherTextValue?: string | null = '';
  marineTourOperatorServices?: IMarineTourOperatorServices[] | null;
  selectedMarineTourOperatorServices?: IMarineTourOperatorServices[] | null;
  permitNumber: number | undefined;
  fuelTypes?: IFuelTypes[] | null;
  selectedFuelTypes?: IFuelTypes[] | null;
  providedLanguages?: IProvidedLanguages[] | null;
  selectedProvidedLanguages?: IProvidedLanguages[] | null;
  licenseDurationValues = Object.keys(LicenseDuration);
  selectedlicenseDurationValues?: keyof typeof LicenseDuration | null;
  serviceTypeDocumentTypes?: IServiceTypeDocumentType[];
  serviceTypeTermsConditions?: IServiceTypeTermsConditions[];
  serviceTypeLiabilityType?: IServiceTypeLiabilityType[];
  hidden1: boolean = true;
  hidden2: boolean = false;
  hidden3: boolean = false;
  hidden4: boolean = false;
  hidden5: boolean = false;
  hidden6: boolean = false;
  hidden7: boolean = false;
  hidden8: boolean = false;
  hidden9: boolean = false;
  hidden10: boolean = false;
  hidden11: boolean = false;
  hidden12: boolean = false;
  hidden13: boolean = false;
  hidden14: boolean = false;
  hidden15: boolean = false;
  hidden16: boolean = false;
  hiddeCyberSecurityCertificateAttachment: boolean = false;
  hiddeCompanyProfileAttachment: boolean = false;
  hiddeProofOfOwnershipOfTheMarinaAttachment: boolean = false;
  hiddeAIdentityOfTheOwnerOfTheMarinaAttachment: boolean = false;
  hiddenCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach: boolean = false;
  hiddenSiteVerification: boolean = false;
  hiddenOccupancyCertificate: boolean = false;
  hiddenMaritimeSpacePlanning: boolean = false;
  hiddenLifeguardList: boolean = false;
  hiddenEnvironmentalPermit: boolean = false;
  hiddenSafetyManagementPlan: boolean = false;
  hiddenWaterSafetyEquipment: boolean = false;
  hiddenMarineVehiclesAndActivities: boolean = false;

  CrNeedRenewal = false;
  CrExpired = false;
  marineLocationSet = false;
  workLocationSet = false;
  step1: boolean = true;
  step2: boolean = false;
  step3: boolean = false;
  // protected liabilityTypeService = inject(LiabilityTypeService);
  zoom = 18;
  center = {
    lat: 24.746604039896134,
    lng: 46.724558262553245,
  };
  centerMarine = {
    lat: 24.746604039896134,
    lng: 46.724558262553245,
  };
  options: google.maps.MapOptions = {
    mapTypeId: 'hybrid',
    zoomControl: true,
    scrollwheel: true,
    disableDoubleClickZoom: true,
    zoom: this.zoom,
  };
  markers = [] as any;
  marinaMarkers = [] as any;
  markerOptions: google.maps.MarkerOptions = {
    draggable: false,
    animation: google.maps.Animation.DROP,
  };
  fileSizeError = false;
  fullInfoEn: any;
  fullInfoAr: any;

  subCompanyDetailsFields: any;

  getFileConfig(): DropzoneConfigInterface {
    return {
      maxFilesize: 5,
      clickable: true,
      addRemoveLinks: true,
      maxFiles: 1,
      acceptedFiles: '.pdf,.jpeg,.jpg,.png',
      dictDefaultMessage: this.isArabic() ? 'اضغط هنا لرفع الملف' : 'Press here to upload file',
      dictInvalidFileType: this.isArabic()
        ? 'لا تستطيع رفع ملف من هذه الصيغة. (الصيغ المسموحة: pdf, jpeg ,jpg ,png)'
        : 'Cannot upload this file. (Allowed extensions: pdf, jpeg ,jpg ,png)',
      dictFileTooBig: this.isArabic() ? 'الحجم الأقصى للصورة هو 5 MB' : 'Maximum image size is 5 MB',
      url: SERVER_API_URL + '/api/files',
      //   headers: {'X-XSRF-TOKEN': this.cookieService.get('XSRF-TOKEN')}
    };
  }
  getOtherFilesConfig(): DropzoneConfigInterface {
    return {
      maxFilesize: 50,
      clickable: true,
      addRemoveLinks: true,
      maxFiles: 10,
      acceptedFiles: '.pdf,.jpeg,.jpg,.png',
      dictDefaultMessage: this.isArabic() ? 'اضغط هنا لرفع الملف' : 'Press here to upload file',
      dictInvalidFileType: this.isArabic()
        ? 'لا تستطيع رفع ملف من هذه الصيغة. (الصيغ المسموحة: pdf, jpeg ,jpg ,png)'
        : 'Cannot upload this file. (Allowed extensions: pdf, jpeg ,jpg ,png)',
      dictFileTooBig: this.isArabic() ? 'الحجم الأقصى للملف هو 5 MB' : 'Maximum file size is 5 MB',
      url: SERVER_API_URL + '/api/files',
      //   headers: {'X-XSRF-TOKEN': this.cookieService.get('XSRF-TOKEN')}
    };
  }
  // @ViewChild('secondaryCr', { static: false }) secondaryCr: any;
  documents: FileKeyValuePair[] = [] as FileKeyValuePair[];
  documentTypes: any[] = [];
  CrAttachment: IAttachment | undefined;
  MainCRAttachment: IAttachment | undefined;
  exceptionalDocAttachment: IAttachment | undefined;
  CompanyNationalAddressAttachment: IAttachment | undefined;
  CopyWorkforceMinistryHRAttachment: IAttachment | undefined;
  InvestmentLicenseAttachment: IAttachment | undefined;
  BankGuaranteeAttachment: IAttachment | undefined;
  ZakatLicenseAttachment: IAttachment | undefined;
  InternationalSafetyManagementAttachment: IAttachment | undefined;
  CompanyFileAttachment: IAttachment | undefined;
  // protected termsConditionsTypeService = inject(TermsConditionsTypeService);
  ListMarineOwnedAttachment: IAttachment | undefined;
  ContractAuthorityAttachment: IAttachment | undefined;
  ConstructionPermitAttachment: IAttachment | undefined;
  OperatingContractAttachment: IAttachment | undefined;
  InsuranceCoverageAttachment: IAttachment | undefined;
  EnvironmentalLicenseAttachment: IAttachment | undefined;
  AdditionalAttachment: IAttachment[] = [];
  CyberSecurityCertificateAttachment: IAttachment | undefined;
  CompanyProfileAttachment: IAttachment | undefined;
  ProofOfOwnershipOfTheMarinaAttachment: IAttachment | undefined;
  AIdentityOfTheOwnerOfTheMarinaAttachment: IAttachment | undefined;
  CommercialRegistrationOrIdentityOfTheOwnerOfTheBeachAttachment: IAttachment | undefined;
  SiteVerificationAttachment: IAttachment | undefined;
  OccupancyCertificateAttachment: IAttachment | undefined;
  MaritimeSpacePlanningAttachment: IAttachment | undefined;
  LifeguardListAttachment: IAttachment | undefined;
  EnvironmentalPermitAttachment: IAttachment | undefined;
  SafetyManagementPlanAttachment: IAttachment | undefined;
  WaterSafetyEquipmentAttachment: IAttachment | undefined;
  MarineVehiclesAndActivitiesAttachment: IAttachment | undefined;

  Docks: IDocks[] = [];
  docksInvalid: boolean = true;
  steps: any[] = [
    { titleAr: 'السجل التجاري ', titleEn: 'CR' },
    { titleAr: 'العنوان', titleEn: 'Address' },
    { titleAr: 'بيانات الرخصة', titleEn: 'Permit Data' },
    { titleAr: 'بيانات إضافية', titleEn: 'Additional Information' },
    { titleAr: 'المرفقات', titleEn: 'Attachment' },
    { titleAr: 'معاينة الطلب', titleEn: 'Preview' },
  ];
  requiredDocuments = {
    maritimeTourismAgent: [
      {
        val_ar:
          'ضمان بنكي صادر من بنك محلي في المملكة العربية السعودية لايقل عن 500 الف ريال غير قابل للإلغاء وغير مقيد وغير مشروط وينتهي بعد أشهر من إنتهاء الترخيص',
        val_en:
          'A bank guarantee issued by a local bank in the Kingdom of Saudi Arabia of no less than 500,000 SAR, irrevocable, unrestricted, and unconditional, and valid for several months after the license expiration',
      },
      {
        val_ar: 'شهادة الامن السيبراني',
        val_en: 'Cybersecurity certificate',
      },
      {
        val_ar: 'رخصة الاستثمار (في حال كانت المنشأة أجنبية او مختلطة)',
        val_en: 'Investment license (if the entity is foreign or mixed)',
      },
      {
        val_ar: 'الملف التعريفي للشركة',
        val_en: 'Company profile',
      },
    ],
    yachtCharteringLicense: [
      {
        val_ar: 'شهادة الامتثال لنظام إدارة السلامة الدولية',
        val_en: 'International Safety Management (ISM) compliance certificate',
      },
      {
        val_ar: 'شهادة الامن السيبراني',
        val_en: 'Cybersecurity certificate',
      },
      {
        val_ar: 'عقد بين المنشأة ووكيل ملاحي سياحي مرخص من قبل الهيئة السعودية للبحر الاحمر',
        val_en: 'A contract between the entity and a maritime tourism agent licensed by the Saudi Red Sea Authority',
      },
      {
        val_ar: 'رخصة الاستثمار (في حال كانت المنشأة أجنبية او مختلطة)',
        val_en: 'Investment license (if the entity is foreign or mixed)',
      },
      {
        val_ar: 'الملف التعريفي للشركة',
        val_en: 'Company profile',
      },
    ],
    marinaOperator: [
      {
        val_ar: 'عقد تشغيل بين مالك المرسى ومشغل المرسى',
        val_en: 'Operating contract between the marina owner and the marina operator',
      },
      {
        val_ar: 'شهادة الامن السيبراني',
        val_en: 'Cybersecurity certificate',
      },
      {
        val_ar: 'التغطية التأمينية (تشمل مسؤولية الطرف الثالث كحد أدنى)',
        val_en: 'Insurance coverage (including third-party liability at a minimum)',
      },
      {
        val_ar: 'رخصة الاستثمار (في حال كانت المنشأة أجنبية او مختلطة)',
        val_en: 'Investment license (if the entity is foreign or mixed)',
      },
      {
        val_ar: 'الملف التعريفي للشركة',
        val_en: 'Company profile',
      },
      {
        val_ar: 'إثبات ملكية المرسى',
        val_en: 'Proof of marina ownership',
      },
      {
        val_ar: 'هوية مالك المرسى',
        val_en: "Marina owner's ID",
      },
    ],
    beachOperator: [
      {
        val_ar: 'رخصة الاستثمار (في حالة كانت المنشأة أجنبية أو مختلطة)',
        val_en: 'Investment license (if the entity is foreign or mixed)',
      },
      {
        val_ar: 'السجل التجاري أو الهوية لمالك الموقع (الشاطئ)',
        val_en: 'Commercial registration or identity of the owner of the site (beach)',
      },
      {
        val_ar: 'إثبات الملكية للموقع',
        val_en: 'Site verification',
      },
      {
        val_ar: 'شهادة الإشغال الصادرة عن الجهة ذات الاختصاص المكاني',
        val_en: 'An occupancy certificate issued by the competent local authority',
      },
      {
        val_ar: 'وثيقة تأمين ضد المسؤولية المدنية لمشغل الشاطئ صادرة عن شركة مرخصة من هيئة التأمين',
        val_en:
          'Obtaining a liability insurance policy for the beach operator issued by an insurance company licensed by the Insurance Authority',
      },
      {
        val_ar: 'تخطيط الحيز البحري لمناطق ممارسة الأنشطة البحرية في مياه الشواطئ',
        val_en: 'Maritime space planning for areas where maritime activities are carried out in coastal waters',
      },
      {
        val_ar: 'الملف التعريفي للمنشأة',
        val_en: 'Company Profile',
      },
      {
        val_ar: 'بيان بعدد وأسماء المنقذين',
        val_en: 'A statement of the number and names of lifeguards',
      },
      {
        val_ar: 'التصريح البيئي للتشغيل الصادر عن المركز الوطني للرقابة على الالتزام البيئي',
        val_en: 'An environmental operating permit issued by the NCEC',
      },
      {
        val_ar: 'خطة سلامة الشواطئ',
        val_en: 'Safety and health management plans',
      },
      {
        val_ar: 'بيان بأدوات وتجهيزات السلامة المائية للشواطئ',
        val_en: 'A statement of water safety tools and equipment for beaches',
      },
      {
        val_ar: 'بيان بعدد وتراخيص الوسائط البحرية، والأنشطة البحرية المرخصة التي تُمارس في مياه الشواطئ من الجهات المختصة',
        val_en:
          'A statement of the number and licenses of marine vehicles and licensed marine activities carried out in coastal waters by the competent authorities',
      },
      {
        val_ar: ' عقد التشغيل',
        val_en: ' Operating Contract',
      },
    ],
  };

  // {key: '', file: new File([],'')}

  // @ViewChild('InvestmentLicense', { static: false }) InvestmentLicense: ElementRef;
  currentStep = 1;
  wizardDisplay: boolean = false;
  showCrDetails: boolean = false;
  public alertService = inject(SrsaAlertService);
  protected permitRequestService = inject(PermitRequestService);
  protected permitRequestFormService = inject(PermitRequestFormService);
  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: PermitRequestFormGroup = this.permitRequestFormService.createPermitRequestFormGroup();
  protected companyFormService = inject(CompanyFormService);
  companyForm: CompanyFormGroup = this.companyFormService.createCompanyFormGroup();
  protected companyService = inject(CompanyService);
  protected userService = inject(UserService);
  protected licenseProviderService = inject(LicenseProviderService);
  protected serviceTypeService = inject(ServiceTypeService);
  protected licenseRequestService = inject(LicenseRequestService);
  protected activatedRoute = inject(ActivatedRoute);
  protected investmentTypeService = inject(InvestmentTypeService);
  protected maritimeMediumService = inject(MaritimeMediumService);
  protected maritimeTourismServicesService = inject(MaritimeTourismServicesService);
  protected countryService = inject(CountryService);
  protected providedLanguagesService = inject(ProvidedLanguagesService);
  protected serviceTypeDocumentTypeService = inject(ServiceTypeDocumentTypeService);
  protected serviceTypeTermsConditionsService = inject(ServiceTypeTermsConditionsService);
  protected serviceTypeLiabilityTypeService = inject(ServiceTypeLiabilityTypeService);
  protected fuelTypesService = inject(FuelTypesService);
  protected marineTourOperatorServicesService = inject(MarineTourOperatorServicesService);
  protected translateService = inject(TranslateService);
  protected pageTitleService = inject(PageTitleService);
  private criteria: any;

  compareCompany = (o1: ICompany | null, o2: ICompany | null): boolean => this.companyService.compareCompany(o1, o2);

  compareUser = (o1: IUser | null, o2: IUser | null): boolean => this.userService.compareUser(o1, o2);

  compareLicenseProvider = (o1: ILicenseProvider | null, o2: ILicenseProvider | null): boolean =>
    this.licenseProviderService.compareLicenseProvider(o1, o2);

  compareServiceType = (o1: IServiceType | null, o2: IServiceType | null): boolean => this.serviceTypeService.compareServiceType(o1, o2);

  compareLicenseRequest = (o1: ILicenseRequest | null, o2: ILicenseRequest | null): boolean =>
    this.licenseRequestService.compareLicenseRequest(o1, o2);

  ngOnInit(): void {
    this.center = {
      lat: 24.746604039896134,
      lng: 46.724558262553245,
    };
    this.activatedRoute.data.subscribe(({ permitRequest }) => {
      this.permitRequest = permitRequest;
      if (this.permitRequest?.requestNumber) {
        this.permitChanges = {};
        this.permitRequestService.getPermitChanges(this.permitRequest.requestNumber).subscribe((changes: IPermitChanges[]) => {
          if (changes && changes.length > 0) {
            changes.forEach((change: IPermitChanges) => {
              if (change.columnChanged) {
                this.permitChanges[change.columnChanged] = true;
              }
            });
          }
        });
      }
      if (permitRequest) {
        this.updateForm(permitRequest);
      } else {
        this.permitRequest = <IPermitRequest>{};
        this.permitRequest!.licenseRequest = <ILicenseRequest>{};
        this.selectedMaritimeTourismServices = [];
        this.selectedMaritimeMediums = [];
        this.selectedMaritimeMediumsId = [];
      }

      this.loadRelationshipsOptions();
      this.checkMaritimeTourismAgent();
    });

    this.pageTitleService.setBreadcrumb(this.translateService.instant('srsaApp.serviceType.home.title'), '/permit-request');
  }
  hideAllAttachment() {
    this.hidden1 = false;
    this.hidden2 = false;
    this.hidden3 = false;
    this.hidden4 = false;
    this.hidden5 = false;
    this.hidden6 = false;
    this.hidden7 = false;
    this.hidden8 = false;
    this.hidden9 = false;
    this.hidden10 = false;
    this.hidden11 = false;
    this.hidden12 = false;
    this.hidden13 = false;
    this.hidden14 = false;
    this.hidden15 = false;
    this.hidden16 = false;
    this.hiddeProofOfOwnershipOfTheMarinaAttachment = false;
    this.hiddeAIdentityOfTheOwnerOfTheMarinaAttachment = false;
    this.hiddeCompanyProfileAttachment = false;
    this.hiddeCyberSecurityCertificateAttachment = false;
    this.hiddenCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach = false;
    this.hiddenSiteVerification = false;
    this.hiddenOccupancyCertificate = false;
    this.hiddenMaritimeSpacePlanning = false;
    this.hiddenLifeguardList = false;
    this.hiddenEnvironmentalPermit = false;
    this.hiddenSafetyManagementPlan = false;
    this.hiddenWaterSafetyEquipment = false;
    this.hiddenMarineVehiclesAndActivities = false;
  }
  clickToShow1() {
    this.hideAllAttachment();
    this.hidden1 = true;
  }

  clickToShow2() {
    this.hideAllAttachment();
    this.hidden2 = true;
  }

  clickToShow3() {
    this.hideAllAttachment();
    this.hidden3 = true;
  }

  clickToShow4() {
    this.hideAllAttachment();
    this.hidden4 = true;
  }

  clickToShow5() {
    this.hideAllAttachment();
    this.hidden5 = true;
  }

  clickToShow6() {
    this.hideAllAttachment();
    this.hidden6 = true;
  }

  clickToShow7() {
    this.hideAllAttachment();
    this.hidden7 = true;
  }

  clickToShow8() {
    this.hideAllAttachment();
    this.hidden8 = true;
  }

  clickToShow9() {
    this.hideAllAttachment();
    this.hidden9 = true;
  }

  clickToShow10() {
    this.hideAllAttachment();
    this.hidden10 = true;
  }

  clickToShow11() {
    this.hideAllAttachment();
    this.hidden11 = true;
  }

  clickToShow12() {
    this.hideAllAttachment();
    this.hidden12 = true;
  }

  clickToShow13() {
    this.hideAllAttachment();
    this.hidden13 = true;
  }

  clickToShow14() {
    this.hideAllAttachment();
    this.hidden14 = true;
  }

  clickToShow15() {
    this.hideAllAttachment();
    this.hidden15 = true;
  }

  clickToShow16() {
    this.hideAllAttachment();
    this.hidden16 = true;
  }

  eventHandler(event: any, name: string) {
    // Add marker on double click event
    if (name === 'mapClick') {
      this.dropMarker(event.latLng.lat(), event.latLng.lng());
    }
  }

  eventHandlerMarine(event: any, name: string) {
    // Add marker on double click event
    if (name === 'mapClick') {
      this.dropMarkerMarine(event.latLng.lat(), event.latLng.lng());
    }
  }

  dropMarkerMarine(newLatitude: any, newLongitude: any) {
    this.marineLocationSet = true;
    this.marinaMarkers = [];
    const company = this.companyFormService.getCompany(this.companyForm);
    company.nationalAddress = <INationalAddress>{};
    company.nationalAddress.latitude = newLatitude;
    company.nationalAddress.longitude = newLongitude;
    this.marinaLocation = company.nationalAddress;
    this.marinaMarkers.push({
      position: {
        lat: newLatitude,
        lng: newLongitude,
      },
      label: {
        color: 'blue',
        text: 'العنوان',
      },
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      title: 'Address title ' + (this.markers.length + 1),
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      info: 'Address info ' + (this.markers.length + 1),
      options: {
        animation: google.maps.Animation.DROP,
      },
    });
  }

  dropMarker(newLatitude: any, newLongitude: any) {
    this.workLocationSet = true;
    this.markers = [];
    const company = this.companyFormService.getCompany(this.companyForm);
    company.nationalAddress = <INationalAddress>{};
    company.nationalAddress.latitude = newLatitude;
    company.nationalAddress.longitude = newLongitude;
    this.workLocation = company.nationalAddress;
    this.markers.push({
      position: {
        lat: newLatitude,
        lng: newLongitude,
      },
      label: {
        color: 'blue',
        text: 'العنوان',
      },
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      title: 'Address title ' + (this.markers.length + 1),
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      info: 'Address info ' + (this.markers.length + 1),
      options: {
        animation: google.maps.Animation.DROP,
      },
    });
  }

  trackMaritimeMedium(_index: number, item: IMaritimeMedium): string {
    return item.code!;
  }

  trackIdentity(_index: number, item: ICompanyOwner): string {
    return item.name!;
  }

  trackIdentityDocType(_index: number, item: IServiceTypeDocumentType): number {
    return item.id!;
  }

  trackIdentityTerms(_index: number, item: IServiceTypeTermsConditions): number {
    return item.id!;
  }

  trackIdentityLiability(_index: number, item: IServiceTypeLiabilityType): number {
    return item.id!;
  }

  previousState(): void {
    window.history.back();
  }

  isArabic() {
    if (this.translateService && this.translateService!.currentLang) {
      let currentLanguage = this.translateService!.currentLang;
      currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
      return currentLanguage.startsWith('ar', 0);
    }
    return 'ar';
  }

  nextStep(stepNumber: number): void {
    this.step = stepNumber;
  }

  nextStepSelected(serviceType: IServiceType | null) {
    this.selectedServiceType = serviceType;
    this.loadServiceTypesRelations();
    this.step = 1;
  }

  prepareRequestStatus(status: string): void {
    if (status == 'DRAFT') {
      this.requestTypeStatusEnum = StatusType.DRAFT;
    }
    if (status == 'PENDING_REVIEW') {
      this.requestTypeStatusEnum = StatusType.PENDING_REVIEW;
    }
    if (status == 'RETURNED') {
      if (this.permitRequest?.requestStatus == 'RETURNED_LICENSING_MANAGER') {
        this.requestTypeStatusEnum = StatusType.PENDING_MANAGEMENT_LICENSING;
      } else if (this.permitRequest?.requestStatus == 'RETURNED_LICENSING_OFFICER') {
        this.requestTypeStatusEnum = StatusType.UNDER_PROCESS;
      } else if (this.permitRequest?.requestStatus == 'RETURNED_INSPECTION_OFFICER') {
        this.requestTypeStatusEnum = StatusType.UNDER_INSPECTION;
      }
    }
  }

  updateCheckedOptions(option: any) {
    if (this.selectedMaritimeTourismServices?.findIndex(s => s.id === option) === -1) {
      this.selectedMaritimeTourismServices?.push(option);
    } else {
      this.selectedMaritimeTourismServices?.splice(this.selectedMaritimeTourismServices?.findIndex(s => s.id === option), 1);
    }
  }

  updateCheckedMaritimeMediums(option: IMaritimeMedium, event: any) {
    if (event.target.checked === true) {
      this.selectedMaritimeMediums?.push(option);
    } else {
      this.selectedMaritimeMediums?.splice(this.selectedMaritimeMediums?.findIndex(s => s.id == option.id), 1);
      if (option.id == 1) {
        this.selectedMaritimeTourismServices?.splice(this.selectedMaritimeTourismServices?.findIndex(s => s.id === 1), 1);
      } else if (option.id == 8) {
        this.selectedMaritimeTourismServices?.splice(this.selectedMaritimeTourismServices?.findIndex(s => s.id === 12), 1);
      } else if (option.id == 2) {
        this.selectedMaritimeTourismServices?.splice(this.selectedMaritimeTourismServices?.findIndex(s => s.id === 2), 1);
      }
    }
    this.selectedMaritimeMediumsId = this.selectedMaritimeMediums?.map(s => s.id);
    this.prepareMaritimeTourismServices(option.id, event.target.checked);
    if (option.id == 8) {
      this.displayOtherTextField = !this.displayOtherTextField;
    }
    console.log(this.selectedMaritimeTourismServices);
  }

  checkMandatoryFields(option: any) {
    console.log(option);
    option.checked = true;
    //return false;
  }

  prepareMaritimeTourismServices(option: any, isChecked: any) {
    this.maritimeTourismServices = [];
    // this.selectedMaritimeTourismServices=[];
    for (let selectedMaritime of this.selectedMaritimeMediums!) {
      for (let mMaritime of this.maritimeMediums!) {
        if (isChecked != null && isChecked == false && mMaritime.id == option) {
          for (let tourismMaritime of mMaritime!.maritimeTourismServices!) {
            if (this.selectedMaritimeTourismServices?.findIndex(s => s.id === tourismMaritime.id) !== -1) {
              this.selectedMaritimeTourismServices?.splice(
                this.selectedMaritimeTourismServices?.findIndex(s => s.id === tourismMaritime.id),
                1,
              );
            }
          }
        }
        if (mMaritime.id == selectedMaritime.id) {
          for (let tourismMaritime of mMaritime!.maritimeTourismServices!) {
            this.maritimeTourismServices.push(tourismMaritime);
            if (
              isChecked == true &&
              (tourismMaritime.id == 1 || tourismMaritime.id == 12) &&
              this.selectedMaritimeTourismServices?.findIndex(s => s.id === tourismMaritime.id) === -1
            ) {
              tourismMaritime.itemDisabled = true;
              this.selectedMaritimeTourismServices?.push(tourismMaritime);
            }

            // if (this.selectedMaritimeMediums?.indexOf(mMaritime.id) === -1) {
            //   this.selectedMaritimeTourismServices?.splice(this.selectedMaritimeTourismServices?.indexOf(tourismMaritime.id), 1);
            // }
            // if (this.selectedMaritimeTourismServices?.indexOf(tourismMaritime.id) === -1) {
            //   this.selectedMaritimeTourismServices?.splice(this.selectedMaritimeTourismServices?.indexOf(tourismMaritime.id), 1);
            // }
          }
          // break;
        }
      }
    }
  }

  save(): void {
    this.isSuccess = true;
    const permitRequest = this.permitRequestFormService.getPermitRequest(this.editForm);
    permitRequest.requestStatus = this.requestTypeStatusEnum;
    permitRequest!.licenseRequest = this.permitRequest?.licenseRequest;
    permitRequest.serviceType = this.selectedServiceType;
    permitRequest.secondaryCompany = this.selectedSubCompany;
    permitRequest.branchDto = this.subCompany;
    permitRequest.companyOwners = this.companyOwners;
    permitRequest.workLocation = this.workLocation;
    permitRequest.licenseRequest!.requestedLicenseDuration = this.selectedlicenseDurationValues;
    permitRequest.licenseRequest!.licenseRequestDocks = <ILicenseRequestDocks[]>[];

    if (this.selectedServiceType?.code === 'BO') {
      permitRequest.licenseRequest!.beachActivities = this.selectedBeachActivities;
      permitRequest.licenseRequest!.beachEquipment = this.selectedBeachEquipments;
    }

    if (this.permitRequest != null && this.permitRequest?.totalFundingOfProject != null) {
      permitRequest.totalFundingOfProject = this.permitRequest?.totalFundingOfProject;
    }
    this.Docks.forEach(dock => {
      permitRequest.licenseRequest!.licenseRequestDocks?.push(<ILicenseRequestDocks>{ docks: dock });
    });
    permitRequest.licenseRequest!.licenseRequestMarineTourServices = <ILicenseRequestMarineTourServices[]>[];
    permitRequest.licenseRequest!.maritimeTourismAgent = this.maritimeTourismAgentCompanies.find(
      i => i.id === this.selectedMaritimeTourismAgent?.id,
    );
    if (this.selectedMarineTourOperatorServices) {
      for (let mServices of this.selectedMarineTourOperatorServices) {
        this.licenseMarineTourServices = <ILicenseRequestMarineTourServices>{};
        this.licenseMarineTourServices!.marineTourOperatorServices = <IMarineTourOperatorServices>{};
        this.licenseMarineTourServices!.marineTourOperatorServices!.id = mServices.id;
        permitRequest.licenseRequest!.licenseRequestMarineTourServices.push(this.licenseMarineTourServices);
      }
    }

    permitRequest.licenseRequest!.licenseRequestFuelTypes = <ILicenseRequestFuelTypes[]>[];
    if (this.selectedFuelTypes) {
      for (let fuelType of this.selectedFuelTypes) {
        this.licenseFuelTypes = <ILicenseRequestFuelTypes>{};
        this.licenseFuelTypes!.fuelTypes = <IFuelTypes>{};
        this.licenseFuelTypes!.fuelTypes!.id = fuelType.id;
        permitRequest.licenseRequest!.licenseRequestFuelTypes.push(this.licenseFuelTypes);
      }
    }
    permitRequest.licenseRequest!.licenseRequestMaritimeMediums = <ILicenseRequestMaritimeMedium[]>[];
    if (this.selectedMaritimeMediums) {
      for (let maritimeMediumItem of this.selectedMaritimeMediums) {
        this.licenseMaritimeMedium = <ILicenseRequestMaritimeMedium>{};
        this.licenseMaritimeMedium!.maritimeMedium = <IMaritimeMedium>{};
        this.licenseMaritimeMedium!.maritimeMedium!.id = maritimeMediumItem.id;
        this.licenseMaritimeMedium!.licenseRequest = null;
        this.licenseMaritimeMedium!.otherValue = this.otherTextValue;
        permitRequest.licenseRequest!.licenseRequestMaritimeMediums.push(this.licenseMaritimeMedium);
      }
    }

    // permitRequest.licenseRequest!.licenseRequestMaritimeServices=this.selectedMaritimeTourismServices;
    permitRequest.licenseRequest!.licenseRequestMaritimeServices = <ILicenseRequestMaritimeServices[]>[];
    if (this.selectedMaritimeTourismServices) {
      for (let selectedMaritimeTourismServ of this.selectedMaritimeTourismServices) {
        this.licenseRequestMaritimeService = <ILicenseRequestMaritimeServices>{};
        this.licenseRequestMaritimeService!.maritimeTourismServices = <IMaritimeTourismServices>{};
        this.licenseRequestMaritimeService!.maritimeTourismServices!.id = selectedMaritimeTourismServ.id;
        this.licenseRequestMaritimeService!.licenseRequest = null;
        permitRequest.licenseRequest!.licenseRequestMaritimeServices.push(this.licenseRequestMaritimeService);
      }
    }
    // permitRequest.licenseRequest!.licenseRequestProvidedLanguages=this.selectedProvidedLanguages;

    permitRequest.licenseRequest!.licenseRequestProvidedLanguages = <ILicenseRequestProvidedLanguages[]>[];
    if (this.selectedProvidedLanguages) {
      for (let selectedProvidedLanguage of this.selectedProvidedLanguages) {
        this.licenseRequestProvidedLanguages = <ILicenseRequestProvidedLanguages>{};
        this.licenseRequestProvidedLanguages!.providedLanguages = <IProvidedLanguages>{};
        this.licenseRequestProvidedLanguages!.providedLanguages!.id = selectedProvidedLanguage.id;
        permitRequest.licenseRequest!.licenseRequestProvidedLanguages.push(this.licenseRequestProvidedLanguages);
      }
    }

    // permitRequest.licenseRequest!.licenseRequestCountries=this.selectedCountries;
    permitRequest.licenseRequest!.licenseRequestCountries = <ILicenseRequestCountry[]>[];
    if (this.selectedCountries) {
      for (let selectedCount of this.selectedCountries) {
        this.licenseRequestCountries = <ILicenseRequestCountry>{};
        this.licenseRequestCountries!.countryOfWork = <ICountry>{};
        this.licenseRequestCountries!.countryOfWork!.id = selectedCount.id;
        permitRequest.licenseRequest!.licenseRequestCountries.push(this.licenseRequestCountries);
      }
    }
    permitRequest.licenseRequest!.marinaLocation = this.marinaLocation;
    if (permitRequest.id !== null) {
      permitRequest.attachments = this.permitRequest!.attachments;
      this.subscribeToSaveUpdateResponse(this.permitRequestService.update(permitRequest, this.documents));
    } else {
      this.subscribeToSaveResponse(this.permitRequestService.create(permitRequest, this.documents));
    }
  }

  public onUploadFileSuccess(event: any, fileType: string): void {
    // If we update the attachment we remove already exist file
    if (this.permitRequest?.id !== null && this.permitRequest?.id !== undefined && this.permitRequest!.attachments!) {
      for (const attachment of this.permitRequest!.attachments!) {
        if (attachment.attachmentType?.code === fileType && attachment.attachmentType.code !== 'AdditionalAttachment') {
          attachment.isRemoved = true;

          if (this.documents.some(doc => doc.key === fileType)) {
            this.documents.splice(
              this.documents.findIndex(doc => doc.key === fileType),
              1,
            );
          }
        }
      }
    }
    const args = event;
    this.fileSizeError = false;
    this.documents.push({ key: fileType, file: new File([args], args.name, { type: args.type }) });
    if (this.documents && this.documents.length > 0) {
      this.documentTypes = [];
      this.documents.forEach(doc => (this.documentTypes.indexOf(doc.key) < 0 ? this.documentTypes.push(doc.key) : null));
    }
  }

  public onUploadFileError(event: any): void {
    this.fileSizeError = true;
  }

  public onFileRemoved(event: any, fileType: string): void {
    if (fileType === 'AdditionalAttachment' && this.documents.findIndex(doc => doc.key === fileType) !== -1) {
      this.documents.splice(
        this.documents.findIndex(doc => doc.file.name === event.name),
        1,
      );
    } else if (this.documents.findIndex(doc => doc.key === fileType) !== -1) {
      this.documents.splice(
        this.documents.findIndex(doc => doc.key === fileType),
        1,
      );
    }
    // this.document = undefined;
  }

  updateDocks() {
    this.permitRequest?.licenseRequest?.licenseRequestDocks?.forEach(LRD => {
      if (LRD.docks) {
        this.Docks.push(LRD.docks);
      }
    });
    this.checkDocks();
  }

  deleteSavedFile(attachment: IAttachment) {
    for (const savedAttachment of this.permitRequest!.attachments!) {
      if (savedAttachment.id === attachment.id) {
        savedAttachment.isRemoved = true;
        this.AdditionalAttachment.splice(this.AdditionalAttachment.indexOf(attachment), 1);
      }
    }
  }

  getAttachment(attachmentId: number): void {
    this.permitRequestService.getAttachmentById(attachmentId).subscribe({
      next(response) {
        const url = window.URL.createObjectURL(response);
        window.open(url);
      },
      error: () => {
        this.alertService.addAlert({
          type: 'danger',
          translationKey: 'srsaApp.profile.error.attachmentError',
          source: 'main',
        });
      },
    });
  }

  wizardNextStep() {
    if (this.currentStep < this.steps.length) this.currentStep++;
  }

  // Simple helper methods for common validations
  private isNullOrEmpty(value: any): boolean {
    return value === null || value === undefined || (Array.isArray(value) && value.length < 1);
  }

  private isNullOrUndefined(value: any): boolean {
    return value === null || value === undefined;
  }

  private isInValidRange(value: number | null | undefined, min: number, max: number): boolean {
    return value !== null && value !== undefined && (value < min || value > max);
  }

  private hasFormFieldError(fieldName: string): boolean {
    return this.editForm.get(fieldName)?.invalid || false;
  }

  private hasRequiredDoc(docKey: string): boolean {
    return this.documents.some(doc => doc.key === docKey);
  }

  // Optimized validation methods
  checkStep1(): boolean {
    if (!this.requiredCrField || this.invTypeId === 1) return false;

    const provider = this.selectedLicenseProvider?.code;
    const licenseFields: { [key: string]: string } = {
      MISA: 'misaLicenseNumber',
      ECZA: 'eczaLicenseNumber',
    };

    if (!provider) return true;
    return provider in licenseFields && this.hasFormFieldError(licenseFields[provider]);
  }

  checkStep3(): boolean {
    const request = this.permitRequest?.licenseRequest;
    const serviceCode = this.selectedServiceType?.code;
    const funding = this.permitRequest?.totalFundingOfProject;
    const managedMediums = request?.noOfManagedMaritimeMediumsGeneral;

    return (
      // Fields that DON'T apply to BO
      (serviceCode !== 'BO' && this.isNullOrEmpty(this.selectedProvidedLanguages)) ||
      (serviceCode !== 'BO' && this.isNullOrUndefined(funding)) ||
      (serviceCode !== 'BO' && this.isInValidRange(funding, 0, 9999999999999999999)) ||
      (serviceCode !== 'BO' && this.isNullOrUndefined(request?.noOfYearsExperience)) ||
      (serviceCode !== 'BO' && this.isInValidRange(request?.noOfYearsExperience, 1, 999)) ||
      (serviceCode !== 'BO' &&
        managedMediums !== null &&
        managedMediums !== undefined &&
        this.isInValidRange(managedMediums, 1, 99999999)) ||
      (serviceCode !== 'BO' && this.isNullOrUndefined(this.selectedCountries)) ||
      // Fields that apply to ALL service types (including BO)
      this.isNullOrUndefined(this.selectedlicenseDurationValues) ||
      // Service-specific validations (existing)
      (serviceCode && ['MA', 'TO', 'MO'].includes(serviceCode) && this.isNullOrEmpty(this.selectedMaritimeMediums)) ||
      (serviceCode &&
        ['CO', 'YC'].includes(serviceCode) &&
        this.isNullOrUndefined(this.selectedMaritimeTourismAgent) &&
        !this.haveMaritimeTourismAgent) ||
      (serviceCode === 'MA' && this.isNullOrEmpty(this.selectedMaritimeTourismServices)) ||
      (serviceCode === 'MO' && !this.marineLocationSet) ||
      // BO-specific validations
      (serviceCode === 'BO' && this.isNullOrEmpty(this.selectedBeachEquipments)) ||
      (serviceCode === 'BO' && this.isNullOrEmpty(this.selectedBeachActivities)) ||
      (serviceCode === 'BO' && !this.marineLocationSet) ||
      (serviceCode === 'BO' &&
        this.isNullOrUndefined(this.permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia)) ||
      (serviceCode === 'BO' &&
        this.permitRequest?.licenseRequest?.previouslyOperatedBeachInsideOrOutsideSaudiArabia === true &&
        this.isNullOrUndefined(this.selectedCountries))
    );
  }

  checkStep4(): boolean {
    const formFields = [
      'vatRegistrationNumber',
      'contactOfficerMobileNumber',
      'contactOfficerEmailAddress',
      'contactOfficerName',
      'rateRange',
      'selectedSaudizationLevel',
      'numberOfFemaleEmployees',
      'numberOfMaleEmployees',
      'ministryOfHumanResourcesNumber',
      'foreignersLaborCount',
    ];

    const hasFormErrors = formFields.some(field => this.hasFormFieldError(field));
    const hasCustomErrors = this.maleFemaleTotalError() || this.totalWorkersError();
    const hasMarinaErrors = this.selectedServiceType?.code === 'MO' && this.validateMarinaFields();
    const hasBeachOperatorErrors = this.selectedServiceType?.code === 'BO' && this.validateBeachOperatorFields();

    return hasFormErrors || hasCustomErrors || hasMarinaErrors || hasBeachOperatorErrors;
  }

  checkStep5(): boolean {
    const serviceCode = this.selectedServiceType?.code;
    const requiredDocsByService: { [key: string]: string[] } = {
      MA: ['BankGuarantee', 'COMPANY_PROFILE', 'CYBER_SECURITY_CERTIFICATE'],
      YC: ['InternationalSafetyManagement', 'COMPANY_PROFILE', 'CYBER_SECURITY_CERTIFICATE'],
      TO: ['COMPANY_FILE'],
      MO: [
        'OperatingContract',
        'InsuranceCoverage',
        'PROOF_OF_OWNERSHIP_OF_THE_MARINA',
        'IDENTITY_OF_THE_OWNER_OF_THE_MARINA',
        'COMPANY_PROFILE',
        'CYBER_SECURITY_CERTIFICATE',
      ],
      BO: [
        'OperatingContract',
        'InsuranceCoverage',
        'COMPANY_PROFILE',
        'COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH',
        'SITE_VERIFICATION',
        'OCCUPANCY_CERTIFICATE',
        'MARITIME_SPACE_PLANNING',
        'LIFEGUARDS_LIST',
        'ENVIRONMENTAL_PERMIT',
        'SAFETY_MANAGEMENT_PLAN',
        'WATER_SAFETY_EQUIPMENT',
        'MARINE_VEHICLES_AND_ACTIVITIES',
      ],
    };

    return (
      this.fileSizeError ||
      (this.invTypeId !== 0 && this.invTypeId !== 1 && !this.hasRequiredDoc('InvestmentLicense')) ||
      (serviceCode && ['YC', 'CO'].includes(serviceCode) && !this.haveMaritimeTourismAgent && !this.hasRequiredDoc('ContractAuthority')) ||
      (serviceCode && requiredDocsByService[serviceCode]?.some((doc: string) => !this.hasRequiredDoc(doc))) ||
      false
    );
  }

  // Helper for marina-specific validation
  private validateMarinaFields(): boolean {
    const request = this.permitRequest?.licenseRequest;
    if (!request) return true;

    const arabicPattern = /^[\u0621-\u064A 0-9]*$/;
    const englishPattern = /^[a-zA-Z 0-9]*$/;

    const stringFields = [
      { value: request.marinaOwnerNameAr, pattern: arabicPattern },
      { value: request.marinaOwnerNameEn, pattern: englishPattern },
      { value: request.marinaCrNameEn, pattern: englishPattern },
      { value: request.marinaCrNameAr, pattern: arabicPattern },
    ];

    const numericFields = [
      { value: request.numberOfDocks, min: 0, max: 10 },
      { value: request.noOfFuelStations, min: 0, max: 9999 },
      { value: request.noOfControlTowers, min: 0, max: 9999 },
      { value: request.noOfMarinaFacilities, min: 0, max: 99999 },
      { value: request.boatsCapacity, min: 0, max: 9999 },
      { value: request.yachtsCapacity, min: 0, max: 9999 },
      { value: request.numberOfOtherMarineVessels, min: 0, max: 9999 },
    ];

    const hasStringErrors = stringFields.some(({ value, pattern }) => !value || value.length < 1 || !pattern.test(value));

    const hasNumericErrors = numericFields.some(
      ({ value, min, max }) => value === null || value === undefined || value < min || value > max,
    );

    const needsFuelTypes = (request.noOfFuelStations ?? 0) > 0 && this.isNullOrEmpty(this.selectedFuelTypes);

    return hasStringErrors || hasNumericErrors || this.docksInvalid || needsFuelTypes;
  }

  private validateBeachOperatorFields(): boolean {
    const request = this.permitRequest?.licenseRequest;
    if (!request) return true;

    // Required string fields
    const stringFields = [request.beachOwnerName, request.beachTradeName, request.beachOwnerCommercialRegistrationNumber];

    // Required numeric fields
    const numericFields = [
      { value: request.numberOfRestrooms, min: 0, max: 9999 },
      { value: request.numberOfLifeguards, min: 0, max: 9999 },
      { value: request.numberOfFoodTruckParking, min: 0, max: 9999 },
      { value: request.visitorCapacity, min: 0, max: 999999 },
      { value: request.noOfWaterStations, min: 0, max: 9999 },
    ];

    // Required boolean fields
    const booleanFields = [request.isThereCabinsForRent, request.isThereMarineVessel];

    // Check string fields
    const hasStringErrors = stringFields.some(value => this.isNullOrUndefined(value) || (typeof value === 'string' && value.length < 1));

    // Check numeric fields
    const hasNumericErrors = numericFields.some(
      ({ value, min, max }) => this.isNullOrUndefined(value) || (value !== null && value !== undefined && (value < min || value > max)),
    );

    // Check boolean fields
    const hasBooleanErrors = booleanFields.some(value => this.isNullOrUndefined(value));

    // Conditional validation: numberOfCabinsAndRooms required if isThereCabinsForRent is true
    const cabinsValidationError =
      request.isThereCabinsForRent === true &&
      (this.isNullOrUndefined(request.numberOfCabinsAndRooms) ||
        (request.numberOfCabinsAndRooms !== null &&
          request.numberOfCabinsAndRooms !== undefined &&
          (request.numberOfCabinsAndRooms < 0 || request.numberOfCabinsAndRooms > 9999)));

    // Conditional validation: numberOfOtherMarineVessel required if isThereMarineVessel is true
    const marineVesselValidationError =
      request.isThereMarineVessel === true &&
      (this.isNullOrUndefined(request.numberOfOtherMarineVessel) ||
        (request.numberOfOtherMarineVessel !== null &&
          request.numberOfOtherMarineVessel !== undefined &&
          (request.numberOfOtherMarineVessel < 0 || request.numberOfOtherMarineVessel > 9999)));

    return hasStringErrors || hasNumericErrors || hasBooleanErrors || cabinsValidationError || marineVesselValidationError;
  }

  wizardPrevStep() {
    if (this.currentStep > 0) this.currentStep--;
  }

  showWizard() {
    this.wizardDisplay = true;
  }

  checkMaritimeTourismAgent() {
    for (const maritimeTourismAgentCompany of this.maritimeTourismAgentCompanies) {
      if (maritimeTourismAgentCompany.id === this.registeredCompany?.id) {
        this.haveMaritimeTourismAgent = true;
      }
    }
  }

  checkInvestmentInput() {
    if (
      (this.requiredCrField &&
        this.invTypeId !== 1 &&
        this.selectedLicenseProvider?.code == 'MISA' &&
        this.editForm.get('misaLicenseNumber')?.invalid) ||
      (this.requiredCrField &&
        this.invTypeId !== 1 &&
        this.selectedLicenseProvider?.code == 'ECZA' &&
        this.editForm.get('eczaLicenseNumber')?.invalid) ||
      (this.requiredCrField &&
        this.invTypeId !== 1 &&
        (this.selectedLicenseProvider?.code === null || this.selectedLicenseProvider?.code === undefined))
    ) {
      return true;
    }
    return false;
  }

  isVatNumberValid() {
    if (
      this.editForm.get('vatRegistrationNumber')!.invalid &&
      (this.editForm.get('vatRegistrationNumber')!.dirty || this.editForm.get('vatRegistrationNumber')!.touched) &&
      this.editForm.get('vatRegistrationNumber')!.errors!.pattern
    ) {
      return true;
    }
    return false;
  }

  docksCount(count: any) {
    this.Docks = [];
    if (count && count > 0) {
      if (count > 10) {
        count = 10;
        this.permitRequest!.licenseRequest!.numberOfDocks = 10;
      }
      for (let x = 0; x < count; x++) {
        this.Docks.push(<ILicenseRequestDocks>{});
      }
    }
  }
  checkDocks(): any {
    this.docksInvalid = true;
    this.Docks.forEach(dock => {
      if (
        dock.dockDepth === null ||
        dock.dockDepth === undefined ||
        dock.dockDepth < 0 ||
        dock.dockDepth > 99999 ||
        dock.dockWidth === null ||
        dock.dockWidth === undefined ||
        dock.dockWidth < 0 ||
        dock.dockWidth > 99999 ||
        dock.dockLength === null ||
        dock.dockLength === undefined ||
        dock.dockLength < 0 ||
        dock.dockLength > 9999999 ||
        dock.noOfWaterStations === null ||
        dock.noOfWaterStations === undefined ||
        dock.noOfWaterStations < 0 ||
        dock.noOfWaterStations > 9999 ||
        dock.noOfWasteStations === null ||
        dock.noOfWasteStations === undefined ||
        dock.noOfWasteStations < 0 ||
        dock.noOfWasteStations > 9999 ||
        dock.noOfElectricChargingStations === null ||
        dock.noOfElectricChargingStations === undefined ||
        dock.noOfElectricChargingStations < 0 ||
        dock.noOfElectricChargingStations > 9999
      ) {
        this.docksInvalid = true;
      } else {
        this.docksInvalid = false;
      }
    });
  }

  compareMaritimeTourismAgent = (o1: ICompany | null, o2: ICompany | null): boolean => this.companyService.compareCompany(o1, o2);

  protected subscribeToSaveResponse(result: Observable<HttpResponse<IPermitRequest>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: (res: any) => {
        this.permitNumber = res.body.requestNumber;
      },
      error: () => this.onSaveError(),
    });
  }

  protected subscribeToSaveUpdateResponse(result: Observable<HttpResponse<IPermitRequest>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: (res: any) => {
        if (res.body.requestNumber != null) {
          this.permitNumber = res.body.requestNumber;
          this.onSaveSuccess();
        }
      },
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    // if (this.requestTypeStatusEnum === StatusType.DRAFT || this.requestTypeStatusEnum === StatusType.PENDING_MANAGEMENT_LICENSING) {
    //   this.previousState();
    // }
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSuccess = true;
  }

  protected updateForm(permitRequest: IPermitRequest): void {
    this.permitRequest = permitRequest;

    //this.loadServiceTypesRelations();
    // if (this.permitRequest.companyOwners) {
    if (this.permitRequest.company && this.permitRequest!.company!.companyOwners) {
      this.companyOwners = this.permitRequest!.company!.companyOwners;
      for (let compOwner of this.permitRequest!.company!.companyOwners) {
        if (compOwner.nationalityCode != 'SA') {
          this.requiredCrField = true;
          break;
        }
      }
    }

    if (this.permitRequest.serviceType) {
      this.selectedServiceType = this.permitRequest.serviceType;
    }

    this.loadServiceTypesRelations();

    if (this.permitRequest.company) {
      this.registeredCompany = this.permitRequest.company;
    }
    if (this.permitRequest.secondaryCompany) {
      this.selectedSubCompany = this.permitRequest.secondaryCompany;
    }

    if (this.permitRequest.licenseRequest!.requestedLicenseDuration) {
      this.selectedlicenseDurationValues = this.permitRequest.licenseRequest!.requestedLicenseDuration;
    }
    if (this.permitRequest.licenseRequest!.maritimeTourismAgent) {
      this.selectedMaritimeTourismAgent = this.permitRequest.licenseRequest!.maritimeTourismAgent;
    }

    if (this.permitRequest.licenseProvider) {
      this.selectedLicenseProvider = this.permitRequest.licenseProvider;
      if (this.permitRequest.licenseProvider.investmentType) {
        // this.selectedInvestmentType = this.permitRequest.licenseProvider.investmentType;
        this.invTypeId = this.permitRequest.licenseProvider.investmentType.id;
        this.permitRequest.selectedInvestmentType = this.permitRequest.licenseProvider.investmentType;
      }
    }
    if (this.permitRequest.workLocation) {
      this.center = {
        lat: Number(this.permitRequest.workLocation.latitude),
        lng: Number(this.permitRequest.workLocation.longitude),
      };
      this.dropMarker(Number(this.permitRequest.workLocation.latitude), Number(this.permitRequest.workLocation.longitude));
    }
    if (this.permitRequest.licenseRequest?.marinaLocation) {
      this.centerMarine = {
        lat: Number(this.permitRequest.licenseRequest?.marinaLocation.latitude),
        lng: Number(this.permitRequest.licenseRequest?.marinaLocation.longitude),
      };
      this.dropMarkerMarine(
        Number(this.permitRequest.licenseRequest?.marinaLocation.latitude),
        Number(this.permitRequest.licenseRequest?.marinaLocation.longitude),
      );
    }

    if (this.permitRequest!.licenseRequest!.licenseRequestMaritimeMediums) {
      this.maritimeMediums = [];
      this.selectedMaritimeMediums = <IMaritimeMedium[]>[];
      for (let maritimeMediumItem of this.permitRequest!.licenseRequest!.licenseRequestMaritimeMediums) {
        if (maritimeMediumItem.maritimeMedium) {
          this.selectedMaritimeMediums.push(maritimeMediumItem.maritimeMedium);
          this.maritimeMediums.push(maritimeMediumItem.maritimeMedium);
          if (maritimeMediumItem.maritimeMedium.code == 'OTHER') {
            this.displayOtherTextField = true;
            this.otherTextValue = maritimeMediumItem!.otherValue;
          }
        }
      }
      this.selectedMaritimeMediumsId = this.selectedMaritimeMediums?.map(s => s.id);
      this.prepareMaritimeTourismServices(null, null);
    }
    if (this.permitRequest!.licenseRequest!.licenseRequestMaritimeServices) {
      this.selectedMaritimeTourismServices = <IMaritimeTourismServices[]>[];
      for (let maritimeTourismServices of this.permitRequest!.licenseRequest!.licenseRequestMaritimeServices) {
        if (maritimeTourismServices.maritimeTourismServices) {
          this.selectedMaritimeTourismServices.push(maritimeTourismServices.maritimeTourismServices);
        }
      }
    }
    if (this.permitRequest!.licenseRequest!.licenseRequestMarineTourServices) {
      this.selectedMarineTourOperatorServices = <IMarineTourOperatorServices[]>[];
      for (let marineTourOperatorServices of this.permitRequest!.licenseRequest!.licenseRequestMarineTourServices) {
        if (marineTourOperatorServices.marineTourOperatorServices) {
          this.selectedMarineTourOperatorServices.push(marineTourOperatorServices.marineTourOperatorServices);
        }
      }
    }
    if (this.permitRequest!.licenseRequest!.licenseRequestFuelTypes) {
      this.selectedFuelTypes = <IFuelTypes[]>[];
      for (let fuelType of this.permitRequest!.licenseRequest!.licenseRequestFuelTypes) {
        if (fuelType.fuelTypes) {
          this.selectedFuelTypes.push(fuelType.fuelTypes);
        }
      }
    }

    if (this.permitRequest!.licenseRequest!.licenseRequestProvidedLanguages) {
      this.selectedProvidedLanguages = <IProvidedLanguages[]>[];
      for (let providedLanguages of this.permitRequest!.licenseRequest!.licenseRequestProvidedLanguages) {
        if (providedLanguages.providedLanguages) {
          this.selectedProvidedLanguages.push(providedLanguages.providedLanguages);
        }
      }
    }
    if (this.permitRequest!.licenseRequest!.licenseRequestCountries) {
      this.selectedCountries = <ICountry[]>[];
      for (let country of this.permitRequest!.licenseRequest!.licenseRequestCountries) {
        if (country.countryOfWork) {
          this.selectedCountries.push(country.countryOfWork);
        }
      }
    }
    if (this.permitRequest!.licenseRequest!.beachEquipment) {
      this.selectedBeachEquipments = <IBeachEquipment[]>[];
      for (let beachEquipment of this.permitRequest!.licenseRequest!.beachEquipment) {
        if (beachEquipment) {
          this.selectedBeachEquipments.push(beachEquipment);
        }
      }
    }
    if (this.permitRequest!.licenseRequest!.beachActivities) {
      this.selectedBeachActivities = <IBeachActivities[]>[];
      for (let beachActivity of this.permitRequest!.licenseRequest!.beachActivities) {
        if (beachActivity) {
          this.selectedBeachActivities.push(beachActivity);
        }
      }
    }
    if (this.permitRequest.attachments) {
      this.documentTypes = [];
      for (let file of this.permitRequest!.attachments) {
        this.documentTypes.push(file.attachmentType?.code);

        switch (file!.attachmentType?.code) {
          case 'CR':
            this.CrAttachment = file;
            this.documents.push({ key: 'CR', file: new File([], file!.docName!, undefined) });
            break;
          case 'MainCR':
            this.MainCRAttachment = file;
            this.documents.push({ key: 'MainCR', file: new File([], file!.docName!, undefined) });
            break;
          case 'EXCEPTIONAL_DOC':
            this.exceptionalDocAttachment = file;
            this.documents.push({ key: 'EXCEPTIONAL_DOC', file: new File([], file!.docName!, undefined) });
            break;
          case 'CompanyNationalAddress':
            this.CompanyNationalAddressAttachment = file;
            this.documents.push({ key: 'CompanyNationalAddress', file: new File([], file!.docName!, undefined) });
            break;
          case 'CopyWorkforceMinistryHR':
            this.CopyWorkforceMinistryHRAttachment = file;
            this.documents.push({ key: 'CopyWorkforceMinistryHR', file: new File([], file!.docName!, undefined) });
            break;
          case 'InvestmentLicense':
            this.InvestmentLicenseAttachment = file;
            this.documents.push({ key: 'InvestmentLicense', file: new File([], file!.docName!, undefined) });
            break;
          case 'BankGuarantee':
            this.BankGuaranteeAttachment = file;
            this.documents.push({ key: 'BankGuarantee', file: new File([], file!.docName!, undefined) });
            break;
          case 'InsuranceCoverage':
            this.InsuranceCoverageAttachment = file;
            this.documents.push({ key: 'InsuranceCoverage', file: new File([], file!.docName!, undefined) });
            break;
          case 'InternationalSafetyManagement':
            this.InternationalSafetyManagementAttachment = file;
            this.documents.push({ key: 'InternationalSafetyManagement', file: new File([], file!.docName!, undefined) });
            break;
          case 'COMPANY_FILE':
            this.CompanyFileAttachment = file;
            this.documents.push({ key: 'COMPANY_FILE', file: new File([], file!.docName!, undefined) });
            break;
          case 'OperatingContract':
            this.OperatingContractAttachment = file;
            this.documents.push({ key: 'OperatingContract', file: new File([], file!.docName!, undefined) });
            break;
          case 'AdditionalAttachment':
            this.AdditionalAttachment.push(file);
            this.documents.push({ key: 'AdditionalAttachment', file: new File([], file!.docName!, undefined) });
            break;
          case 'ContractAuthority':
            this.ContractAuthorityAttachment = file;
            this.documents.push({ key: 'ContractAuthority', file: new File([], file!.docName!, undefined) });
            break;
          case 'CONSTRUCTION_PERMIT':
            this.ConstructionPermitAttachment = file;
            this.documents.push({ key: 'CONSTRUCTION_PERMIT', file: new File([], file!.docName!, undefined) });
            break;
          case 'ENVIRONMENTAL_LICENSE':
            this.EnvironmentalLicenseAttachment = file;
            this.documents.push({ key: 'ENVIRONMENTAL_LICENSE', file: new File([], file!.docName!, undefined) });
            break;
          case 'CYBER_SECURITY_CERTIFICATE':
            this.CyberSecurityCertificateAttachment = file;
            this.documents.push({ key: 'CYBER_SECURITY_CERTIFICATE', file: new File([], file!.docName!, undefined) });
            break;
          case 'COMPANY_PROFILE':
            this.CompanyProfileAttachment = file;
            this.documents.push({ key: 'COMPANY_PROFILE', file: new File([], file!.docName!, undefined) });
            break;
          case 'PROOF_OF_OWNERSHIP_OF_THE_MARINA':
            this.ProofOfOwnershipOfTheMarinaAttachment = file;
            this.documents.push({ key: 'PROOF_OF_OWNERSHIP_OF_THE_MARINA', file: new File([], file!.docName!, undefined) });
            break;
          case 'IDENTITY_OF_THE_OWNER_OF_THE_MARINA':
            this.AIdentityOfTheOwnerOfTheMarinaAttachment = file;
            this.documents.push({ key: 'IDENTITY_OF_THE_OWNER_OF_THE_MARINA', file: new File([], file!.docName!, undefined) });
            break;
          case 'MARINE_VEHICLES_AND_ACTIVITIES':
            this.MarineVehiclesAndActivitiesAttachment = file;
            this.documents.push({ key: 'MARINE_VEHICLES_AND_ACTIVITIES', file: new File([], file!.docName!, undefined) });
            break;

          case 'WATER_SAFETY_EQUIPMENT':
            this.WaterSafetyEquipmentAttachment = file;
            this.documents.push({ key: 'WATER_SAFETY_EQUIPMENT', file: new File([], file!.docName!, undefined) });
            break;

          case 'SAFETY_MANAGEMENT_PLAN':
            this.SafetyManagementPlanAttachment = file;
            this.documents.push({ key: 'SAFETY_MANAGEMENT_PLAN', file: new File([], file!.docName!, undefined) });
            break;

          case 'ENVIRONMENTAL_PERMIT':
            this.EnvironmentalPermitAttachment = file;
            this.documents.push({ key: 'ENVIRONMENTAL_PERMIT', file: new File([], file!.docName!, undefined) });
            break;

          case 'LIFEGUARDS_LIST':
            this.LifeguardListAttachment = file;
            this.documents.push({ key: 'LIFEGUARDS_LIST', file: new File([], file!.docName!, undefined) });
            break;

          case 'MARITIME_SPACE_PLANNING':
            this.MaritimeSpacePlanningAttachment = file;
            this.documents.push({ key: 'MARITIME_SPACE_PLANNING', file: new File([], file!.docName!, undefined) });
            break;

          case 'OCCUPANCY_CERTIFICATE':
            this.OccupancyCertificateAttachment = file;
            this.documents.push({ key: 'OCCUPANCY_CERTIFICATE', file: new File([], file!.docName!, undefined) });
            break;

          case 'SITE_VERIFICATION':
            this.SiteVerificationAttachment = file;
            this.documents.push({ key: 'SITE_VERIFICATION', file: new File([], file!.docName!, undefined) });
            break;

          case 'COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH':
            this.CommercialRegistrationOrIdentityOfTheOwnerOfTheBeachAttachment = file;
            this.documents.push({
              key: 'COMMERCIAL_REGISTRATION_OR_IDENTITY_OF_THE_OWNER_OF_THE_BEACH',
              file: new File([], file!.docName!, undefined),
            });
            break;
        }
      }
    }

    this.companiesSharedCollection = this.companyService.addCompanyToCollectionIfMissing<ICompany>(
      this.companiesSharedCollection,
      permitRequest.company,
    );
    this.usersSharedCollection = this.userService.addUserToCollectionIfMissing<IUser>(
      this.usersSharedCollection,
      permitRequest.assignedEmployee,
    );
    this.licenseProvidersSharedCollection = this.licenseProviderService.addLicenseProviderToCollectionIfMissing<ILicenseProvider>(
      this.licenseProvidersSharedCollection,
      permitRequest.licenseProvider,
    );
    this.licenseProvidersByCategory = this.licenseProviderService.addLicenseProviderToCollectionIfMissing<ILicenseProvider>(
      this.licenseProvidersByCategory,
      permitRequest.licenseProvider,
    );
    this.serviceTypesSharedCollection = this.serviceTypeService.addServiceTypeToCollectionIfMissing<IServiceType>(
      this.serviceTypesSharedCollection,
      permitRequest.serviceType,
    );
    this.licenseRequestsSharedCollection = this.licenseRequestService.addLicenseRequestToCollectionIfMissing<ILicenseRequest>(
      this.licenseRequestsSharedCollection,
      permitRequest.licenseRequest,
    );
    this.permitRequestFormService.resetForm(this.editForm, permitRequest);
    this.editMode = true;
    this.updateDocks();
    this.step = 4;
    this.showWizard();
    this.currentStep = 3;
  }

  protected loadServiceTypesRelations(): void {
    this.criteria = {};
    const serviceCategory = {
      'serviceTypeId.equals': Number(this.selectedServiceType?.id),
    };
    this.criteria = { ...this.criteria, ...serviceCategory };

    this.serviceTypeDocumentTypeService
      .query({ ...this.criteria })
      .pipe(map((res: HttpResponse<IServiceTypeDocumentType[]>) => res.body ?? []))
      .subscribe((serviceTypeDocumentType: IServiceTypeDocumentType[]) => {
        this.serviceTypeDocumentTypes = serviceTypeDocumentType;
      });
    this.serviceTypeTermsConditionsService
      .query({ ...this.criteria })
      .pipe(map((res: HttpResponse<IServiceTypeTermsConditions[]>) => res.body ?? []))
      .subscribe((serviceTypeTermsConditions: IServiceTypeTermsConditions[]) => {
        this.serviceTypeTermsConditions = serviceTypeTermsConditions;
      });
    this.serviceTypeLiabilityTypeService
      .query({ ...this.criteria })
      .pipe(map((res: HttpResponse<IServiceTypeLiabilityType[]>) => res.body ?? []))
      .subscribe((serviceTypeLiabilityType: IServiceTypeLiabilityType[]) => {
        this.serviceTypeLiabilityType = serviceTypeLiabilityType;
      });
  }

  protected loadRelationshipsOptions(): void {
    this.maritimeMediumService
      .query()
      .pipe(map((res: HttpResponse<IMaritimeMedium[]>) => res.body ?? []))
      .subscribe((maritimeMediums: IMaritimeMedium[]) => (this.maritimeMediums = maritimeMediums));

    // this.maritimeTourismServicesService
    //   .query()
    //   .pipe(map((res: HttpResponse<IMaritimeTourismServices[]>) => res.body ?? []))
    //   .subscribe((maritimeServ: IMaritimeTourismServices[]) => {
    //     this.maritimeTourismServices = maritimeServ;
    //   });

    this.countryService
      .query()
      .pipe(map((res: HttpResponse<ICountry[]>) => res.body ?? []))
      .subscribe((countries: ICountry[]) => (this.countries = countries));

    this.providedLanguagesService
      .query()
      .pipe(map((res: HttpResponse<IProvidedLanguages[]>) => res.body ?? []))
      .subscribe((providedLanguages: IProvidedLanguages[]) => (this.providedLanguages = providedLanguages));

    // this.companyService
    //   .query()
    //   .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
    //   .pipe(
    //     map((companies: ICompany[]) =>
    //       this.companyService.addCompanyToCollectionIfMissing<ICompany>(companies, this.permitRequest?.company),
    //     ),
    //   )
    //   .subscribe((companies: ICompany[]) => (this.companiesSharedCollection = companies));

    this.companyService
      .findAllMaritimeTourismAgentCompanies()
      .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
      .subscribe((companies: ICompany[]) => (this.maritimeTourismAgentCompanies = companies));

    this.companyService
      .getRegisteredCompany()
      .pipe(map((res: HttpResponse<any>) => res.body ?? []))
      .subscribe((companyFullInfo: any) => {
        this.registeredCompany = companyFullInfo.company;
        this.managersAr = companyFullInfo.managersAr;
        this.managersEn = companyFullInfo.managersEn;
        this.fullInfoAr = companyFullInfo.crInfoWathqResponseAr;
        this.fullInfoEn = companyFullInfo.crInfoWathqResponseEn;
        this.onSelectedCR(this.registeredCompany);
        this.companyService
          .getRelatedCompany(this.registeredCompany!.crNumber)
          .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
          .subscribe((companies: ICompany[]) => (this.relatedCompanies = companies));
      });

    this.userService
      .query()
      .pipe(map((res: HttpResponse<IUser[]>) => res.body ?? []))
      .pipe(map((users: IUser[]) => this.userService.addUserToCollectionIfMissing<IUser>(users, this.permitRequest?.assignedEmployee)))
      .subscribe((users: IUser[]) => (this.usersSharedCollection = users));

    this.licenseProviderService
      .query()
      .pipe(map((res: HttpResponse<ILicenseProvider[]>) => res.body ?? []))
      .pipe(
        map((licenseProviders: ILicenseProvider[]) =>
          this.licenseProviderService.addLicenseProviderToCollectionIfMissing<ILicenseProvider>(
            licenseProviders,
            this.permitRequest?.licenseProvider,
          ),
        ),
      )
      .subscribe((licenseProviders: ILicenseProvider[]) => (this.licenseProvidersSharedCollection = licenseProviders));

    this.investmentTypeService
      .query()
      .pipe(map((res: HttpResponse<IInvestmentType[]>) => res.body ?? []))
      .subscribe((iInvestmentTypes: IInvestmentType[]) => (this.investmentTypes = iInvestmentTypes));

    this.serviceTypeService
      .query()
      .pipe(map((res: HttpResponse<IServiceType[]>) => res.body ?? []))
      .pipe(
        map((serviceTypes: IServiceType[]) =>
          this.serviceTypeService.addServiceTypeToCollectionIfMissing<IServiceType>(serviceTypes, this.permitRequest?.serviceType),
        ),
      )
      .subscribe((serviceTypes: IServiceType[]) => (this.serviceTypesSharedCollection = serviceTypes));

    this.serviceTypeService
      .getAvailableTypes()
      .pipe(map((res: HttpResponse<IServiceType[]>) => res.body ?? []))
      .subscribe({
        next: (serviceTypes: IServiceType[]) => (this.availableServiceTypeList = serviceTypes),
        error: (error: any) => {
          if (error.error.params === 'CrExpired') {
            this.CrExpired = true;
          } else if (error.error.params === 'CrNeedRenewal') {
            this.CrNeedRenewal = true;
          }
        },
      });

    this.licenseRequestService
      .query()
      .pipe(map((res: HttpResponse<ILicenseRequest[]>) => res.body ?? []))
      .pipe(
        map((licenseRequests: ILicenseRequest[]) =>
          this.licenseRequestService.addLicenseRequestToCollectionIfMissing<ILicenseRequest>(
            licenseRequests,
            this.permitRequest?.licenseRequest,
          ),
        ),
      )
      .subscribe((licenseRequests: ILicenseRequest[]) => (this.licenseRequestsSharedCollection = licenseRequests));

    this.fuelTypesService
      .query()
      .pipe(map((res: HttpResponse<IFuelTypes[]>) => res.body ?? []))
      .subscribe((iFuelTypes: IFuelTypes[]) => (this.fuelTypes = iFuelTypes));

    this.marineTourOperatorServicesService
      .query()
      .pipe(map((res: HttpResponse<IMarineTourOperatorServices[]>) => res.body ?? []))
      .subscribe(
        (iMarineTourOperatorServices: IMarineTourOperatorServices[]) => (this.marineTourOperatorServices = iMarineTourOperatorServices),
      );

    // if(this.selectedServiceType?.code === 'BO'){
    this.permitRequestService
      .getBeachActivities()
      .subscribe((iBeachActivities: IBeachActivities[]) => (this.beachActivities = iBeachActivities));
    this.permitRequestService
      .getBeachEquipment()
      .subscribe((iBeachEquipments: IBeachEquipment[]) => (this.beachEquipments = iBeachEquipments));
  }

  protected onSelectedServiceType(): void {
    this.loadServiceTypesRelations();
    this.step = 1;
  }

  protected onSelectedCR(event: any): void {
    this.selectedSubCompany = this.subCompany;
    this.companyService
      .getCrOwner(this.registeredCompany?.crNumber)
      .pipe(map((res: HttpResponse<any[]>) => res.body ?? []))
      .subscribe((owner: any[]) => {
        this.companyOwners = owner;
        for (let compOwner of this.companyOwners) {
          if (compOwner.nationalityCode != '1') {
            this.requiredCrField = true;
            break;
          }
        }
      });
    this.subCompanyDetailsFields = [
      {
        label: this.isArabic() ? 'رقم السجل التجاري' : 'CR Number',
        value: this.subCompany?.crNumber || '-',
      },
      {
        label: this.isArabic() ? 'الرقم الوطني للسجل التجاري' : 'CR National Number',
        value: this.subCompany?.crNationalNumber || '-',
      },
      {
        label: this.isArabic() ? 'اسم المنشأة' : 'Company Name',
        value: this.isArabic() ? this.subCompany?.nameAr : this.subCompany?.nameEn,
      },
      {
        label: this.isArabic() ? 'السجل الرئيسي' : 'Main CR Number',
        value: this.subCompany?.mainCrNumber || '-',
      },
      {
        label: this.isArabic() ? 'الرقم الوطني للسجل الرئيسي' : 'Main CR National Number',
        value: this.subCompany?.mainCrNationalNumber || '-',
      },
      {
        label: this.isArabic() ? 'نوع الكيان' : 'Entity Type',
        value: this.isArabic() ? this.subCompany?.entityTypeAr || '-' : this.subCompany?.entityTypeEn || '-',
      },
      {
        label: this.isArabic() ? 'منشأة رئيسية؟' : 'Is Main?',
        value: this.isArabic() ? (this.subCompany?.main ? 'نعم' : 'لا') : this.subCompany?.main ? 'Yes' : 'No',
      },
    ];
    //this.step = 3;
  }

  protected onSelectedInvestmentType(): void {
    this.licenseProvidersByCategory = [];
    for (let licenseProvider of this.licenseProvidersSharedCollection) {
      if (this.invTypeId == licenseProvider.investmentType?.id) {
        this.licenseProvidersByCategory.push(licenseProvider);
      }
    }
  }

  protected onSelectedLicenseProvider(event: any): void {
    this.selectedLicenseProvider = event;
  }

  protected isCurrentWizardInvalid() {
    switch (this.currentStep) {
      case 1:
        return this.checkStep1();
        break;
      case 2:
        return !this.workLocationSet;
        break;
      case 3:
        return this.checkStep3();
        break;
      case 4:
        return this.checkStep4();
        break;
      case 5:
        return this.checkStep5();
        break;
      case 6:
        return false;
        break;
    }
    return true;
  }

  getMaxMales(): number {
    return (
      (this.editForm.getRawValue().saudiLaborCount || 0) +
      (this.editForm.getRawValue().foreignersLaborCount || 0) -
      (this.editForm.getRawValue().numberOfFemaleEmployees || 0)
    );
  }
  getMaxFemales(): number {
    return (
      (this.editForm.getRawValue().saudiLaborCount || 0) +
      (this.editForm.getRawValue().foreignersLaborCount || 0) -
      (this.editForm.getRawValue().numberOfMaleEmployees || 0)
    );
  }

  maleFemaleTotalError(): boolean {
    return (
      (this.editForm.getRawValue().saudiLaborCount || 0) + (this.editForm.getRawValue().foreignersLaborCount || 0) !=
      (this.editForm.getRawValue().numberOfFemaleEmployees || 0) + (this.editForm.getRawValue().numberOfMaleEmployees || 0)
    );
  }
  totalWorkersError() {
    return (this.editForm.getRawValue().saudiLaborCount || 0) + (this.editForm.getRawValue().foreignersLaborCount || 0) <= 0;
  }
  changeFuelCount() {
    if (this.permitRequest?.licenseRequest?.noOfFuelStations !== null && this.permitRequest!.licenseRequest!.noOfFuelStations === 0) {
      this.selectedFuelTypes = null;
    }
  }

  clickToShowProofOfOwnershipOfTheMarinaAttachment() {
    this.hideAllAttachment();
    this.hiddeProofOfOwnershipOfTheMarinaAttachment = true;
  }

  clickToShowIdentityOfTheOwnerOfTheMarinaAttachment() {
    this.hideAllAttachment();
    this.hiddeAIdentityOfTheOwnerOfTheMarinaAttachment = true;
  }

  clickToShowCyberSecurityCertificateAttachment() {
    this.hideAllAttachment();
    this.hiddeCyberSecurityCertificateAttachment = true;
  }

  clickToShowCompanyProfileAttachment() {
    this.hideAllAttachment();
    this.hiddeCompanyProfileAttachment = true;
  }

  clickToShowCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach() {
    this.hideAllAttachment();
    this.hiddenCommercialRegistrationOrIdentityOfTheOwnerOfTheBeach = true;
  }

  clickToShowSiteVerification() {
    this.hideAllAttachment();
    this.hiddenSiteVerification = true;
  }

  clickToShowOccupancyCertificate() {
    this.hideAllAttachment();
    this.hiddenOccupancyCertificate = true;
  }

  clickToShowMaritimeSpacePlanning() {
    this.hideAllAttachment();
    this.hiddenMaritimeSpacePlanning = true;
  }

  clickToShowLifeguardList() {
    this.hideAllAttachment();
    this.hiddenLifeguardList = true;
  }

  clickToShowEnvironmentalPermit() {
    this.hideAllAttachment();
    this.hiddenEnvironmentalPermit = true;
  }

  clickToShowSafetyManagementPlan() {
    this.hideAllAttachment();
    this.hiddenSafetyManagementPlan = true;
  }

  clickToShowWaterSafetyEquipment() {
    this.hideAllAttachment();
    this.hiddenWaterSafetyEquipment = true;
  }

  clickToShowMarineVehiclesAndActivities() {
    this.hideAllAttachment();
    this.hiddenMarineVehiclesAndActivities = true;
  }
}
