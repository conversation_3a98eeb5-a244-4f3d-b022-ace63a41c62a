<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity LicenseRequest.
    -->
    <changeSet id="20240429102455-1" author="jhipster">
        <createTable tableName="license_request">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="no_of_years_experience" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_country_company_opr" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_managed_maritime_mediums" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_owned_maritime_mediums_ksa_world" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_managed_maritime_mediums_general" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="requested_license_duration" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="marina_owner_name" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="marina_cr_number" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="marina_cr_name_ar" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="marina_cr_name_en" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="number_of_docks" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="dock_length" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="dock_depth" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_water_stations" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_water_stations_per_platform" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_fuel_stations" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_waste_stations" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_waste_stations_per_platform" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_electric_charging_stations" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_control_towers" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="no_of_marina_facilities" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="boats_capacity" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="yachts_capacity" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="other_maritime_medium_capacity" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="marina_location_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="maritime_tourism_agent_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="record_activity" type="boolean" defaultValue="true">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="varchar(50)" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp" defaultValueDate="${now}">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20240429102455-1-data" author="jhipster" >
        <loadData
                  file="config/liquibase/fake-data/license_request.csv"
                  separator=";"
                  tableName="license_request"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="no_of_years_experience" type="numeric"/>
            <column name="no_of_country_company_opr" type="numeric"/>
            <column name="no_of_managed_maritime_mediums" type="numeric"/>
            <column name="no_of_owned_maritime_mediums_ksa_world" type="numeric"/>
            <column name="no_of_managed_maritime_mediums_general" type="numeric"/>
            <column name="requested_license_duration" type="string"/>
            <column name="marina_owner_name" type="string"/>
            <column name="marina_cr_number" type="string"/>
            <column name="marina_cr_name_ar" type="string"/>
            <column name="marina_cr_name_en" type="string"/>
            <column name="number_of_docks" type="numeric"/>
            <column name="dock_length" type="numeric"/>
            <column name="dock_depth" type="numeric"/>
            <column name="no_of_water_stations" type="numeric"/>
            <column name="no_of_water_stations_per_platform" type="numeric"/>
            <column name="no_of_fuel_stations" type="numeric"/>
            <column name="no_of_waste_stations" type="numeric"/>
            <column name="no_of_waste_stations_per_platform" type="numeric"/>
            <column name="no_of_electric_charging_stations" type="numeric"/>
            <column name="no_of_control_towers" type="numeric"/>
            <column name="no_of_marina_facilities" type="numeric"/>
            <column name="boats_capacity" type="numeric"/>
            <column name="yachts_capacity" type="numeric"/>
            <column name="other_maritime_medium_capacity" type="numeric"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
    <changeSet id = "202407170257-add-fields" author="fkanhal">

        <addColumn tableName="license_request">
            <column name="marina_owner_name_english" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="dock_width" type="integer">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="20250611-03-drop-maritime-column" author="osama">
        <dropColumn tableName="license_request" columnName="no_of_owned_maritime_mediums_ksa_world"/>
    </changeSet>

    <changeSet id="20250611-02-add-maritime-columns" author="osama">
        <addColumn tableName="license_request">
            <column name="no_of_owned_maritime_mediums_ksa" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="no_of_owned_maritime_mediums_world" type="int">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="202507201207-add-beach-operator-columns" author="faalkanhal">
        <addColumn tableName="license_request">
            <column name="beach_owner_name" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="beach_trade_name" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="beach_owner_commercial_registration_number" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="no_of_available_restrooms" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="no_of_available_lifeguards" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="are_there_rooms_or_cabins_for_rent" type="boolean">
                <constraints nullable="true"/>
            </column>
            <column name="no_of_cabins_and_rooms" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="no_of_food_truck_parking" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="visitor_capacity" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="is_there_marine_vessel" type="boolean">
                <constraints nullable="true"/>
            </column>
            <column name="number_of_other_marine_vessels" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="previously_operated_beach_inside_or_outside_saudi_arabia" type="boolean">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
