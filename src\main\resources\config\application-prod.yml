# ===================================================================
# Spring Boot configuration for the "prod" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: INFO
    tech.jhipster: INFO
    com.elm.srsa: INFO

management:
  prometheus:
    metrics:
      export:
        enabled: false
  endpoint:
    quartz:
      enabled: false

spring:
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  quartz:
    enabled: true
    properties:
      org.quartz.jobStore.selectWithLockSQL: SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?
    job-store-type: jdbc
    jdbc:
      initialize-schema: never

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${SPRING_DATASOURCE_URL:*********************************************************************;}
    username: ${SPRING_DATASOURCE_USERNAME:sa}
    password: ${SPRING_DATASOURCE_PASSWORD:Aa123456}
    hikari:
      poolName: Hikari
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    database-platform: org.hibernate.dialect.SQLServer2012Dialect
    database: SQL_SERVER
    show-sql: true
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: true
      hibernate.jdbc.time_zone: Asia/Riyadh
      hibernate.hbm2ddl.auto: none
      hibernate.show_sql: false
      hibernate.format_sql: false
  # Replace by 'prod, faker' to add the faker context and have sample data loaded in production
  liquibase:
    contexts: prod
  mail:
    host: localhost
    port: 25
    username:
    password:
  thymeleaf:
    cache: true

# ===================================================================
# To enable TLS in production, generate a certificate using:
# keytool -genkey -alias srsa -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# See details in topic "Create a Java Keystore (.JKS) from Let's Encrypt Certificates" on https://maximilian-boehm.com/en-gb/blog
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#   port: 443
#   ssl:
#     key-store: classpath:config/tls/keystore.p12
#     key-store-password: password
#     key-store-type: PKCS12
#     key-alias: selfsigned
#     # The ciphers suite enforce the security by deactivating some old and deprecated SSL cipher, this list was tested against SSL Labs (https://www.ssllabs.com/ssltest/)
#     ciphers: TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 ,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA,TLS_RSA_WITH_CAMELLIA_128_CBC_SHA
# ===================================================================
server:
  port: 8080
  shutdown: graceful # see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-graceful-shutdown
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  http:
    cache: # Used by the CachingHttpHeadersFilter
      timeToLiveInDays: 1461
  cache: # Cache configuration
    ehcache: # Ehcache configuration
      time-to-live-seconds: 3600 # By default objects stay 1 hour in the cache
      max-entries: 1000 # Number of objects in each cache entry
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        # As this is the PRODUCTION configuration, you MUST change the default key, and store it securely:
        # - In the Consul configserver
        # - In a separate `application-prod.yml` file, in the same folder as your executable JAR file
        # - In the `JHIPSTER_SECURITY_AUTHENTICATION_JWT_BASE64_SECRET` environment variable
        base64-secret: AJ8A0OWUWuwrIaCUVOdsas15Y6pJgZu7CEJDsrF/JMrh892UXknEZvzeC6FevTTWlhjF00/OmvGJfq7uzX15cZqscDPidIuQyhh9y9WgF0aj1oPtgUcgtKchMeSIbhalQGUGcv/dBGtlbKt5Z4zoYyG8r0coLQheU+hzQcJF9ExozbLHsiQB0IcOM1l9ZlGFOWazhIeh+a8raSFSBkMQQ4Ti84UTEyXzNhWQa1jHrjztB5FrflKBj0673UFlWfx66vRefEUp715n8PrunSNVxtYnotgt1t69XYvK0tWxPKAQnk6yadxTm4aitLWbueOE0Y915tV+j+x1h6RkHfNaG9G1zp9whlwzMmZJAoKgTm35lcG4UUfMTPVt4v0oMeAaAYkwKZeJT02IPgvl07WETLBvLd2AjqGuavDMbZxpRHUdIl5l32Qzh1+w1+bVHuSv3Vi3vRb90KN1Sq6K9j2jY4fYKpFT5NqNu/iiidv+nALNVUnKoha9qr6jkqwQOWKX+zDU6xR7fvvGIa73xr+Oqi6oEQy+26LNcKNQj7omcybtXrZuexDd0uF8qQQGKr8zDtDCYXcIQ7SwfTx/WVZ++7dvKiSbujMsyuRNv2lTGY8HI3J4li2SEe74bTbOl39IPMxn5qAZecuH2yXI22q71xHtuX86zF7muKEeG0ZQVp8=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  mail: # specific JHipster mail property, for standard properties see MailProperties
    base-url: http://my-server-url-to-change # Modify according to your server's URL
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

elm:
  providers:
    filescan:
      mock:
        enabled: true
      rest:
        url: http://localhost:8282/dcc-engines-filescan/scan-file
      username: dccfilescan-username
      password: dccfilescan-password
      connection:
        timeout: 10000
      read:
        timeout: 10000

# application:
srsa:
  enableOneSession: false
  proxy:
    enabled: true
    protocol: 'http'
    host: '*************'
    port: 8080
    ssl: true
    connectionTimeout: 10000
    readTimeout: 10000
  noProxy:
    connectionTimeout: 10000
    readTimeout: 10000
  capcha:
    enabled: false
    failAttempts: 1
    allowedTries: 3
  recaptcha:
    responseParameter: g-recaptcha-response
    verificationUrl: https://www.google.com/recaptcha/api/siteverify
    secretKey: 6LfbEq8pAAAAALWqEqInr3Q_ahiRh7PKJ1F6Eee_
    siteKey: 6LfbEq8pAAAAALxjNdFS5Rold51q4kc-ONSX3B1R
  scheduler:
    enabled: true

  yakeen:
    enabled: true
    mocked: true
    appId: '6a8020cb'
    appKey: '4a5cf5aa0d113fbbe560ec714a043e67'
    usageCode: 'USC80006'
    authServiceUrl: 'https://yakeencore.api.elm.sa/api/v2/yakeen/login'
    addressUrl: 'https://yakeencore.api.elm.sa/api/v1/yakeen/data'
    username: 'RED_SEA_AUTHORITY_TRIAL_YAK'
    password: 'MF04beIaW56e8arGWkln'
    tokenValidityInHours: 20
    authServiceCronJob:
      enabled: true
      expression: '00 00 0/12 * * ?'
    dataServiceUrl: 'https://yakeencore.api.elm.sa/api/v1/yakeen/data'
    addressServiceIdentifier: 'fe70a3e8-73c3-4a63-91e0-2b3ba36a3660'
    citizenInfoServiceId: '82d24da8-04b2-490f-b636-aa09c06b3b52'
    gccNinServiceId: '532500ab-1938-4f93-b5d8-eab8164fb11c'
    alienInfoByIqamaServiceId: 'e85f37f7-8434-4d64-88f1-ea0680a91a5d'
    borderNoServiceId: '223a9e74-36c4-4a15-b887-40b1d314a92e'
    convertDate:
      enabled: false
      serviceId: '916d4583-114b-467e-b768-3f2d3ba38418'

  notification:
    baseUrl: 'https://elm-notification-dev-api-staging.apps.devocp4.elm.sa:443'
    appKey: '804ce3cc5e37e1f6f4f6fbd5f05c5120'
    appId: '54fbd2c8'
    clientKey: '266092C6-60B4-4EB6-B37C-BD38B4BAC5DC'
    productCode: '023'
    senderName: 'Tasreeh'
    tourismHarasElhoddodMail: '<EMAIL>'

    sms:
      enabled: true
      mocked: true
      url: '/api/notification/message'
      deliveryRequired: 0
      expiryMessageTime: 0

    email:
      enabled: true
      mocked: true
      url: '/api/notification/email'
      from: '<EMAIL>'

  otp:
    enabled: true
    mocked: true
    mockCode: '5555'
    size: 4
    validityInMinutes: 2
    retryCount: 3

  wathq:
    enabled: true
    mocked: false
    useProxy: false
    baseUrl: 'https://wathiq-qa-api-beta.apps.devocp4.elm.sa/v4/commercialregistration'
    crFullInfoUrl: '/fullinfo/'
    crInfoUrl: '/info/'
    crAddressUrl: '/address/'
    crManagersUrl: '/managers/'
    crTreeUrl: '/branches/'
    crOwnersUrl: '/owners/'
    3scale:
      appId: '258b8414'
      appKey: '38ce52baf0b5cb590fc121f7fe64fa91'

  absherOtp:
    enabled: true
    mocked: true
    mockCode: '5555'
    size: 4
    validityInMinutes: 2
    retryCount: 3
    keyStorePath: '/opt/config/prod/jks/saudi_red_sea_authority.jks'
    keyStoreType: 'jks'
    keyStoreAlias: 'saudi red sea authority'
    keyStorePassword: 'SRSA@2024'
    serviceUrl: 'https://otp.absher.sa/AbsherOTPService'
    clientId: '**********-0001'
    clientAuthorization: 'BQX1VtKbR9bubypDlm/H/SBpkpJlcYdahoxTVwTwC7eoYT24VUrymK7vfi/xjNWF'
    reasonAr: 'التحقق من الجوال برقم الهوية'
    reasonEn: 'Mobile number verification by id number'
    templateId: 'RedSea-OTP-01'
    templateParamName: 'Param1'
    templateParam:
      sp:
        ar: 'تسجيل مقدم خدمة'
        en: 'Service Provider Registration'
      emp:
        ar: 'تسجيل موظف'
        en: 'Employee Registration'
      forgotPassword:
        ar: 'استرجاع كلمة المرور'
        en: 'Forgot Password'
    verificationCheckValidityInMinutes: 30
    templateDefaultLanguage: 'AR'

  fasah:
    enabled: true
    mock: true
    mockDataBase: true
    baseUrl: 'https://soga.fasah.sa/api/'
    username: 'RED_SEA_AUTHORITY_TRIAL_YAK'
    password: 'MF04beIaW56e8arGWkln'
    productCode: '023'
    senderName: 'Tasreeh'
    accountId: 'RED_SEA'
    vessalType: 'YAC'
    serverKey: 'SJJNLLJHL2-JJGN9KGKLR-9LBGBJLHMH'
    3scale:
      appId: '258b8414'
      appKey: '38ce52baf0b5cb590fc121f7fe64fa91'
      token: 'test123'
    callback:
      enabled: true
      url: 'https://red-sea-authority-dev.apps.devocp4.elm.sa/api/payment/external/notification/'
      3scaleToken: 'Shared_secret_sent_from_proxy_to_API_backend_0703ef49752f9ba4'
      verificationEnabled: true
    return:
      enabled: true
      url: 'https://red-sea-authority-dev.apps.devocp4.elm.sa/api/payment/external/result/'
  #      verificationEnabled: false

  zatca:
    enabled: true
    mocked: true
    baseurl: ''
    clientId: ''
    clientSecret: ''
  mofa:
    enabled: true
    mocked: true
    baseurl: 'https://gsn-api.mofa.gov.sa'
    tokenUrl: '/api/auth/connect/token'
    issueVisaUrl: '/api/tga/visas'
    submitVisaUrl: '/api/tga/tourism-requests'
    arabicCountryUrl: '/api/tga/arabic-countries'
    countryUrl: '/api/tga/countries'
    clientSecret: 'secret'
    scope: 'redseaScope'
    grantType: 'client_credentials'
    audience: 'mofa_identity_takamol_api'
  tga:
    enabled: true
    mocked: true
    renewValidDays: 5
    baseurl: 'http://************:90/API.Gateway/gateway/'
    systemName: 'RedSea'
    serviceName: 'Marines'
    secretToken: 'FB75EB30-0DA8-43EE-99C1-B44FB45F6522'
    callback:
      enabled: true
      url: 'http://localhost:8080/api/navigation-permit/external/tga/update-request/'
      3scaleToken: 'Shared_secret_sent_from_proxy_to_API_backend_0703ef49752f9ba4'
    requestParameter:
      systemName: 'RedSeaSystem'
      accountId: 'c3ba86d0-be9a-40fb-a738-da3ae9bbdd3f'
      userId: '********-0000-0000-0000-********0000'
      createdBy: '********-0000-0000-0000-********0000'
      lockedByAccount: '********-0000-0000-0000-********0000'
      requestId: '********-0000-0000-0000-********0000'
      attachmentStatusId: '522608ee-84d6-4457-4e5c-08d98cb13cae'
      sequence: 1
      saudiRequestType: 112
      foreignRequestType: 122

  generalConfig:
    cspConfig: script-src 'self' https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js http://* https://www.gstatic.com https://www.google.com https://kit.fontawesome.com https://* https://code.jquery.com https://cdn.jsdelivr.net https://stackpath.bootstrapcdn.com https://ka-p.fontawesome.com 'sha256-AbCdEf123...' 'sha256-XyZ456...'; style-src 'self' https://maxcdn.bootstrapcdn.com https://fonts.googleapis.com https://stackpath.bootstrapcdn.com; font-src 'self' https://fonts.gstatic.com https://ka-p.fontawesome.com; img-src 'self' data:; object-src 'none'; base-uri 'self'; frame-ancestors 'none'
    rsaDomain:
    googleMapApiKey: 'AIzaSyCBQbcD9jDt17OoC3bIpMcAGnYh5ZFwb68'
    allowConnectivityTest: true

  documentAttachmentConfiguration:
    permitRequestAttachmentsPath: C:\\srsa\\attachment\\permitRequest
    userAttachmentsPath: C:\\srsa\\attachment\\userAttachment
    companyAttachmentsPath: C:\\srsa\\attachment\\companyAttachment
    marineAttachmentsPath: C:\\srsa\\attachment\\marineMedium
    mtaDelegationAttachmentsPath: C:\\srsa\\attachment\\mtaDelegation
    arrDepRequestAttachmentsPath: C:\\srsa\\attachment\\arrDepRequest
    navigationPermitAttachmentsPath: C:\\srsa\\attachment\\navigationPermit
    baseAttachmentsPath: C:\\srsa\\attachment\\
    tourismPermitAttachmentsPath: C:\\srsa\\attachment\\tourismPermit

  Cookie:
    secure: true
    httpOnly: true
    sameSite: strict

  htmlToPdf:
    path: C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf
    pdfParams: --enable-local-file-access --page-size A4 -T 0mm -B 0mm -L 0 -R 0

  registration:
    idNumber:
      acceptedNumberOfDaysBeforeExpiry: 90
    crNumber:
      acceptedNumberOfDaysBeforeExpiry: 90
    nationalAddress:
      #accepted values validate, useDefault
      action: 'validate'
      default:
        region: 'الرياض'
        city: 'الرياض'
        districtId: 12
        district: 'السليمانية'
        street: 'شارع تركي السديري'
        buildingNumber: 1234
        postalCode: 11125
        additionalNumber: 1234
        unitNumber: 33

  payment:
    gracePeriodInHours: 4
    cronJob:
      enabled: true
      expression: '0 0/2 * * * ?'

  quickTik:
    enabled: true
    mocked: false
    useProxy: false
    baseUrl: 'https://secure.quicktik.sa'
    createInvoice:
      enabled: true
      url: '/payment/new/invoice'
      validityPeriodInDays: 7
      cronJob:
        enabled: true
        expression: '0 0/2 * * * ?'
      tranType: 'sale'
      tranClass: 'ecom'
      cartCurrency: 'SAR'
      payPageLang: 'ar'
      hideShipping: true
      tokenise: 2
      showSaveCard: true
      customerCountry: 'SA'
      customerNumberPrefix: 'SRSA-'
      invoiceNumberPrefix: 'SRSA-INV-'
    invoiceStatus:
      enabled: true
      url: '/payment/invoice/status'
    cancelInvoice:
      enabled: true
      url: '/payment/invoice/cancel'
    downloadInvoicePdf:
      enabled: true
      url: '/payment/invoice/$invoice_id$/download/pdf'
      directory: /opt/docs/
      parameter1: '$invoice_id$'
    return:
      enabled: true
      url: 'https://red-sea-authority-qa.apps.devocp4.elm.sa/api/payment/external/result/'
      verificationEnabled: false
    paymentStatus:
      url: 'https://red-sea-authority-qa.apps.devocp4.elm.sa/payment/$paymentRequestUuid$/payment-request-status'
      parameter1: '$paymentRequestUuid$'
    callback:
      enabled: true
      url: 'https://red-sea-authority-qa.apps.devocp4.elm.sa/api/payment/external/notification/'
      3scaleToken: 'SJJNLLJHL2-JJGN9KGKLR-9LBGBJLHMH'
      verificationEnabled: false
    serverKey: 'SJJNLLJHL2-JJGN9KGKLR-9LBGBJLHMH'
    clientKey: 'C9KMDM-76MQ66-TQPKTK-KRKQPM'
    profileId: '47508'
  sadad:
    billerNameAr: هيئة البحر الأحمر
    billerNameEn: Red Sea Authority
    billerNumber: 387

  discoveryContentOnlyEnabled: false

  user:
    removeInactive:
      periodInDays: 3
      cronJob:
        enabled: true
        expression: '0 0/2 * * * ?'

  mtaDelegationExpiration:
    cronJob:
      enabled: true
      expression: '0 0 0 * * ?'

  tourismPermitExpiration:
    cronJob:
      enabled: true
      expression: '0 0 0 * * ?'

  rate-limit:
    capacity: 10 # requests
    duration-in-sec: 60 # seconds
