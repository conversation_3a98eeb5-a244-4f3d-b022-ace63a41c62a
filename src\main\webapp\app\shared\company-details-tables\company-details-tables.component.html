<div class="p-4">
  <div class="row" *ngIf="!loading">
    <div class="col-md-6" *ngFor="let field of companyDetailsFields">
      <strong>{{ field.label }}:</strong>
      <span>{{ field.value }}</span>
    </div>
  </div>
</div>

<div class="p-4" *ngIf="!isServiceProvider()">
  <p id="jhi-permitRq6" data-cy="PermitRequestCreateUpdateHeading" class="headFont" jhiTranslate="srsaApp.permitRequest.ownerInformation">
    بيانات الملاك
  </p>
  <hr />
  <div class="mb-5">
    <div class="table-responsive">
      <table class="table table-striped dga-table" aria-describedby="companyOwnersTable">
        <thead>
          <tr>
            <th class="text-start">{{ isArabic() ? 'الاسم' : 'Name' }}</th>
            <th class="text-start">{{ isArabic() ? 'نوع الهوية' : 'Identity Type' }}</th>
            <th class="text-start">{{ isArabic() ? 'رقم الهوية' : 'Identity Code' }}</th>
            <th class="text-start">{{ isArabic() ? 'رمز الجنسية' : 'Nationality Code' }}</th>
            <th class="text-start">{{ isArabic() ? 'الجنسية' : 'Nationality' }}</th>
            <th class="text-start">{{ isArabic() ? 'نوع الارتباط' : 'Relation Type' }}</th>
            <th class="text-start">{{ isArabic() ? 'معرف الشركة' : 'Company ID' }}</th>
            <th class="text-start">{{ isArabic() ? 'مساهمة نقدية' : 'Cash Contribution' }}</th>
            <th class="text-start">{{ isArabic() ? 'مساهمة عينية' : 'In-Kind Contribution' }}</th>
            <th class="text-start">{{ isArabic() ? 'إجمالي المساهمة' : 'Total Contribution' }}</th>
            <th class="text-start">{{ isArabic() ? 'رقم الرخصه' : 'license No' }}</th>
          </tr>
        </thead>
        <tbody *ngIf="!loading">
          <tr *ngFor="let owner of owners">
            <td class="text-start">{{ isArabic() ? owner.nameAr : owner.nameEn }}</td>
            <td class="text-start">{{ isArabic() ? owner.identityTypeAr || '-' : owner.identityTypeEn || '-' }}</td>
            <td class="text-start">{{ owner.identityCode ? owner.identityCode : '-' }}</td>
            <td class="text-start">{{ owner.nationalityCode ? owner.nationalityCode : '-' }}</td>
            <td class="text-start">{{ isArabic() ? owner.nationalityNameAr || '-' : owner.nationalityNameEn || '-' }}</td>
            <td class="text-start">{{ isArabic() ? owner.relationAr || '-' : owner.relationEn || '-' }}</td>
            <td class="text-start">{{ owner.companyId || '-' }}</td>
            <td class="text-start">{{ owner.cashContributionCount }}</td>
            <td class="text-start">{{ owner.inKindContributionCount }}</td>
            <td class="text-start">{{ owner.totalContributionCount }}</td>
            <td class="text-start">{{ owner.licenseNo }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <p id="jhi-permitRq9" data-cy="PermitRequestCreateUpdateHeading" class="headFont">
    {{ isArabic() ? 'بيانات المديرين' : 'Managers ' }}
  </p>

  <div class="mb-5">
    <div class="table-responsive">
      <table class="table table-striped dga-table" aria-describedby="managersTable">
        <thead>
          <tr>
            <th class="text-start">{{ isArabic() ? 'الاسم' : 'Name' }}</th>
            <th class="text-start">{{ isArabic() ? 'نوع الشخص' : 'Type' }}</th>
            <th class="text-start">{{ isArabic() ? 'رقم الهوية' : 'Identity Number' }}</th>
            <th class="text-start">{{ isArabic() ? 'نوع الهوية' : 'Identity Type' }}</th>
            <th class="text-start">{{ isArabic() ? 'الجنسية' : 'Nationality' }}</th>
            <th class="text-start">{{ isArabic() ? 'المناصب' : 'Positions' }}</th>
            <th class="text-start">{{ isArabic() ? 'مرخّص' : 'Licensed' }}</th>
          </tr>
        </thead>
        <tbody *ngIf="isArabic()">
          <tr *ngFor="let person of managers">
            <td class="text-start">{{ person.name }}</td>
            <td class="text-start">{{ person.typeName }}</td>
            <td class="text-start">{{ person.identity?.id || '-' }}</td>
            <td class="text-start">{{ person.identity?.typeName || '-' }}</td>
            <td class="text-start">{{ person.nationality?.name || '-' }}</td>
            <td class="text-start">
              <ul class="mb-0 ps-3">
                <li *ngFor="let pos of person.positions">{{ pos.name }}</li>
              </ul>
            </td>
            <td class="text-start">
              {{ isArabic() ? (person.licensed ? 'نعم' : 'لا') : person.licensed ? 'Yes' : 'No' }}
            </td>
          </tr>
        </tbody>
        <tbody *ngIf="!isArabic()">
          <tr *ngFor="let person of managers">
            <td class="text-start">{{ person.name }}</td>
            <td class="text-start">{{ person.typeName }}</td>
            <td class="text-start">{{ person.identity?.id || '-' }}</td>
            <td class="text-start">{{ person.identity?.typeName || '-' }}</td>
            <td class="text-start">{{ person.nationality?.name || '-' }}</td>
            <td class="text-start">
              <ul class="mb-0 ps-3">
                <li *ngFor="let pos of person.positions">{{ pos.name }}</li>
              </ul>
            </td>
            <td class="text-start">
              {{ isArabic() ? (person.licensed ? 'نعم' : 'لا') : person.licensed ? 'Yes' : 'No' }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
