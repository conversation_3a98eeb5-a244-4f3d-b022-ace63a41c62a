package com.elm.srsa.mofa.client.request;

public class InsuranceQuestionsDTO {

    private boolean areYouDisabledByAccident;
    private boolean isCurrentlyInHospitalOrTakingEmerTreatments;
    private boolean youHaveWeaknessOrCongenitalPlatformation;
    private boolean areYouPregnant;
    private boolean isNoramalPregnancy;
    private int howManyMonthsOfPregnancy;

    public boolean isAreYouDisabledByAccident() {
        return areYouDisabledByAccident;
    }

    public void setAreYouDisabledByAccident(boolean areYouDisabledByAccident) {
        this.areYouDisabledByAccident = areYouDisabledByAccident;
    }

    public boolean isCurrentlyInHospitalOrTakingEmerTreatments() {
        return isCurrentlyInHospitalOrTakingEmerTreatments;
    }

    public void setCurrentlyInHospitalOrTakingEmerTreatments(boolean currentlyInHospitalOrTakingEmerTreatments) {
        isCurrentlyInHospitalOrTakingEmerTreatments = currentlyInHospitalOrTakingEmerTreatments;
    }

    public boolean isYouHaveWeaknessOrCongenitalPlatformation() {
        return youHaveWeaknessOrCongenitalPlatformation;
    }

    public void setYouHaveWeaknessOrCongenitalPlatformation(boolean youHaveWeaknessOrCongenitalPlatformation) {
        this.youHaveWeaknessOrCongenitalPlatformation = youHaveWeaknessOrCongenitalPlatformation;
    }

    public boolean isAreYouPregnant() {
        return areYouPregnant;
    }

    public void setAreYouPregnant(boolean areYouPregnant) {
        this.areYouPregnant = areYouPregnant;
    }

    public boolean isNoramalPregnancy() {
        return isNoramalPregnancy;
    }

    public void setNoramalPregnancy(boolean noramalPregnancy) {
        isNoramalPregnancy = noramalPregnancy;
    }

    public int getHowManyMonthsOfPregnancy() {
        return howManyMonthsOfPregnancy;
    }

    public void setHowManyMonthsOfPregnancy(int howManyMonthsOfPregnancy) {
        this.howManyMonthsOfPregnancy = howManyMonthsOfPregnancy;
    }
    // Getters and Setters
    // ... (similar to above)
}
