import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { map, Observable } from 'rxjs';

import dayjs from 'dayjs/esm';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { IPermitRequest, NewPermitRequest } from '../permit-request.model';
import { FileKeyValuePair } from '../update/permit-request-update.component';
import { IServiceRating } from '../service-rate.model';
import { ILicenseRate } from '../license-rate.model';
import { IPermitStatistics } from '../permit-statistics.model';
import { IPermitChanges, PermitChanges } from '../permit-changes.model';
import { IBeachActivities } from '../beach-activities.model';
import { IBeachEquipment } from '../beach-equipment.model';

export type PartialUpdatePermitRequest = Partial<IPermitRequest> & Pick<IPermitRequest, 'id'>;

type RestOf<T extends IPermitRequest | NewPermitRequest | ILicenseRate | IPermitStatistics> = Omit<T, 'submitDate'> & {
  submitDate?: string | null;
};

export type RestPermitRequest = RestOf<IPermitRequest>;
export type RestILicenseRate = RestOf<ILicenseRate>;
export type RestIPermitStatistics = RestOf<IPermitStatistics>;
export type NewRestPermitRequest = RestOf<NewPermitRequest>;

export type PartialUpdateRestPermitRequest = RestOf<PartialUpdatePermitRequest>;

export type EntityResponseType = HttpResponse<IPermitRequest>;
export type EntityArrayResponseType = HttpResponse<IPermitRequest[]>;
export type licenseRateArrayResponseType = HttpResponse<ILicenseRate[]>;
export type permitStatisticsArrayResponseType = HttpResponse<IPermitStatistics[]>;
export type statisticsArrayResponseType = HttpResponse<IPermitRequest[]>;
export type EntityNumberResponseType = HttpResponse<number>;

@Injectable({ providedIn: 'root' })
export class PermitRequestService {
  protected http = inject(HttpClient);
  protected applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/permit-requests');
  protected resourceReportUrl = this.applicationConfigService.getEndpointFor('api/report-info/permits');
  protected countReportUrl = this.applicationConfigService.getEndpointFor('api/report-info/count-permits');
  protected statisticsReportUrl = this.applicationConfigService.getEndpointFor('api/report-info/permits-statistics');
  protected licenseRateReportUrl = this.applicationConfigService.getEndpointFor('api/report-info/license-rate');
  protected ratingResourceUrl = this.applicationConfigService.getEndpointFor('api/service-rating');

  create(permitRequest: NewPermitRequest, files: FileKeyValuePair[]): Observable<EntityResponseType> {
    // const copy = this.convertDateFromClient(permitRequest);
    const formData: any = new FormData();
    formData.append('jsonObject', JSON.stringify(permitRequest));

    if (files !== undefined && files.length > 0) {
      files.forEach(fileObject => {
        // for (const key in fileObject) {
        if (fileObject.file !== undefined) {
          formData.append('documents', fileObject.file, fileObject.key);
        }
      });
    } else {
      formData.append('documents', new File([''], 'no-file-h'));
    }
    return this.http
      .post<RestPermitRequest>(this.resourceUrl, formData, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  update(permitRequest: IPermitRequest, files: FileKeyValuePair[]): Observable<EntityResponseType> {
    // const copy = this.convertDateFromClient(permitRequest);
    const formData: any = new FormData();
    formData.append('jsonObject', JSON.stringify(permitRequest));

    if (files !== undefined && files.length > 0) {
      files.forEach(fileObject => {
        // for (const key in fileObject) {
        if (fileObject.file !== undefined) {
          formData.append('documents', fileObject.file, fileObject.key);
        }
      });
    } else {
      formData.append('documents', new File([''], 'no-file-h'));
    }
    return this.http
      .post<RestPermitRequest>(`${this.resourceUrl}/${this.getPermitRequestIdentifier(permitRequest)}`, formData, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  partialUpdate(permitRequest: PartialUpdatePermitRequest): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(permitRequest);
    return this.http
      .patch<RestPermitRequest>(`${this.resourceUrl}/${this.getPermitRequestIdentifier(permitRequest)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  find(id: number): Observable<EntityResponseType> {
    return this.http
      .get<RestPermitRequest>(`${this.resourceUrl}/${id}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestPermitRequest[]>(this.resourceUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }
  queryReport(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestPermitRequest[]>(this.resourceReportUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }
  countReport(req?: any): Observable<EntityNumberResponseType> {
    const options = createRequestOption(req);
    return this.http.get<number>(this.countReportUrl, { params: options, observe: 'response' });
  }

  queryStatistics(req?: any): Observable<permitStatisticsArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<RestIPermitStatistics[]>(this.statisticsReportUrl, { params: options, observe: 'response' });
  }

  queryLicenseRate(req?: any): Observable<licenseRateArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<RestILicenseRate[]>(this.licenseRateReportUrl, { params: options, observe: 'response' });
  }

  queryLicenses(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestPermitRequest[]>(this.resourceUrl + '/licenses', { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  delete(id: number): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  getPermitRequestIdentifier(permitRequest: Pick<IPermitRequest, 'id'>): number {
    return permitRequest.id;
  }

  comparePermitRequest(o1: Pick<IPermitRequest, 'id'> | null, o2: Pick<IPermitRequest, 'id'> | null): boolean {
    return o1 && o2 ? this.getPermitRequestIdentifier(o1) === this.getPermitRequestIdentifier(o2) : o1 === o2;
  }

  addPermitRequestToCollectionIfMissing<Type extends Pick<IPermitRequest, 'id'>>(
    permitRequestCollection: Type[],
    ...permitRequestsToCheck: (Type | null | undefined)[]
  ): Type[] {
    const permitRequests: Type[] = permitRequestsToCheck.filter(isPresent);
    if (permitRequests.length > 0) {
      const permitRequestCollectionIdentifiers = permitRequestCollection.map(permitRequestItem =>
        this.getPermitRequestIdentifier(permitRequestItem),
      );
      const permitRequestsToAdd = permitRequests.filter(permitRequestItem => {
        const permitRequestIdentifier = this.getPermitRequestIdentifier(permitRequestItem);
        if (permitRequestCollectionIdentifiers.includes(permitRequestIdentifier)) {
          return false;
        }
        permitRequestCollectionIdentifiers.push(permitRequestIdentifier);
        return true;
      });
      return [...permitRequestsToAdd, ...permitRequestCollection];
    }
    return permitRequestCollection;
  }

  exportPdf(permitRequestId: number | undefined): Observable<ArrayBuffer> {
    const resourceUrl = this.resourceUrl + '/export-pdf';
    return this.http.get(`${resourceUrl}/${permitRequestId}`, { responseType: 'arraybuffer' });
  }

  getAttachmentById(id: number): Observable<Blob> {
    return this.http.get(`${this.resourceUrl}/getAttachment/${id}`, { responseType: 'blob' });
  }

  getAttachmentVisit(visitId: any): Observable<any> {
    let endpointFor = this.applicationConfigService.getEndpointFor('api/permit-requests/getAttachmentVisitFile/');
    let queryParams = new HttpParams();
    // @ts-ignore
    queryParams = queryParams.append('permitVisit', visitId);
    // @ts-ignore
    return this.http.get(this.resourceUrl + '/getAttachmentVisitFile', {
      responseType: 'blob',
      params: queryParams,
    });
  }

  submitServiceRate(serviceRating: IServiceRating): Observable<IServiceRating> {
    return this.http.post<IServiceRating>(this.ratingResourceUrl, serviceRating);
  }

  getLicensingOfficers(): Observable<any> {
    const resourceUrl = this.resourceUrl + '/getLicensingOfficers';
    return this.http.get(resourceUrl, { observe: 'response' });
  }

  selectLicensingOfficers(permit: any, assignedEmployee: any): Observable<any> {
    const resourceUrl = this.resourceUrl + '/selectLicensingOfficer';
    return this.http.post(resourceUrl, { permitRequestDTO: permit, adminUserDTO: assignedEmployee });
  }
  getLicensingManagers(): Observable<any> {
    const resourceUrl = this.resourceUrl + '/getLicensingManagers';
    return this.http.get(resourceUrl, { observe: 'response' });
  }

  selectLicensingManagers(permit: any, assignedEmployee: any): Observable<any> {
    const resourceUrl = this.resourceUrl + '/selectLicensingManagers';
    return this.http.post(resourceUrl, { permitRequestDTO: permit, adminUserDTO: assignedEmployee });
  }
  getInspectionOfficers(): Observable<any> {
    const resourceUrl = this.resourceUrl + '/getInspectionOfficers';
    return this.http.get(resourceUrl, { observe: 'response' });
  }

  selectInspectionOfficers(permit: any, assignedEmployee: any): Observable<any> {
    const resourceUrl = this.resourceUrl + '/selectInspectionOfficers';
    return this.http.post(resourceUrl, { permitRequestDTO: permit, adminUserDTO: assignedEmployee });
  }

  returnPermitRequest(permitRequestDTO: any, returnOption?: string): Observable<any> {
    permitRequestDTO.attachments = null;
    permitRequestDTO.returnOption = returnOption;
    const resourceUrl = this.resourceUrl + '/returnPermitRequest';
    return this.http.post(resourceUrl, permitRequestDTO);
  }
  unAssignPermitRequest(permitRequestDTO: any): Observable<any> {
    permitRequestDTO.attachments = null;
    const resourceUrl = this.resourceUrl + '/unAssignPermitRequest';
    return this.http.post(resourceUrl, permitRequestDTO);
  }
  rejectPermitRequest(permitRequestDTO: any): Observable<any> {
    permitRequestDTO.attachments = null;
    const resourceUrl = this.resourceUrl + '/rejectPermitRequest';
    return this.http.post(resourceUrl, permitRequestDTO);
  }
  sendToPayPermitRequest(permitRequestDTO: any, files: FileKeyValuePair[]): Observable<any> {
    permitRequestDTO.attachments = null;
    const formData: any = new FormData();
    formData.append('jsonObject', JSON.stringify(permitRequestDTO));

    if (files !== undefined && files.length > 0) {
      files.forEach(fileObject => {
        // for (const key in fileObject) {
        if (fileObject.file !== undefined) {
          formData.append('documents', fileObject.file, fileObject.key);
        } else {
          formData.append('documents', new File([''], 'no-file-h'));
        }
        // }
      });
    } else {
      formData.append('documents', new File([''], 'no-file-h'));
    }

    const resourceUrl = this.resourceUrl + '/sendToPayPermitRequest';
    return this.http.post(resourceUrl, formData);
  }

  getPermitVisit(permitRequest: any): Observable<any> {
    permitRequest.attachments = null;
    const resourceUrl = this.resourceUrl + '/getPermitVisit';
    return this.http.post(resourceUrl, permitRequest);
  }

  getPermitChanges(requestNumber: string): Observable<any> {
    return this.http.get(`${this.resourceUrl}/getPermitChanges/${requestNumber}`);
  }

  getBeachActivities(): Observable<IBeachActivities[]> {
    return this.http.get<IBeachActivities[]>(`${this.resourceUrl}/beachActivities`);
  }

  getBeachEquipment(): Observable<IBeachEquipment[]> {
    return this.http.get<IBeachEquipment[]>(`${this.resourceUrl}/beachEquipment`);
  }

  /**
   * Check if the current company has an active yacht rental facility permit
   * @returns Observable<boolean> true if the company has an active yacht rental facility permit, false otherwise
   */
  hasYachtRentalFacility(): Observable<boolean> {
    return this.http.get<boolean>(`${this.resourceUrl}/SP/hasYachtRentalFacility`);
  }
  getCompletedPermitVisit(permitRequest: any): Observable<any> {
    permitRequest.attachments = null;
    const resourceUrl = this.resourceUrl + '/getCompletedPermitVisit';
    return this.http.post(resourceUrl, permitRequest);
  }

  protected convertDateFromClient<T extends IPermitRequest | NewPermitRequest | PartialUpdatePermitRequest>(permitRequest: T): RestOf<T> {
    return {
      ...permitRequest,
      submitDate: permitRequest.submitDate?.toJSON() ?? null,
    };
  }

  protected convertDateFromServer(restPermitRequest: RestPermitRequest): IPermitRequest {
    return {
      ...restPermitRequest,
      submitDate: restPermitRequest.submitDate ? dayjs(restPermitRequest.submitDate) : undefined,
    };
  }

  protected convertResponseFromServer(res: HttpResponse<RestPermitRequest>): HttpResponse<IPermitRequest> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }

  protected convertResponseArrayFromServer(res: HttpResponse<RestPermitRequest[]>): HttpResponse<IPermitRequest[]> {
    return res.clone({
      body: res.body ? res.body.map(item => this.convertDateFromServer(item)) : null,
    });
  }

  licensingOfficerSubmit(needVisit: any, permitRequestDTO: any): Observable<any> {
    permitRequestDTO.attachments = null;
    const resourceUrl = this.resourceUrl + '/licensingOfficerSubmit/' + needVisit;
    return this.http.post(resourceUrl, permitRequestDTO);
  }

  sendToInspectionManager(files: FileKeyValuePair[], permitRequestDTO: any, visitDate: any): Observable<any> {
    permitRequestDTO.attachments = null;
    const formData: any = new FormData();
    formData.append('jsonObject', JSON.stringify(permitRequestDTO));
    formData.append('visitDate', JSON.stringify(visitDate));

    if (files !== undefined && files.length > 0) {
      files.forEach(fileObject => {
        // for (const key in fileObject) {
        if (fileObject.file !== undefined) {
          formData.append('documents', fileObject.file, fileObject.key);
        } else {
          formData.append('documents', new File([''], 'no-file-h'));
        }
        // }
      });
    } else {
      formData.append('documents', new File([''], 'no-file-h'));
    }
    const resourceUrl = this.resourceUrl + '/sendToInspectionManager';
    return this.http.post(resourceUrl, formData);
  }
  editVisitReport(files: FileKeyValuePair[], permitRequestDTO: any): Observable<any> {
    permitRequestDTO.attachments = null;
    const formData: any = new FormData();
    formData.append('jsonObject', JSON.stringify(permitRequestDTO));

    if (files !== undefined) {
      files.forEach(fileObject => {
        // for (const key in fileObject) {
        if (fileObject.file !== undefined) {
          formData.append('documents', fileObject.file, fileObject.key);
        } else {
          formData.append('documents', new File([''], 'no-file-h'));
        }
        // }
      });
    }
    const resourceUrl = this.resourceUrl + '/editVisitReport';
    return this.http.post(resourceUrl, formData);
  }
  scheduleVisit(files: FileKeyValuePair[], permitRequestDTO: any, visitDate: any): Observable<any> {
    permitRequestDTO.attachments = null;
    const formData: any = new FormData();
    formData.append('jsonObject', JSON.stringify(permitRequestDTO));
    formData.append('visitDate', JSON.stringify(visitDate));

    if (files !== undefined) {
      files.forEach(fileObject => {
        // for (const key in fileObject) {
        if (fileObject.file !== undefined) {
          formData.append('documents', fileObject.file, fileObject.key);
        } else {
          formData.append('documents', new File([''], 'no-file-h'));
        }
        // }
      });
    }
    const resourceUrl = this.resourceUrl + '/scheduleVisit';
    return this.http.post(resourceUrl, formData);
  }
}
