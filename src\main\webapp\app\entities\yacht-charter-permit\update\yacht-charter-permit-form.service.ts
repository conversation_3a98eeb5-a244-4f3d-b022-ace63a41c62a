import { Injectable } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

import dayjs from 'dayjs/esm';
import { DATE_FORMAT, DATE_TIME_FORMAT } from 'app/config/input.constants';
import { IYachtCharterPermit, NewYachtCharterPermit } from '../yacht-charter-permit.model';
import { ICompany } from '../../company/company.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IYachtCharterPermit for edit and NewYachtCharterPermitFormGroupInput for create.
 */
type YachtCharterPermitFormGroupInput = IYachtCharterPermit | PartialWithRequiredKeyOf<NewYachtCharterPermit>;

/**
 * Type that converts some properties for forms.
 */
type FormValueOf<T extends IYachtCharterPermit | NewYachtCharterPermit> = Omit<
  T,
  'arrivalDate' | 'departureDate' | 'startDate' | 'endDate'
> & {
  arrivalDate?: string | null;
  departureDate?: string | null;
  startDate?: string | null;
  endDate?: string | null;
};

type YachtCharterPermitFormRawValue = FormValueOf<IYachtCharterPermit>;

type NewYachtCharterPermitFormRawValue = FormValueOf<NewYachtCharterPermit>;

type YachtCharterPermitFormDefaults = Pick<
  NewYachtCharterPermit,
  'id' | 'arrivalDate' | 'departureDate' | 'startDate' | 'endDate' | 'disclosureChecked'
>;

type YachtCharterPermitFormGroupContent = {
  id: FormControl<YachtCharterPermitFormRawValue['id'] | NewYachtCharterPermit['id']>;
  marineOwnerNameAr: FormControl<YachtCharterPermitFormRawValue['marineOwnerNameAr']>;
  marineOwnerNameEn: FormControl<YachtCharterPermitFormRawValue['marineOwnerNameEn']>;
  marineOwnerIdNumber: FormControl<YachtCharterPermitFormRawValue['marineOwnerIdNumber']>;
  marineOwnerPassportNumber: FormControl<YachtCharterPermitFormRawValue['marineOwnerPassportNumber']>;
  delegatorNameAr: FormControl<YachtCharterPermitFormRawValue['delegatorNameAr']>;
  delegatorNameEn: FormControl<YachtCharterPermitFormRawValue['delegatorNameEn']>;
  delegatorMobileNumber: FormControl<YachtCharterPermitFormRawValue['delegatorMobileNumber']>;
  delegatorEmailNumber: FormControl<YachtCharterPermitFormRawValue['delegatorEmailNumber']>;
  permitNumber: FormControl<YachtCharterPermitFormRawValue['permitNumber']>;
  requestStatus: FormControl<YachtCharterPermitFormRawValue['requestStatus']>;
  invTypeId: FormControl<YachtCharterPermitFormRawValue['invTypeId']>;
  misaLicenseNumber: FormControl<YachtCharterPermitFormRawValue['misaLicenseNumber']>;
  eczaLicenseNumber: FormControl<YachtCharterPermitFormRawValue['eczaLicenseNumber']>;
  marineColor: FormControl<YachtCharterPermitFormRawValue['marineColor']>;
  captainName: FormControl<YachtCharterPermitFormRawValue['captainName']>;
  captainMobileNumber: FormControl<YachtCharterPermitFormRawValue['captainMobileNumber']>;
  captainPassportNumber: FormControl<YachtCharterPermitFormRawValue['captainPassportNumber']>;
  arrivalDate: FormControl<YachtCharterPermitFormRawValue['arrivalDate']>;
  departureDate: FormControl<YachtCharterPermitFormRawValue['departureDate']>;
  startDate: FormControl<YachtCharterPermitFormRawValue['startDate']>;
  endDate: FormControl<YachtCharterPermitFormRawValue['endDate']>;
  disclosureChecked: FormControl<YachtCharterPermitFormRawValue['disclosureChecked']>;
  marineMedium: FormControl<YachtCharterPermitFormRawValue['marineMedium']>;
  marineCompanyOwner: FormControl<YachtCharterPermitFormRawValue['marineCompanyOwner']>;
  marineOwner: FormControl<YachtCharterPermitFormRawValue['marineOwner']>;
  marineDelegatorUser: FormControl<YachtCharterPermitFormRawValue['marineDelegatorUser']>;
  marineDelegatorCompany: FormControl<YachtCharterPermitFormRawValue['marineDelegatorCompany']>;
  arrivalPort: FormControl<YachtCharterPermitFormRawValue['arrivalPort']>;
  departurePort: FormControl<YachtCharterPermitFormRawValue['departurePort']>;
  captainNatinoality: FormControl<YachtCharterPermitFormRawValue['captainNatinoality']>;
  anchorageArea: FormControl<YachtCharterPermitFormRawValue['anchorageArea']>;
  charterName: FormControl<YachtCharterPermitFormRawValue['charterName']>;
  charterNumber: FormControl<YachtCharterPermitFormRawValue['charterNumber']>;
  licenseProvider: FormControl<YachtCharterPermitFormRawValue['licenseProvider']>;
};

export type YachtCharterPermitFormGroup = FormGroup<YachtCharterPermitFormGroupContent>;

export function noNumbersValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle it
    }

    const hasNumbers = /\d/.test(control.value);
    return hasNumbers ? { hasNumbers: { value: control.value } } : null;
  };
}

@Injectable({ providedIn: 'root' })
export class YachtCharterPermitFormService {
  createYachtCharterPermitFormGroup(yachtCharterPermit: YachtCharterPermitFormGroupInput = { id: null }): YachtCharterPermitFormGroup {
    const yachtCharterPermitRawValue = this.convertYachtCharterPermitToYachtCharterPermitRawValue({
      ...this.getFormDefaults(),
      ...yachtCharterPermit,
    });
    return new FormGroup<YachtCharterPermitFormGroupContent>({
      id: new FormControl(
        { value: yachtCharterPermitRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      marineOwnerNameAr: new FormControl(yachtCharterPermitRawValue.marineOwnerNameAr),
      marineOwnerNameEn: new FormControl(yachtCharterPermitRawValue.marineOwnerNameEn),
      marineOwnerIdNumber: new FormControl(yachtCharterPermitRawValue.marineOwnerIdNumber),
      marineOwnerPassportNumber: new FormControl(yachtCharterPermitRawValue.marineOwnerPassportNumber),
      delegatorNameAr: new FormControl(yachtCharterPermitRawValue.delegatorNameAr),
      delegatorNameEn: new FormControl(yachtCharterPermitRawValue.delegatorNameEn),
      delegatorMobileNumber: new FormControl(yachtCharterPermitRawValue.delegatorMobileNumber),
      delegatorEmailNumber: new FormControl(yachtCharterPermitRawValue.delegatorEmailNumber),
      permitNumber: new FormControl(yachtCharterPermitRawValue.permitNumber),
      requestStatus: new FormControl(yachtCharterPermitRawValue.requestStatus),
      invTypeId: new FormControl(yachtCharterPermitRawValue.invTypeId),
      misaLicenseNumber: new FormControl(yachtCharterPermitRawValue.misaLicenseNumber, [Validators.minLength(2), Validators.maxLength(20)]),
      eczaLicenseNumber: new FormControl(yachtCharterPermitRawValue.eczaLicenseNumber, [Validators.minLength(2), Validators.maxLength(20)]),
      marineColor: new FormControl(yachtCharterPermitRawValue.marineColor),
      captainName: new FormControl(yachtCharterPermitRawValue.captainName, { validators: [Validators.required, noNumbersValidator()] }),
      captainMobileNumber: new FormControl(yachtCharterPermitRawValue.captainMobileNumber),
      captainPassportNumber: new FormControl(yachtCharterPermitRawValue.captainPassportNumber),
      arrivalDate: new FormControl(yachtCharterPermitRawValue.arrivalDate),
      departureDate: new FormControl(yachtCharterPermitRawValue.departureDate),
      startDate: new FormControl(yachtCharterPermitRawValue.startDate, { validators: [Validators.required] }),
      endDate: new FormControl(yachtCharterPermitRawValue.endDate, { validators: [Validators.required] }),
      disclosureChecked: new FormControl(yachtCharterPermitRawValue.disclosureChecked),
      marineMedium: new FormControl(yachtCharterPermitRawValue.marineMedium),
      marineCompanyOwner: new FormControl(yachtCharterPermitRawValue.marineCompanyOwner),
      marineOwner: new FormControl(yachtCharterPermitRawValue.marineOwner),
      marineDelegatorUser: new FormControl(yachtCharterPermitRawValue.marineDelegatorUser),
      marineDelegatorCompany: new FormControl(yachtCharterPermitRawValue.marineDelegatorCompany),
      arrivalPort: new FormControl(yachtCharterPermitRawValue.arrivalPort, { validators: [Validators.required] }),
      departurePort: new FormControl(yachtCharterPermitRawValue.departurePort),
      captainNatinoality: new FormControl(yachtCharterPermitRawValue.captainNatinoality),
      anchorageArea: new FormControl(yachtCharterPermitRawValue.anchorageArea, { validators: [Validators.required, noNumbersValidator()] }),
      charterName: new FormControl(yachtCharterPermitRawValue.charterName, { validators: [Validators.required, noNumbersValidator()] }),
      charterNumber: new FormControl(yachtCharterPermitRawValue.charterNumber, { validators: [Validators.required] }),
      licenseProvider: new FormControl(yachtCharterPermitRawValue.licenseProvider),
    });
  }

  getYachtCharterPermit(form: YachtCharterPermitFormGroup): IYachtCharterPermit | NewYachtCharterPermit {
    return this.convertYachtCharterPermitRawValueToYachtCharterPermit(
      form.getRawValue() as YachtCharterPermitFormRawValue | NewYachtCharterPermitFormRawValue,
    );
  }

  resetForm(form: YachtCharterPermitFormGroup, yachtCharterPermit: YachtCharterPermitFormGroupInput): void {
    const yachtCharterPermitRawValue = this.convertYachtCharterPermitToYachtCharterPermitRawValue({
      ...this.getFormDefaults(),
      ...yachtCharterPermit,
    });
    form.reset(
      {
        ...yachtCharterPermitRawValue,
        id: { value: yachtCharterPermitRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): YachtCharterPermitFormDefaults {
    const currentTime = dayjs();

    return {
      id: null,
      arrivalDate: currentTime,
      departureDate: currentTime,
      startDate: currentTime,
      endDate: currentTime,
      disclosureChecked: false,
    };
  }

  private convertYachtCharterPermitRawValueToYachtCharterPermit(
    rawYachtCharterPermit: YachtCharterPermitFormRawValue | NewYachtCharterPermitFormRawValue,
  ): IYachtCharterPermit | NewYachtCharterPermit {
    return {
      ...rawYachtCharterPermit,
      arrivalDate: dayjs(rawYachtCharterPermit.arrivalDate, DATE_TIME_FORMAT),
      departureDate: dayjs(rawYachtCharterPermit.departureDate, DATE_TIME_FORMAT),
      startDate: dayjs(rawYachtCharterPermit.startDate, DATE_FORMAT),
      endDate: dayjs(rawYachtCharterPermit.endDate, DATE_FORMAT),
    };
  }

  private convertYachtCharterPermitToYachtCharterPermitRawValue(
    yachtCharterPermit: IYachtCharterPermit | (Partial<NewYachtCharterPermit> & YachtCharterPermitFormDefaults),
  ): YachtCharterPermitFormRawValue | PartialWithRequiredKeyOf<NewYachtCharterPermitFormRawValue> {
    return {
      ...yachtCharterPermit,
      arrivalDate: yachtCharterPermit.arrivalDate ? yachtCharterPermit.arrivalDate.format(DATE_TIME_FORMAT) : undefined,
      departureDate: yachtCharterPermit.departureDate ? yachtCharterPermit.departureDate.format(DATE_TIME_FORMAT) : undefined,
      startDate: yachtCharterPermit.startDate ? yachtCharterPermit.startDate.format(DATE_FORMAT) : undefined,
      endDate: yachtCharterPermit.endDate ? yachtCharterPermit.endDate.format(DATE_FORMAT) : undefined,
      anchorageArea: yachtCharterPermit.anchorageArea ? yachtCharterPermit.anchorageArea : undefined,
      marineMedium: yachtCharterPermit.marineMedium ? yachtCharterPermit.marineMedium : undefined,
      marineCompanyOwner: yachtCharterPermit.marineCompanyOwner ? yachtCharterPermit.marineCompanyOwner : undefined,
      marineDelegatorUser: yachtCharterPermit.marineDelegatorUser ? yachtCharterPermit.marineDelegatorUser : undefined,
      marineDelegatorCompany: yachtCharterPermit.marineDelegatorCompany ? yachtCharterPermit.marineDelegatorCompany : undefined,
      arrivalPort: yachtCharterPermit.arrivalPort ? yachtCharterPermit.arrivalPort : undefined,
      charterName: yachtCharterPermit.charterName ? yachtCharterPermit.charterName : undefined,
      charterNumber: yachtCharterPermit.charterNumber ? yachtCharterPermit.charterNumber : undefined,
      company: yachtCharterPermit.company ? yachtCharterPermit.company : undefined,
      secondaryCompany: yachtCharterPermit.secondaryCompany ? yachtCharterPermit.secondaryCompany : undefined,
      licenseProvider: yachtCharterPermit.licenseProvider ? yachtCharterPermit.licenseProvider : undefined,
    };
  }
}
