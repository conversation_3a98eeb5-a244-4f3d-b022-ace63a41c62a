import { Component, inject, Input, OnInit } from '@angular/core';
import { IServiceTypeLiabilityType } from '../../service-type-liability-type/service-type-liability-type.model';
import { IServiceType } from '../../service-type/service-type.model';
import HasAnyAuthorityDirective from '../../../shared/auth/has-any-authority.directive';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { SharedLibsModule } from '../../../shared/shared-libs.module';
import { PageTitleComponent } from '../../../layouts/sub/page-title/page-title.component';
import { TabViewModule } from 'primeng/tabview';
import TranslateDirective from '../../../shared/language/translate.directive';
import { TranslateService } from '@ngx-translate/core';
import { ServiceTypeDefinitionCardComponent } from '../../../shared/service-type-definition-card/service-type-definition-card.component';
import { RequestRequirementsComponent } from '../../../shared/request-requeirments/request-requirements.component';
import { ServiceTypeService } from '../../service-type/service/service-type.service';
import { RequestWizardStepperComponent, Step } from '../../../shared/request-wizard-steper/request-wizard-stepper.component';
import { AccountService } from '../../../core/auth/account.service';
import { FormStepSelectOrgCrComponent } from '../../../shared/form-step-select-org-cr/form-step-select-org-cr.component';
import { map } from 'rxjs/operators';
import { HttpResponse } from '@angular/common/http';
import { ICompany } from '../../company/company.model';
import { CompanyService } from '../../company/service/company.service';
import { TermsConditionsModalComponent } from '../../../shared/terms-conditions-modal/terms-conditions-modal.component';
import { FormStepSelectMarineMediumComponent } from '../../../shared/form-step-select-marine-medium/form-step-select-marine-medium.component';
import { VisaFormStepAddApplicantComponent } from './form/visa-form-step-add-applicant/visa-form-step-add-applicant.component';
import { FormStepUploadAttachmentsComponent } from './form/form-step-upload-attachments/form-step-upload-attachments.component';
import { VisaFormStepRequestPreviewComponent } from './form/visa-form-step-request-privew/visa-form-step-request-preview.component';
import { PageTitleService } from '../../../core/util/page-title.service';
import { MarineMedium } from '../../marine-medium/model/marine-medium.model';
import { MarineMediumService } from '../../marine-medium/marine-medium.service';
import { VisaRequestService } from '../visa-tourism-request.service';

@Component({
  selector: 'jhi-visa-request-update',
  standalone: true,
  imports: [
    HasAnyAuthorityDirective,
    SharedLibsModule,
    PageTitleComponent,
    TabViewModule,
    TranslateDirective,
    ServiceTypeDefinitionCardComponent,
    RequestRequirementsComponent,
    RequestWizardStepperComponent,
    FormStepSelectOrgCrComponent,
    TermsConditionsModalComponent,
    FormStepSelectMarineMediumComponent,
    VisaFormStepAddApplicantComponent,
    FormStepUploadAttachmentsComponent,
    VisaFormStepRequestPreviewComponent,
  ],
  templateUrl: './visa-request-update.component.html',
  styleUrl: './visa-request-update.component.scss',
})
export class VisaRequestUpdateComponent implements OnInit {
  protected translateService = inject(TranslateService);
  serviceTypeService = inject(ServiceTypeService);
  protected accountService = inject(AccountService);
  protected companyService = inject(CompanyService);
  protected pageTitleService = inject(PageTitleService);
  protected marineMediumService = inject(MarineMediumService);

  step = 1;
  serviceDefinitionPage = true;
  wizardDisplay = false;
  disclaimerChecked = false;
  liabilityChecked = false;
  selectedServiceType: IServiceType | null = null;
  editForm!: FormGroup;
  editMode = false;
  requiredDocuments: Step[] = [];
  businessRoles: Step[] = [];
  TARGET_AUDIENCE: any[] = [];
  steps: any[] = [];
  userIndividual: boolean = false;
  userServiceProvider: boolean = false;
  selectedMarineMedium: any | null = null;
  marineMediums: MarineMedium[] = [];
  STEP_SELECT_MARINE_MEDIUM = 1;
  STEP_VISA_FORM_ADD_APPLICANT = 2;
  STEP_ADD_ATTACHMENTS = 3;
  STEP_SELECT_REQUEST_PREVIEW = 4;
  SERVICE_TYPE_CODE: string = 'TVR';
  constructor(
    private fb: FormBuilder,
    private visaRequestService: VisaRequestService,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.pageTitleService.setBreadcrumb(this.translateService.instant('srsaApp.visaTourism.home.title'), '/visa-tourism');
    this.initStaticData();
    this.initLoggedInUserRole();
    this.buildForm();
    this.fetchServiceType();
    this.loadAllMarineMediums();
    this.initForCreateOrUpdate();
  }
  initLoggedInUserRole() {
    if (this.accountService.hasAnyAuthority('ROLE_INDIVIDUAL_USER')) {
      this.userIndividual = true;
    } else {
      this.userServiceProvider = true;
    }
  }
  initStaticData() {
    this.TARGET_AUDIENCE = [
      {
        ar: 'أفراد أجانب',
        en: 'Foreign Individual',
      },
      {
        ar: 'وكيل ملاح سياحي مفوض',
        en: 'Authorized Tourism Navigation Agent',
      },
      {
        ar: 'منشأة سعودية مالكة ليخت',
        en: 'Saudi Yacht Owner Company',
      },
    ];
    this.steps = [
      { titleAr: 'تحديد الواسطة البحرية', titleEn: 'Select Maritime Medium' },
      { titleAr: 'بيانات الطلب', titleEn: 'Request Info' },
      { titleAr: 'المرفقات', titleEn: 'Attachment' },
      { titleAr: 'معاينة الطلب', titleEn: 'Preview' },
    ];
    this.requiredDocuments = [
      {
        titleAr: ' صورة جواز السفر',
        titleEn: 'Passport copy',
      },
      {
        titleAr: 'صورة شخصية',
        titleEn: 'Personal Image',
      },
    ];
    this.businessRoles = [
      {
        titleAr: 'إذا كان مقدم الطلب منشأة أو فرد أجنبي، يجب أن يكون لديه وكيل ملاح سياحي مفوض.',
        titleEn: 'If the applicant is a foreign entity or individual, a delegated tourism navigation agent is required.',
      },
      {
        titleAr: 'إذا كان مقدم الطلب وكيل ملاح سياحي، يجب أن يكون لديه تفويض ساري على اليخت.',
        titleEn: 'If the applicant is a tourism navigation agent, they must have an active delegation for the yacht.',
      },
      {
        titleAr: 'يجب أن يكون لليخت ترخيص ملاحي ساري.',
        titleEn: 'The yacht must have a valid navigation license.',
      },
      {
        titleAr: 'يجب أن يكون لليخت ترخيص فني ساري.',
        titleEn: 'The yacht must have a valid technical license.',
      },
    ];
  }
  private buildForm() {
    this.editForm = this.fb.group({
      visaType: ['', Validators.required],
      yachtId: ['', Validators.required],
      userId: ['', Validators.required],
      applicants: this.fb.array([]),
    });
  }
  private initForCreateOrUpdate() {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.editMode = true;
      // this.visaRequestService.getRequestById(+id).subscribe(data => {
      //   this.form.patchValue(data);
      //   data.applicants.forEach(app => this.addApplicant(app));
      // });
    } else {
      this.addApplicant(); // start with 1 applicant
    }
  }
  fetchServiceType() {
    this.serviceTypeService.findByCode(this.SERVICE_TYPE_CODE).subscribe({
      next: response => {
        this.selectedServiceType = response.body;
      },
      error() {
        // If there's an error loading the service type, log it
        console.error('Error loading service type with code TVR');
      },
    });
  }
  // loadCompanyData() {
  //   if (this.userServiceProvider) {
  //     this.companyService
  //       .getRegisteredCompany()
  //       .pipe(map((res: HttpResponse<ICompany>) => res.body ?? []))
  //       .subscribe((companyFullInfo: any) => {
  //         this.registeredCompany = companyFullInfo.company;
  //         this.companyService
  //           .getRelatedCompany(this.registeredCompany!.crNumber)
  //           .pipe(map((res: HttpResponse<ICompany[]>) => res.body ?? []))
  //           .subscribe((companies: ICompany[]) => {
  //             this.relatedCompanies = companies;
  //           });
  //       });
  //   }
  // }
  isArabic() {
    if (this.translateService && this.translateService!.currentLang) {
      let currentLanguage = this.translateService!.currentLang;
      currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
      return currentLanguage.startsWith('ar', 0);
    }
    return 'ar';
  }
  get applicants(): FormArray {
    return this.editForm.get('applicants') as FormArray;
  }
  addApplicant(app: any = {}) {
    this.applicants.push(
      this.fb.group({
        fullName: [app.fullName || '', Validators.required],
        passportNumber: [app.passportNumber || '', Validators.required],
        nationality: [app.nationality || '', Validators.required],
        birthDate: [app.birthDate || '', Validators.required],
        gender: [app.gender || '', Validators.required],
        identityType: [app.identityType || '', Validators.required],
        role: [app.role || '', Validators.required],
        jobTitle: [app.jobTitle || '', Validators.required],
        maritalStatus: [app.maritalStatus || '', Validators.required],
        religion: [app.religion || '', Validators.required],
        email: [app.email || '', Validators.required],
        phoneNumber: [app.phoneNumber || '', Validators.required],
        address: [app.address || '', Validators.required],
        passportIssueDate: [app.passportIssueDate || '', Validators.required],
        passportExpiryDate: [app.passportExpiryDate || '', Validators.required],
        passportCountry: [app.passportCountry || '', Validators.required],
        birthCountry: [app.birthCountry || '', Validators.required],
        residenceCountry: [app.residenceCountry || '', Validators.required],
        expectedEntryDate: [app.expectedEntryDate || '', Validators.required],
        numberOfEntries: [app.numberOfEntries || '', Validators.required],
        visaDurationDays: [app.visaDurationDays || '', Validators.required],
        wantsUmrah: [app.wantsUmrah || false],
        hasOtherNationality: [app.hasOtherNationality || false],
        otherNationality: [app.otherNationality || ''],
        hasDisability: [app.hasDisability || false],
        tookVaccines: [app.tookVaccines || false],
      }),
    );
  }
  nextStep() {
    if (this.step < 5) this.step++;
  }
  prevStep() {
    if (this.step > 0) this.step--;
  }
  submit() {
    if (this.editForm.invalid) return;
    const data = this.editForm.value;
    // if (this.editMode) {
    //   this.visaRequestService.updateRequest(data).subscribe();
    // } else {
    //   this.visaRequestService.createRequest(data).subscribe();
    // }
  }

  startService() {
    this.serviceDefinitionPage = false;
    this.step = 1;
    this.wizardDisplay = true;
    return;
  }
  loadAllMarineMediums(): void {
    this.marineMediumService.listAvailableForTourismPermit().subscribe({
      next: response => {
        this.marineMediums = response.data as MarineMedium[];
      },
      error: () => {},
    });
  }
}
