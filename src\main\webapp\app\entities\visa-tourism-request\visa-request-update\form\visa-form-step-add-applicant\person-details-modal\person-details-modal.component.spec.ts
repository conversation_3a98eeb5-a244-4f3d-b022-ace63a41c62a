import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Form<PERSON>uilder, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PersonDetailsModalComponent, CustomValidators } from './person-details-modal.component';

describe('PersonDetailsModalComponent', () => {
  let component: PersonDetailsModalComponent;
  let fixture: ComponentFixture<PersonDetailsModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PersonDetailsModalComponent, ReactiveFormsModule, TranslateModule.forRoot()],
      providers: [FormBuilder, TranslateService],
    }).compileComponents();

    fixture = TestBed.createComponent(PersonDetailsModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('CustomValidators', () => {
    describe('arabicOnly', () => {
      it('should return null for valid Arabic text', () => {
        const control = { value: 'محمد أحمد' };
        const result = CustomValidators.arabicOnly(control as any);
        expect(result).toBeNull();
      });

      it('should return error for English text', () => {
        const control = { value: 'Mohammed Ahmed' };
        const result = CustomValidators.arabicOnly(control as any);
        expect(result).toEqual({ arabicOnly: true });
      });

      it('should return error for mixed text', () => {
        const control = { value: 'محمد Ahmed' };
        const result = CustomValidators.arabicOnly(control as any);
        expect(result).toEqual({ arabicOnly: true });
      });

      it('should return null for empty value', () => {
        const control = { value: '' };
        const result = CustomValidators.arabicOnly(control as any);
        expect(result).toBeNull();
      });
    });

    describe('englishOnly', () => {
      it('should return null for valid English text', () => {
        const control = { value: 'Mohammed Ahmed' };
        const result = CustomValidators.englishOnly(control as any);
        expect(result).toBeNull();
      });

      it('should return error for Arabic text', () => {
        const control = { value: 'محمد أحمد' };
        const result = CustomValidators.englishOnly(control as any);
        expect(result).toEqual({ englishOnly: true });
      });

      it('should return error for mixed text', () => {
        const control = { value: 'Mohammed محمد' };
        const result = CustomValidators.englishOnly(control as any);
        expect(result).toEqual({ englishOnly: true });
      });

      it('should return null for empty value', () => {
        const control = { value: '' };
        const result = CustomValidators.englishOnly(control as any);
        expect(result).toBeNull();
      });
    });

    describe('arabicOrEnglish', () => {
      it('should return null for valid Arabic text', () => {
        const control = { value: 'محمد أحمد' };
        const result = CustomValidators.arabicOrEnglish(control as any);
        expect(result).toBeNull();
      });

      it('should return null for valid English text', () => {
        const control = { value: 'Mohammed Ahmed' };
        const result = CustomValidators.arabicOrEnglish(control as any);
        expect(result).toBeNull();
      });

      it('should return error for numbers', () => {
        const control = { value: '123456' };
        const result = CustomValidators.arabicOrEnglish(control as any);
        expect(result).toEqual({ arabicOrEnglish: true });
      });

      it('should return error for special characters', () => {
        const control = { value: 'Mohammed@Ahmed' };
        const result = CustomValidators.arabicOrEnglish(control as any);
        expect(result).toEqual({ arabicOrEnglish: true });
      });

      it('should return null for empty value', () => {
        const control = { value: '' };
        const result = CustomValidators.arabicOrEnglish(control as any);
        expect(result).toBeNull();
      });
    });
  });

  describe('Form Validation', () => {
    it('should apply Arabic validation to Arabic name fields', () => {
      const firstNameArControl = component.personForm.get('firstNameAr');

      // Test valid Arabic input
      firstNameArControl?.setValue('محمد');
      expect(firstNameArControl?.valid).toBeTruthy();

      // Test invalid English input
      firstNameArControl?.setValue('Mohammed');
      expect(firstNameArControl?.valid).toBeFalsy();
      expect(firstNameArControl?.errors?.['arabicOnly']).toBeTruthy();
    });

    it('should apply English validation to English name fields', () => {
      const firstNameEnControl = component.personForm.get('firstNameEn');

      // Test valid English input
      firstNameEnControl?.setValue('Mohammed');
      expect(firstNameEnControl?.valid).toBeTruthy();

      // Test invalid Arabic input
      firstNameEnControl?.setValue('محمد');
      expect(firstNameEnControl?.valid).toBeFalsy();
      expect(firstNameEnControl?.errors?.['englishOnly']).toBeTruthy();
    });

    it('should apply Arabic or English validation to mixed fields', () => {
      const birthPlaceControl = component.personForm.get('birthPlace');

      // Test valid Arabic input
      birthPlaceControl?.setValue('الرياض');
      expect(birthPlaceControl?.valid).toBeTruthy();

      // Test valid English input
      birthPlaceControl?.setValue('Riyadh');
      expect(birthPlaceControl?.valid).toBeTruthy();

      // Test invalid input with numbers
      birthPlaceControl?.setValue('Riyadh123');
      expect(birthPlaceControl?.valid).toBeFalsy();
      expect(birthPlaceControl?.errors?.['arabicOrEnglish']).toBeTruthy();
    });
  });
});
