# Arabic/English Field Validation Implementation

## Overview

This implementation adds custom validation to form fields that require specific language input (Arabic only, English only, or Arabic/English mixed).

## Changes Made

### 1. Custom Validators (TypeScript)

**File:** `src/main/webapp/app/entities/visa-tourism-request/visa-request-update/form/visa-form-step-add-applicant/person-details-modal/person-details-modal.component.ts`

Added three custom validators:

```typescript
export class CustomValidators {
  static arabicOnly(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const arabicPattern = /^[\u0621-\u064A\u0660-\u0669\s]+$/;
    return arabicPattern.test(control.value) ? null : { arabicOnly: true };
  }

  static englishOnly(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const englishPattern = /^[a-zA-Z\s]+$/;
    return englishPattern.test(control.value) ? null : { englishOnly: true };
  }

  static arabicOrEnglish(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const arabicOrEnglishPattern = /^[a-zA-Z\u0621-\u064A\u0660-\u0669\s]+$/;
    return arabicOrEnglishPattern.test(control.value) ? null : { arabicOrEnglish: true };
  }
}
```

### 2. Form Field Validation Rules

Applied validators to specific form fields:

#### Arabic-Only Fields:

- `firstNameAr`: `[Validators.required, CustomValidators.arabicOnly]`
- `fatherNameAr`: `[Validators.required, CustomValidators.arabicOnly]`
- `grandfatherNameAr`: `[Validators.required, CustomValidators.arabicOnly]`

#### English-Only Fields:

- `firstNameEn`: `[Validators.required, CustomValidators.englishOnly]`
- `fatherNameEn`: `[Validators.required, CustomValidators.englishOnly]`
- `grandfatherNameEn`: `[Validators.required, CustomValidators.englishOnly]`

#### Arabic or English Fields:

- `birthPlace`: `[Validators.required, CustomValidators.arabicOrEnglish]`
- `guardianName`: `[CustomValidators.arabicOrEnglish]`
- `guardianRelation`: `[CustomValidators.arabicOrEnglish]`
- `occupation`: `[Validators.required, CustomValidators.arabicOrEnglish]`
- `address`: `[Validators.required, CustomValidators.arabicOrEnglish]`

### 3. Template Validation Messages

**File:** `src/main/webapp/app/entities/visa-tourism-request/visa-request-update/form/visa-form-step-add-applicant/person-details-modal/person-details-modal.component.html`

Updated validation error display to show specific error messages:

```html
<div *ngIf="personForm.get('firstNameAr')?.invalid && personForm.get('firstNameAr')?.touched" class="text-danger small">
  <span *ngIf="personForm.get('firstNameAr')?.errors?.['required']" jhiTranslate="srsaApp.visaTourism.visaModal.firstNameArabicRequired"
    >First name in Arabic is required</span
  >
  <span *ngIf="personForm.get('firstNameAr')?.errors?.['arabicOnly']" jhiTranslate="srsaApp.visaTourism.visaModal.arabicOnlyError"
    >This field should contain only Arabic characters</span
  >
</div>
```

### 4. Translation Keys

**Files:**

- `src/main/webapp/i18n/en/visaTourism.json`
- `src/main/webapp/i18n/ar-ly/visaTourism.json`

Added new translation keys:

**English:**

```json
"arabicOnlyError": "This field should contain only Arabic characters",
"englishOnlyError": "This field should contain only English characters",
"arabicOrEnglishError": "This field should contain only Arabic or English characters"
```

**Arabic:**

```json
"arabicOnlyError": "هذا الحقل يجب أن يحتوي على أحرف عربية فقط",
"englishOnlyError": "هذا الحقل يجب أن يحتوي على أحرف إنجليزية فقط",
"arabicOrEnglishError": "هذا الحقل يجب أن يحتوي على أحرف عربية أو إنجليزية فقط"
```

### 5. Unit Tests

**File:** `src/main/webapp/app/entities/visa-tourism-request/visa-request-update/form/visa-form-step-add-applicant/person-details-modal/person-details-modal.component.spec.ts`

Created comprehensive unit tests to verify:

- Arabic-only validation works correctly
- English-only validation works correctly
- Arabic or English validation works correctly
- Form field validation integration

## Validation Patterns

### Arabic Pattern

- **Regex:** `/^[\u0621-\u064A\u0660-\u0669\s]+$/`
- **Covers:** Arabic letters (U+0621 to U+064A) and Arabic digits (U+0660 to U+0669)
- **Allows:** Spaces for multi-word names

### English Pattern

- **Regex:** `/^[a-zA-Z\s]+$/`
- **Covers:** English letters (both uppercase and lowercase)
- **Allows:** Spaces for multi-word names

### Arabic or English Pattern

- **Regex:** `/^[a-zA-Z\u0621-\u064A\u0660-\u0669\s]+$/`
- **Covers:** Both Arabic and English characters
- **Allows:** Spaces for multi-word entries

## Usage Examples

### Valid Inputs:

- **Arabic Only:** "محمد أحمد", "فاطمة الزهراء"
- **English Only:** "Mohammed Ahmed", "John Smith"
- **Arabic or English:** "محمد", "Ahmed", "الرياض", "Riyadh"

### Invalid Inputs:

- **Arabic Only:** "Mohammed", "محمد123", "Ahmed@gmail"
- **English Only:** "محمد", "Ahmed123", "John@doe"
- **Arabic or English:** "Name123", "محمد@أحمد", "City#1"

## Benefits

1. **Data Quality:** Ensures consistent data entry in the correct language
2. **User Experience:** Provides immediate feedback to users about input requirements
3. **Internationalization:** Supports both Arabic and English input validation
4. **Maintainability:** Reusable validators that can be applied to other forms
5. **Accessibility:** Clear error messages in both languages

## Future Enhancements

1. **Additional Language Support:** Extend patterns for other languages if needed
2. **Custom Error Messages:** Field-specific error messages for better UX
3. **Real-time Validation:** Add input event listeners for immediate feedback
4. **Pattern Configuration:** Make validation patterns configurable via settings
