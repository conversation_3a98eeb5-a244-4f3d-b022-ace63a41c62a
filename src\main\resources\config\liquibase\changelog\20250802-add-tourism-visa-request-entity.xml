<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- changeSet for visa_request table -->
    <changeSet id="3-create-visa-request" author="osama">
        <createTable tableName="visa_request">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="request_number" type="VARCHAR(100)"/>
            <column name="request_status" type="VARCHAR(50)"/>
            <column name="visa_type" type="VARCHAR(100)"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="updated_at" type="TIMESTAMP"/>
            <column name="user_id" type="BIGINT"/>
            <column name="marine_medium_id" type="BIGINT"/>
            <column name="total_fee" type="DECIMAL(12,2)"/>
        </createTable>
        <addForeignKeyConstraint constraintName="visa-request-owner-fk" baseTableName="visa_request" baseColumnNames="user_id"
                                 referencedTableName="srsa_user" referencedColumnNames="id"/>
        <addForeignKeyConstraint constraintName="visa-request-marine-fk" baseTableName="visa_request" baseColumnNames="marine_medium_id"
                                 referencedTableName="marine_medium" referencedColumnNames="id"/>
    </changeSet>

    <!-- changeSet for visa_applicant table -->
    <changeSet id="4-create-visa-applicant" author="osama">
        <createTable tableName="visa_applicant">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="visa_request_id" type="BIGINT"/>
            <column name="full_name" type="VARCHAR(255)"/>
            <column name="passport_number" type="VARCHAR(100)"/>
            <column name="nationality" type="VARCHAR(100)"/>
            <column name="birth_date" type="DATE"/>
            <column name="gender" type="VARCHAR(10)"/>
            <column name="identity_type" type="VARCHAR(50)"/>
            <column name="role" type="VARCHAR(50)"/>
            <column name="job_title" type="VARCHAR(100)"/>
            <column name="marital_status" type="VARCHAR(50)"/>
            <column name="religion" type="VARCHAR(50)"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="phone_number" type="VARCHAR(50)"/>
            <column name="address" type="VARCHAR(255)"/>
            <column name="passport_issue_date" type="DATE"/>
            <column name="passport_expiry_date" type="DATE"/>
            <column name="passport_country" type="VARCHAR(100)"/>
            <column name="birth_country" type="VARCHAR(100)"/>
            <column name="residence_country" type="VARCHAR(100)"/>
            <column name="expected_entry_date" type="DATE"/>
            <column name="number_of_entries" type="VARCHAR(20)"/>
            <column name="visa_duration_days" type="INT"/>
            <column name="wants_umrah" type="BOOLEAN"/>
            <column name="has_other_nationality" type="BOOLEAN"/>
            <column name="other_nationality" type="VARCHAR(100)"/>
            <column name="has_disability" type="BOOLEAN"/>
            <column name="took_vaccines" type="BOOLEAN"/>
        </createTable>
        <addForeignKeyConstraint constraintName="visa-request-applicant-fk" baseTableName="visa_applicant" baseColumnNames="visa_request_id"
                                 referencedTableName="visa_request" referencedColumnNames="id"/>
    </changeSet>

    <!-- changeSet for visa_medical_history table -->
    <changeSet id="5-create-visa-medical-history" author="osama">
        <createTable tableName="visa_medical_history">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="visa_applicant_id" type="BIGINT"/>
            <column name="had_accident_causing_disability" type="BOOLEAN"/>
            <column name="has_current_hospital_admission" type="BOOLEAN"/>
            <column name="has_congenital_condition" type="BOOLEAN"/>
        </createTable>
        <addForeignKeyConstraint constraintName="visa_medical_history-fk" baseTableName="visa_medical_history" baseColumnNames="visa_applicant_id"
                                 referencedTableName="visa_applicant" referencedColumnNames="id"/>
    </changeSet>

    <!-- changeSet for visa_security_clearance table -->
    <changeSet id="6-create-visa-security-clearance" author="osama">
        <createTable tableName="visa_security_clearance">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="visa_applicant_id" type="BIGINT"/>
            <column name="interpol_arrest" type="BOOLEAN"/>
            <column name="deported" type="BOOLEAN"/>
            <column name="has_travel_restrictions" type="BOOLEAN"/>
            <column name="has_been_arrested_for_finance" type="BOOLEAN"/>
            <column name="sentenced_in_home_country" type="BOOLEAN"/>
            <column name="served_in_military" type="BOOLEAN"/>
            <column name="worked_in_political_or_media" type="BOOLEAN"/>
            <column name="linked_to_terrorist_org" type="BOOLEAN"/>
            <column name="clarification" type="VARCHAR(1000)"/>
        </createTable>
        <addForeignKeyConstraint constraintName="visa_security_clearance-fk" baseTableName="visa_security_clearance" baseColumnNames="visa_applicant_id"
                                 referencedTableName="visa_applicant" referencedColumnNames="id"/>
    </changeSet>

    <!-- changeSet for visa_applicant_previous_travel table -->
    <changeSet id="7-create-visa-applicant-previous-travel" author="osama">
        <createTable tableName="visa_applicant_previous_travel">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="visa_applicant_id" type="BIGINT"/>
            <column name="country" type="VARCHAR(100)"/>
            <column name="purpose" type="VARCHAR(255)"/>
            <column name="from_date" type="DATE"/>
            <column name="to_date" type="DATE"/>
        </createTable>
        <addForeignKeyConstraint constraintName="visa_applicant_previous_travel-fk" baseTableName="visa_applicant_previous_travel" baseColumnNames="visa_applicant_id"
                                 referencedTableName="visa_applicant" referencedColumnNames="id"/>
    </changeSet>
    <changeSet id="20250803-visa-request-attachments" author="osama">
        <createTable tableName="visa_tourism_request_attachment">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="jhi_desc" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="doc_name" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="doc_url" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="visa_request_id" type="bigint">
                <constraints nullable="true" />
            </column>
        </createTable>

        <addForeignKeyConstraint
            baseTableName="visa_tourism_request_attachment"
            baseColumnNames="visa_request_id"
            referencedTableName="visa_request"
            referencedColumnNames="id"
            constraintName="fk_attachment_visa_request"/>
    </changeSet>

</databaseChangeLog>
