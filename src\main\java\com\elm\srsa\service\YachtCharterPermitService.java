package com.elm.srsa.service;

import com.elm.srsa.config.ApplicationProperties;
import com.elm.srsa.domain.*;
import com.elm.srsa.domain.enumeration.VisitStatus;
import com.elm.srsa.domain.enumeration.YachtCharterPermitStatusType;
import com.elm.srsa.repository.*;
import com.elm.srsa.security.AuthoritiesConstants;
import com.elm.srsa.security.RsaUser;
import com.elm.srsa.security.SecurityUtils;
import com.elm.srsa.service.dto.*;
import com.elm.srsa.service.mapper.ServiceTypeMapper;
import com.elm.srsa.service.mapper.UserMapper;
import com.elm.srsa.service.mapper.YachtCharterPermitAttachmentMapper;
import com.elm.srsa.service.mapper.YachtCharterPermitMapper;
import com.elm.srsa.tga.rest.client.response.ShipResponse;
import com.elm.srsa.tga.rest.client.response.TgaBaseResponse;
import com.elm.srsa.util.*;
import com.elm.srsa.wathq.rest.client.response.newResponse.Activity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.WriterException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

/**
 * Service Implementation for managing {@link com.elm.srsa.domain.YachtCharterPermit}.
 */
@Service
@Transactional
public class YachtCharterPermitService {

    private static final Logger LOG = LoggerFactory.getLogger(YachtCharterPermitService.class);

    @PersistenceContext
    private EntityManager entityManager;

    private final YachtCharterPermitRepository yachtCharterPermitRepository;
    private final RandomUtil randomUtil;
    private final CompanyService companyService;
    private final YachtCharterPermitMapper yachtCharterPermitMapper;
    private final YachtCharterPermitAttachmentMapper yachtCharterPermitAttachmentMapper;
    private final ServiceTypeRepository serviceTypeRepository;
    private final YachtCharterPermitAttachmentRepository yachtCharterPermitAttachmentRepository;
    private final YachtCharterPermitAttachmentService yachtCharterPermitAttachmentService;
    private final YachtCharterPermitStatusRepository yachtCharterPermitStatusRepository;
    private final YachtCharterPermitHistoryRepository yachtCharterPermitHistoryRepository;
    private final YachtCharterPermitVisitRepository yachtCharterPermitVisitRepository;
    private final YachtCharterPermitVisitAttachmentRepository yachtCharterPermitVisitAttachmentRepository;
    private final CompanyActivityService companyActivityService;
    private final ApplicationProperties.HtmlToPdf htmlToPdf;
    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final FileDownloadUtil downloadUtil;
    private final PermitChangesRepository permitChangesRepository;
    private final AttachmentService attachmentService;
    private final AttachmentRepository attachmentRepository;
    private final ServiceTypeMapper serviceTypeMapper;
    private final WathqApiResponseService wathqApiResponseService;

    public YachtCharterPermitService(
        YachtCharterPermitRepository yachtCharterPermitRepository,
        RandomUtil randomUtil,
        CompanyService companyService,
        ApplicationProperties appProperties,
        YachtCharterPermitMapper yachtCharterPermitMapper,
        YachtCharterPermitAttachmentMapper yachtCharterPermitAttachmentMapper,
        ServiceTypeRepository serviceTypeRepository,
        YachtCharterPermitAttachmentRepository yachtCharterPermitAttachmentRepository,
        YachtCharterPermitAttachmentService yachtCharterPermitAttachmentService,
        YachtCharterPermitStatusRepository yachtCharterPermitStatusRepository,
        YachtCharterPermitHistoryRepository yachtCharterPermitHistoryRepository,
        YachtCharterPermitVisitRepository yachtCharterPermitVisitRepository,
        YachtCharterPermitVisitAttachmentRepository yachtCharterPermitVisitAttachmentRepository,
        CompanyActivityService companyActivityService,
        UserRepository userRepository,
        UserMapper userMapper,
        FileDownloadUtil downloadUtil,
        PermitChangesRepository permitChangesRepository,
        AttachmentService attachmentService,
        AttachmentRepository attachmentRepository,
        ServiceTypeMapper serviceTypeMapper,
        WathqApiResponseService wathqApiResponseService
    ) {
        this.yachtCharterPermitRepository = yachtCharterPermitRepository;
        this.randomUtil = randomUtil;
        this.companyService = companyService;
        this.yachtCharterPermitMapper = yachtCharterPermitMapper;
        this.yachtCharterPermitAttachmentMapper = yachtCharterPermitAttachmentMapper;
        this.serviceTypeRepository = serviceTypeRepository;
        this.yachtCharterPermitAttachmentRepository = yachtCharterPermitAttachmentRepository;
        this.yachtCharterPermitAttachmentService = yachtCharterPermitAttachmentService;
        this.yachtCharterPermitStatusRepository = yachtCharterPermitStatusRepository;
        this.yachtCharterPermitHistoryRepository = yachtCharterPermitHistoryRepository;
        this.yachtCharterPermitVisitRepository = yachtCharterPermitVisitRepository;
        this.yachtCharterPermitVisitAttachmentRepository = yachtCharterPermitVisitAttachmentRepository;
        this.companyActivityService = companyActivityService;
        this.htmlToPdf = appProperties.getHtmlToPdf();
        this.userRepository = userRepository;
        this.userMapper = userMapper;
        this.downloadUtil = downloadUtil;
        this.permitChangesRepository = permitChangesRepository;
        this.attachmentService = attachmentService;
        this.attachmentRepository = attachmentRepository;
        this.serviceTypeMapper = serviceTypeMapper;
        this.wathqApiResponseService = wathqApiResponseService;
    }

    /**
     * Save a yachtCharterPermit.
     *
     * @param yachtCharterPermitDTO the entity to save.
     * @param attachments
     * @return the persisted entity.
     */
    public YachtCharterPermitDTO save(YachtCharterPermitDTO yachtCharterPermitDTO, MultipartFile[] attachments) {
        LOG.debug("Request to save YachtCharterPermit : {}", yachtCharterPermitDTO);
        if (yachtCharterPermitDTO.getId() == null) {
            yachtCharterPermitDTO = create(yachtCharterPermitDTO, attachments);
        } else {
            yachtCharterPermitDTO = update(yachtCharterPermitDTO, attachments);
        }
        return yachtCharterPermitDTO;
    }

    public YachtCharterPermitDTO update(YachtCharterPermitDTO yachtCharterPermitDTO, MultipartFile[] mtaContractMultipartFile) {
        LOG.debug("Request to update yachtCharterPermitDTO : {}", yachtCharterPermitDTO);
        Optional<YachtCharterPermit> oldYAchtCharterPermitOptional = null;

        if (SecurityUtils.isServiceProvider()) {
            oldYAchtCharterPermitOptional =
                yachtCharterPermitRepository.findByIdAndCompany_Id(
                    yachtCharterPermitDTO.getId(),
                    companyService.getRegisteredCompany().getId()
                );
            if (oldYAchtCharterPermitOptional.isEmpty()) {
                throw new RuntimeException("Cannot update tech permit not belonging my company");
            }
        }

        YachtCharterPermit yachtCharterPermit = yachtCharterPermitMapper.toEntity(yachtCharterPermitDTO);

        if (oldYAchtCharterPermitOptional != null) {
            yachtCharterPermit.setAssignedEmployeeRole(oldYAchtCharterPermitOptional.get().getAssignedEmployeeRole());
            yachtCharterPermit.setRequestNumber(oldYAchtCharterPermitOptional.get().getRequestNumber());
            yachtCharterPermit.setUuid(oldYAchtCharterPermitOptional.get().getUuid());
            ServiceType serviceType = serviceTypeRepository
                .findByCode("TC")
                .orElseThrow(() -> new RuntimeException("Service type with code TC not found"));
            yachtCharterPermit.setPermitNumber(oldYAchtCharterPermitOptional.get().getPermitNumber());
            yachtCharterPermit.setServiceType(serviceType);
            yachtCharterPermit.setSubmitDate(Instant.now());
            yachtCharterPermit.setMarineMedium(oldYAchtCharterPermitOptional.get().getMarineMedium());

            BigDecimal durationYears = BigDecimal.valueOf(1);
            yachtCharterPermit.setFees(yachtCharterPermit.getServiceType().getFees().multiply(durationYears));
            yachtCharterPermit.setPaymentPeriodDays(yachtCharterPermit.getServiceType().getPaymentPeriodDays());
            yachtCharterPermit.setPaymentPeriodDate(
                Instant.now().plus(yachtCharterPermit.getServiceType().getPaymentPeriodDays(), ChronoUnit.DAYS)
            );
            Company company = companyService.getRegisteredCompany();
            yachtCharterPermit.setCompany(company);
            if (yachtCharterPermitDTO.getSecondaryCompany() != null && yachtCharterPermitDTO.getBranchDto() != null) {
                yachtCharterPermitDTO.getSecondaryCompany().setCrIssueDate(company.getCrIssueDate());
                yachtCharterPermitDTO.getSecondaryCompany().setCrExpiryDate(company.getCrExpiryDate());
                yachtCharterPermitDTO.getSecondaryCompany().setBusinessType(company.getBusinessType());
                yachtCharterPermitDTO.getSecondaryCompany().setCrEntityNumber(yachtCharterPermitDTO.getBranchDto().getCrNationalNumber());
                yachtCharterPermitDTO
                    .getSecondaryCompany()
                    .setCrMainEntityNumber(yachtCharterPermitDTO.getBranchDto().getMainCrNationalNumber());
                yachtCharterPermitDTO.getSecondaryCompany().setCrName(yachtCharterPermitDTO.getBranchDto().getNameAr());
                yachtCharterPermitDTO.getSecondaryCompany().setParentCrNumber(company.getCrNumber());
                yachtCharterPermitDTO.getSecondaryCompany().setCrNumber(yachtCharterPermitDTO.getBranchDto().getCrNumber());
                Company secCompany = companyService.saveCompany(yachtCharterPermitDTO.getSecondaryCompany());
                yachtCharterPermit.setSecondaryCompany(secCompany);
            }
            setTotalPriceWithAndWithoutVat(yachtCharterPermit, durationYears);
        }

        if (yachtCharterPermitDTO.getAttachments() == null) {
            yachtCharterPermitDTO.setAttachments(new ArrayList<>());
        }
        List<YachtCharterPermitAttachmentDTO> attachmentDTOS = new ArrayList<>(yachtCharterPermitDTO.getAttachments());
        if (mtaContractMultipartFile != null) {
            for (MultipartFile multipartFile : mtaContractMultipartFile) {
                if (!Objects.equals(multipartFile.getOriginalFilename(), "no-file-h")) {
                    try {
                        // This will filter files that are already saved
                        // TODO pass allowed max size for files here
                        if (multipartFile.getBytes().length >= 1) {
                            yachtCharterPermitAttachmentService.saveFileAndReturnPath(
                                Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                                multipartFile.getOriginalFilename(),
                                yachtCharterPermit,
                                attachmentDTOS,
                                yachtCharterPermitRepository.getReferenceById(yachtCharterPermitDTO.getId()).getRequestStatus() ==
                                YachtCharterPermitStatusType.DRAFT,
                                null
                            );
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("fail to save attachment in technical permit  update : " + e.getMessage());
                    }
                }
            }
        }

        attachmentDTOS.forEach(attachment -> {
            if (attachment.isRemoved()) {
                if (
                    yachtCharterPermitRepository.getReferenceById(yachtCharterPermitDTO.getId()).getRequestStatus() ==
                    YachtCharterPermitStatusType.DRAFT
                ) {
                    attachmentService.deleteFile(attachment.getDocUrl());
                }
                attachmentRepository.deleteById(attachment.getId());
            } else if (attachment.isRemoved() || attachment.getId() == null) {
                permitChangesRepository.save(new PermitChanges().getChangeObj(yachtCharterPermitDTO.getRequestNumber(), "Attachments"));
            }
        });

        // Filter replaced file from the List
        attachmentDTOS = attachmentDTOS.stream().filter(attachment -> !attachment.isRemoved()).collect(Collectors.toList());

        List<YachtCharterPermitAttachment> attachments = yachtCharterPermitAttachmentMapper.toEntity(attachmentDTOS);
        yachtCharterPermit.setAttachments(new HashSet<>(attachments));

        final YachtCharterPermit savedTechnicalPermit = yachtCharterPermitRepository.save(yachtCharterPermit);

        attachments.forEach(attachment -> attachment.setYachtCharterPermit(savedTechnicalPermit));
        yachtCharterPermitAttachmentRepository.saveAll(attachments);
        saveYachtCharterPermitStatus(savedTechnicalPermit);
        saveYachtCharterPermitHistory(savedTechnicalPermit);
        return yachtCharterPermitMapper.toDto(savedTechnicalPermit);
    }

    public void setTotalPriceWithAndWithoutVat(YachtCharterPermit yachtCharterPermit, BigDecimal durationYears) {
        if (Objects.nonNull(yachtCharterPermit.getServiceType())) {
            yachtCharterPermit.setTotalPriceWithVat(yachtCharterPermit.getServiceType().getFees().multiply(durationYears));
            yachtCharterPermit.setTotalPriceWithoutVat(yachtCharterPermit.getServiceType().getFees().multiply(durationYears));
        }
    }

    public YachtCharterPermitDTO create(YachtCharterPermitDTO yachtCharterPermitDTO, MultipartFile[] files) {
        LOG.debug("Request to create YachtCharterPermit : {}", yachtCharterPermitDTO);
        ServiceType serviceType = serviceTypeRepository
            .findByCode("TC")
            .orElseThrow(() -> new RuntimeException("Service type with code TC not found"));
        YachtCharterPermit yachtCharterPermit = constructEntity(yachtCharterPermitDTO, serviceType);
        yachtCharterPermit.setUuid(UUID.randomUUID());
        yachtCharterPermit.setServiceType(serviceType);
        yachtCharterPermit.setSubmitDate(Instant.now());
        BigDecimal durationYears = BigDecimal.valueOf(1);
        yachtCharterPermit.setFees(yachtCharterPermit.getServiceType().getFees().multiply(durationYears));
        yachtCharterPermit.setPaymentPeriodDays(yachtCharterPermit.getServiceType().getPaymentPeriodDays());
        yachtCharterPermit.setPaymentPeriodDate(
            Instant.now().plus(yachtCharterPermit.getServiceType().getPaymentPeriodDays(), ChronoUnit.DAYS)
        );
        Company company = companyService.getRegisteredCompany();
        yachtCharterPermit.setCompany(company);
        if (yachtCharterPermitDTO.getSecondaryCompany() != null && yachtCharterPermitDTO.getBranchDto() != null) {
            yachtCharterPermitDTO.getSecondaryCompany().setCrIssueDate(company.getCrIssueDate());
            yachtCharterPermitDTO.getSecondaryCompany().setCrExpiryDate(company.getCrExpiryDate());
            yachtCharterPermitDTO.getSecondaryCompany().setBusinessType(company.getBusinessType());
            yachtCharterPermitDTO.getSecondaryCompany().setCrEntityNumber(yachtCharterPermitDTO.getBranchDto().getCrNationalNumber());
            yachtCharterPermitDTO
                .getSecondaryCompany()
                .setCrMainEntityNumber(yachtCharterPermitDTO.getBranchDto().getMainCrNationalNumber());
            yachtCharterPermitDTO.getSecondaryCompany().setCrName(yachtCharterPermitDTO.getBranchDto().getNameAr());
            yachtCharterPermitDTO.getSecondaryCompany().setParentCrNumber(company.getCrNumber());
            yachtCharterPermitDTO.getSecondaryCompany().setCrNumber(yachtCharterPermitDTO.getBranchDto().getCrNumber());
            Company secCompany = companyService.saveCompany(yachtCharterPermitDTO.getSecondaryCompany());
            yachtCharterPermit.setSecondaryCompany(secCompany);
        }
        setTotalPriceWithAndWithoutVat(yachtCharterPermit, durationYears);
        List<YachtCharterPermitAttachmentDTO> attachmentDTOS = new ArrayList<>();
        if (files != null) {
            for (MultipartFile multipartFile : files) {
                try {
                    // TODO pass allowed max size for files here
                    if (multipartFile.getBytes().length >= 1) {
                        yachtCharterPermitAttachmentService.saveFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            yachtCharterPermit,
                            attachmentDTOS,
                            false,
                            null
                        );
                    }
                } catch (IOException e) {
                    throw new RuntimeException("fail to save attachment in tech permit saving : " + e.getMessage());
                }
            }
        }
        List<YachtCharterPermitAttachment> attachments = yachtCharterPermitAttachmentMapper.toEntity(attachmentDTOS);
        //        technicalPermit.setAttachments(new HashSet<>(attachments));
        final YachtCharterPermit savedYachtCharterPermit = yachtCharterPermitRepository.save(yachtCharterPermit);

        //        attachmentDTOS.forEach(attachmentDTO -> attachmentDTO.setTechnicalPermit(permitRequestMapper.toDto(savedTechnicalPermit)));
        attachments.forEach(attachment -> attachment.setYachtCharterPermit(savedYachtCharterPermit));
        yachtCharterPermitAttachmentRepository.saveAll(attachments);
        saveYachtCharterPermitStatus(savedYachtCharterPermit);
        saveYachtCharterPermitHistory(savedYachtCharterPermit);
        return yachtCharterPermitMapper.toDto(savedYachtCharterPermit);
    }

    private void saveYachtCharterPermitHistory(YachtCharterPermit savedYachtCharterPermit) {
        YachtCharterPermitHistory history = new YachtCharterPermitHistory();
        history.setId(1L);
        history.setMarineOwnerNameAr(savedYachtCharterPermit.getMarineOwnerNameAr());
        history.setMarineOwnerNameEn(savedYachtCharterPermit.getMarineOwnerNameEn());
        history.setMarineOwnerIdNumber(savedYachtCharterPermit.getMarineOwnerIdNumber());
        history.setMarineOwnerPassportNumber(savedYachtCharterPermit.getMarineOwnerPassportNumber());
        history.setDelegatorNameAr(savedYachtCharterPermit.getDelegatorNameAr());
        history.setDelegatorNameEn(savedYachtCharterPermit.getDelegatorNameEn());
        history.setDelegatorMobileNumber(savedYachtCharterPermit.getDelegatorMobileNumber());
        history.setDelegatorEmailNumber(savedYachtCharterPermit.getDelegatorEmailNumber());
        history.setPermitNumber(savedYachtCharterPermit.getPermitNumber());
        history.setRequestStatus(savedYachtCharterPermit.getRequestStatus());

        history.setCaptainMobileNumber(savedYachtCharterPermit.getCaptainMobileNumber());

        history.setArrivalDate(savedYachtCharterPermit.getArrivalDate());
        history.setDepartureDate(savedYachtCharterPermit.getDepartureDate());
        history.setStartDate(savedYachtCharterPermit.getStartDate());
        history.setEndDate(savedYachtCharterPermit.getEndDate());
        history.setDisclosureChecked(savedYachtCharterPermit.getDisclosureChecked());

        history.setMarineDelegatorUser(savedYachtCharterPermit.getMarineDelegatorUser());
        history.setMarineDelegatorCompany(savedYachtCharterPermit.getMarineDelegatorCompany());
        history.setArrivalPort(savedYachtCharterPermit.getArrivalPort());
        history.setDeparturePort(savedYachtCharterPermit.getDeparturePort());
        history.setMarineMedium(savedYachtCharterPermit.getMarineMedium());
        yachtCharterPermitHistoryRepository.save(history);
    }

    //    private void saveYachtCharterPermitStatus(YachtCharterPermit savedYachtCharterPermit) {
    //        YachtCharterPermitStatus yachtCharterPermitStatus = new YachtCharterPermitStatus();
    //        yachtCharterPermitStatus.setYachtCharterPermit(savedYachtCharterPermit);
    //        yachtCharterPermitStatus.setRequestStatus(savedYachtCharterPermit.getRequestStatus());
    //      //  yachtCharterPermitStatus.setRequestNote(savedYachtCharterPermit.getRequestNote());
    //        yachtCharterPermitStatus.setRequestNumber(savedYachtCharterPermit.getRequestNumber());
    //      //  yachtCharterPermitStatus.setAssignedEmployeeName(savedYachtCharterPermit.());
    //      //  yachtCharterPermitStatus.setAssignedEmployee(savedYachtCharterPermit.getAssignedEmployee());
    //        yachtCharterPermitStatusRepository.save(yachtCharterPermitStatus);
    //    }

    /**
     * Update a yachtCharterPermit.
     *
     * @param yachtCharterPermitDTO the entity to save.
     * @return the persisted entity.
     */
    public YachtCharterPermitDTO update(YachtCharterPermitDTO yachtCharterPermitDTO) {
        LOG.debug("Request to update YachtCharterPermit : {}", yachtCharterPermitDTO);
        YachtCharterPermit yachtCharterPermit = yachtCharterPermitMapper.toEntity(yachtCharterPermitDTO);
        yachtCharterPermit = yachtCharterPermitRepository.save(yachtCharterPermit);
        return yachtCharterPermitMapper.toDto(yachtCharterPermit);
    }

    private YachtCharterPermit constructEntity(YachtCharterPermitDTO yachtCharterPermitDTO, ServiceType serviceType) {
        YachtCharterPermit yachtCharterPermit = yachtCharterPermitMapper.toEntity(yachtCharterPermitDTO);

        Long lastSeqNum = randomUtil.getLastSeqNum();
        yachtCharterPermit.setPermitNumber(
            randomUtil.generateRequestNumber(serviceTypeRepository.findById(serviceType.getId()).get(), lastSeqNum)
        );
        yachtCharterPermit.setRequestNumber(randomUtil.generateRequestNumber(lastSeqNum));

        if (yachtCharterPermitDTO.getMarineMedium() != null) {
            MarineMedium MarineMedium = entityManager.getReference(MarineMedium.class, yachtCharterPermitDTO.getMarineMedium().getId());
            yachtCharterPermit.setMarineMedium(MarineMedium);
        }
        if (yachtCharterPermitDTO.getMarineDelegatorUser() != null) {
            User marineDelegatorUser = entityManager.getReference(User.class, yachtCharterPermitDTO.getMarineDelegatorUser().getId());
            yachtCharterPermit.setMarineDelegatorUser(marineDelegatorUser);
        }
        if (yachtCharterPermitDTO.getMarineDelegatorCompany() != null) {
            Company company = entityManager.getReference(Company.class, yachtCharterPermitDTO.getMarineDelegatorCompany().getId());
            yachtCharterPermit.setMarineDelegatorCompany(company);
        }
        if (yachtCharterPermitDTO.getLicenseProvider() != null) {
            LicenseProvider licenseProvider = entityManager.getReference(
                LicenseProvider.class,
                yachtCharterPermitDTO.getLicenseProvider().getId()
            );
            yachtCharterPermit.setLicenseProvider(licenseProvider);
        }

        return yachtCharterPermit;
    }

    /**
     * Partially update a yachtCharterPermit.
     *
     * @param yachtCharterPermitDTO the entity to update partially.
     * @return the persisted entity.
     */
    public Optional<YachtCharterPermitDTO> partialUpdate(YachtCharterPermitDTO yachtCharterPermitDTO) {
        LOG.debug("Request to partially update YachtCharterPermit : {}", yachtCharterPermitDTO);

        return yachtCharterPermitRepository
            .findById(yachtCharterPermitDTO.getId())
            .map(existingYachtCharterPermit -> {
                yachtCharterPermitMapper.partialUpdate(existingYachtCharterPermit, yachtCharterPermitDTO);

                return existingYachtCharterPermit;
            })
            .map(yachtCharterPermitRepository::save)
            .map(yachtCharterPermitMapper::toDto);
    }

    /**
     * Get one yachtCharterPermit by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<YachtCharterPermit> findOne(Long id) {
        LOG.debug("Request to get YachtCharterPermit : {}", id);
        return yachtCharterPermitRepository.findById(id);
    }

    /**
     * Delete the yachtCharterPermit by id.
     *
     * @param id the id of the entity.
     */
    public void delete(Long id) {
        LOG.debug("Request to delete YachtCharterPermit : {}", id);
        yachtCharterPermitRepository.deleteById(id);
    }

    public byte[] exportYachtCharterPermitById(Long yachtCharterPermitId) throws IOException, WriterException {
        Optional<YachtCharterPermit> optionalYachtCharterPermit = yachtCharterPermitRepository.findById(yachtCharterPermitId);
        if (optionalYachtCharterPermit.isEmpty()) {
            return null;
        }
        return exportYachtCharterPermit(optionalYachtCharterPermit.get());
    }

    private byte[] exportYachtCharterPermit(YachtCharterPermit permitRequest) throws IOException, WriterException {
        String templateFileName = "Large Yacht Chartering License";
        String templatePath = "templates/reports";
        String filePath = PdfGeneratorUtil.getFolderPath(templatePath);
        String content = "Constants.EMPTY";
        content = PdfGeneratorUtil.loadTemplate(filePath, content, templateFileName + ".html");
        content = fillContractInfo(permitRequest, content, templateFileName);
        return PdfGeneratorUtil.convertToByte(
            htmlToPdf.getPath(),
            filePath,
            templatePath,
            content,
            false,
            "landscape",
            htmlToPdf.getPdfParams()
        );
    }

    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";
    private static final String GMT_PLUS_3_TIMEZONE = "GMT+3";
    private static final int QR_CODE_SIZE = 150;

    // Template variable constants
    private static final String LICENSE_NUMBER_VAR = "${licenseNumber}";
    private static final String START_DATE_VAR = "${startDate}";
    private static final String END_DATE_VAR = "${endDate}";
    private static final String REGISTRATION_NUMBER_VAR = "${registrationNumber}";
    private static final String FLAG_COUNTRY_VAR = "${flagCountry}";
    private static final String INTERNATIONAL_ORG_NUMBER_VAR = "${internationalOrgNumber}";
    private static final String CALL_SIGN_NUMBER_VAR = "${callSignNumber}";
    private static final String YACHT_COLOR_VAR = "${yachtColor}";
    private static final String DIMENSIONS_VAR = "${dimensions}";
    private static final String YACHT_CHARTER_COMPANY_NAME_VAR = "${yachtCharterCompanyName}";
    private static final String TENANT_NUMBER_VAR = "${tenantNumber}";
    private static final String CAPTAIN_NAME_VAR = "${captainName}";
    private static final String NATIONALITY_VAR = "${nationality}";
    private static final String ANCHORAGE_AREA_VAR = "${anchorageArea}";
    private static final String DELEGATOR_MOBILE_NUMBER_VAR = "${delegatorMobileNumber}";
    private static final String SHIP_TYPE_VAR = "${shipType}";
    private static final String SHIP_NAME_VAR = "${shipName}";
    private static final String REGISTER_PORT_VAR = "${registerPort}";
    private static final String QR_CODE_VAR = "${qrcode}";

    private String fillContractInfo(YachtCharterPermit permitRequest, String content, String templateFileName)
        throws IOException, WriterException {
        // Validate input parameters
        if (permitRequest == null) {
            throw new IllegalArgumentException("Permit request cannot be null");
        }
        if (StringUtils.isBlank(content)) {
            throw new IllegalArgumentException("Template content cannot be null or empty");
        }

        SimpleDateFormat dateFormat = createDateFormatter();

        // Extract ship information from TGA response
        ShipDataExtractor shipDataExtractor = extractShipData(permitRequest);

        // Build template variables map
        Map<String, String> templateVariables = buildTemplateVariables(permitRequest, shipDataExtractor, dateFormat);

        // Generate and add QR code
        addQRCodeToTemplate(templateVariables, permitRequest.getUuid().toString());

        return PdfGeneratorUtil.replaceInString(content, templateVariables);
    }

    // Helper class to hold extracted ship data
    private static class ShipDataExtractor {

        private final TgaBaseResponse tgaBaseResponse;
        private final ShipResponse.ShipDetails shipResponse;
        private final boolean isDataAvailable;

        public ShipDataExtractor(TgaBaseResponse tgaBaseResponse, ShipResponse.ShipDetails shipResponse) {
            this.tgaBaseResponse = tgaBaseResponse;
            this.shipResponse = shipResponse;
            this.isDataAvailable = shipResponse != null && tgaBaseResponse.getData() != null;
        }

        public String getCallSignNumber() {
            return isDataAvailable ? shipResponse.getCallSignNumber() : null;
        }

        public String getRegistrationPortId() {
            return isDataAvailable ? shipResponse.getRegistrationPortId() : null;
        }

        public String getDimensions() {
            if (!isDataAvailable) {
                return null;
            }
            return buildDimensionsString(shipResponse);
        }

        private String buildDimensionsString(ShipResponse.ShipDetails shipData) {
            List<String> dimensionParts = new ArrayList<>();

            Optional.ofNullable(shipData.getHeight()).ifPresent(height -> dimensionParts.add("H: " + height + "m"));

            Optional.ofNullable(shipData.getWidth()).ifPresent(width -> dimensionParts.add("W: " + width + "m"));

            Optional.ofNullable(shipData.getDepth()).ifPresent(depth -> dimensionParts.add("D: " + depth + "m"));

            return dimensionParts.isEmpty() ? null : String.join(" / ", dimensionParts);
        }
    }

    private ObjectMapper createConfiguredObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // Configure to ignore unknown properties to handle JSON structure mismatches
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);
        return objectMapper;
    }

    private SimpleDateFormat createDateFormatter() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        dateFormat.setTimeZone(TimeZone.getTimeZone(GMT_PLUS_3_TIMEZONE));
        return dateFormat;
    }

    private ShipDataExtractor extractShipData(YachtCharterPermit permitRequest) {
        TgaBaseResponse tgaBaseResponse = null;
        ShipResponse.ShipDetails shipResponse = null;

        try {
            // Check if TGA response is available for Saudi marine mediums
            if (isTgaResponseAvailable(permitRequest)) {
                ObjectMapper objectMapper = createConfiguredObjectMapper();

                // Parse TGA response
                String tgaResponseString = permitRequest.getMarineMedium().getTgaResponse();
                tgaBaseResponse = objectMapper.readValue(tgaResponseString, TgaBaseResponse.class);

                // Convert the data object back to JSON string, then parse as ShipResponse
                Object data = tgaBaseResponse.getData();
                if (data != null) {
                    String dataAsJson = objectMapper.writeValueAsString(data);
                    shipResponse = objectMapper.readValue(dataAsJson, ShipResponse.ShipDetails.class);
                }
            }
        } catch (JsonProcessingException e) {
            // Log error but continue processing without ship data
            System.err.println(
                "Failed to parse TGA response for permit: " +
                safeGetString(() -> permitRequest.getPermitNumber()) +
                ". Error: " +
                e.getMessage()
            );
        }

        return new ShipDataExtractor(tgaBaseResponse, shipResponse);
    }

    private boolean isTgaResponseAvailable(YachtCharterPermit permitRequest) {
        return Optional
            .ofNullable(permitRequest.getMarineMedium())
            .filter(marineMedium -> Boolean.TRUE.equals(marineMedium.getSaudi()))
            .map(MarineMedium::getTgaResponse)
            .filter(StringUtils::isNotBlank)
            .isPresent();
    }

    private Map<String, String> buildTemplateVariables(
        YachtCharterPermit permitRequest,
        ShipDataExtractor shipDataExtractor,
        SimpleDateFormat dateFormat
    ) {
        Map<String, String> variables = new HashMap<>();

        // Basic permit information
        variables.put(LICENSE_NUMBER_VAR, safeGetString(() -> permitRequest.getPermitNumber()));
        variables.put(START_DATE_VAR, formatDate(permitRequest.getStartDate(), dateFormat));
        variables.put(END_DATE_VAR, formatDate(permitRequest.getEndDate(), dateFormat));
        variables.put(REGISTRATION_NUMBER_VAR, safeGetString(() -> permitRequest.getRequestNumber()));
        variables.put(ANCHORAGE_AREA_VAR, safeGetString(() -> permitRequest.getAnchorageArea()));
        variables.put(DELEGATOR_MOBILE_NUMBER_VAR, safeGetString(() -> permitRequest.getDelegatorMobileNumber()));

        // Marine medium information
        variables.put(FLAG_COUNTRY_VAR, extractFlagCountry(permitRequest));
        variables.put(INTERNATIONAL_ORG_NUMBER_VAR, extractImoNumber(permitRequest));
        variables.put(YACHT_COLOR_VAR, safeGetString(() -> permitRequest.getMarineColor()));
        variables.put(SHIP_TYPE_VAR, extractMarineMediumType(permitRequest));
        variables.put(SHIP_NAME_VAR, extractMarineMediumName(permitRequest));

        // Ship-specific information (from TGA response)
        variables.put(CALL_SIGN_NUMBER_VAR, shipDataExtractor.getCallSignNumber());
        variables.put(REGISTER_PORT_VAR, shipDataExtractor.getRegistrationPortId());
        variables.put(DIMENSIONS_VAR, shipDataExtractor.getDimensions());

        // Company information
        variables.put(YACHT_CHARTER_COMPANY_NAME_VAR, extractCompanyName(permitRequest));
        variables.put(NATIONALITY_VAR, extractCompanyNationality(permitRequest));

        // Charter and captain information
        variables.put(TENANT_NUMBER_VAR, safeGetString(() -> permitRequest.getCharterName()));
        variables.put(CAPTAIN_NAME_VAR, safeGetString(() -> permitRequest.getCaptainName()));

        return variables;
    }

    // Safe string extraction with null checking
    private String safeGetString(Supplier<String> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            return null;
        }
    }

    private String formatDate(Instant date, SimpleDateFormat dateFormat) {
        return date != null ? dateFormat.format(Date.from(date)) : null;
    }

    private String extractFlagCountry(YachtCharterPermit permitRequest) {
        return Optional
            .ofNullable(permitRequest.getMarineMedium())
            .map(MarineMedium::getFlagNationality)
            .map(Country::getNameAr)
            .orElse(null);
    }

    private String extractImoNumber(YachtCharterPermit permitRequest) {
        return Optional.ofNullable(permitRequest.getMarineMedium()).map(MarineMedium::getImoNumber).orElse(null);
    }

    private String extractMarineMediumType(YachtCharterPermit permitRequest) {
        return Optional
            .ofNullable(permitRequest.getMarineMedium())
            .map(MarineMedium::getMarineMediumType)
            .map(MarineMediumType::getNameAr)
            .orElse(null);
    }

    private String extractMarineMediumName(YachtCharterPermit permitRequest) {
        return Optional.ofNullable(permitRequest.getMarineMedium()).map(MarineMedium::getNameAr).orElse(null);
    }

    private String extractCompanyName(YachtCharterPermit permitRequest) {
        return Optional.ofNullable(permitRequest.getCompany()).map(Company::getCrName).orElse(null);
    }

    private String extractCompanyNationality(YachtCharterPermit permitRequest) {
        return Optional.ofNullable(permitRequest.getCompany()).map(Company::getCompanyNationality).map(Country::getNameAr).orElse(null);
    }

    private void addQRCodeToTemplate(Map<String, String> templateVariables, String permitUuid) throws WriterException {
        if (StringUtils.isBlank(permitUuid)) {
            System.err.println("Warning: Permit UUID is null or empty, skipping QR code generation");
            return;
        }

        try {
            String qrCodeUrl = buildQRCodeUrl(permitUuid);
            byte[] qrCodeImage = QRCodeGenerator.getQRCodeImage(qrCodeUrl, QR_CODE_SIZE, QR_CODE_SIZE);
            String base64EncodedImage = Base64.getEncoder().encodeToString(qrCodeImage);

            templateVariables.put(QR_CODE_VAR, base64EncodedImage);

            System.out.println("QR Code generated successfully for URL: " + qrCodeUrl);
        } catch (Exception e) {
            String errorMessage = "Failed to generate QR code for permit UUID: " + permitUuid + ". Error: " + e.getMessage();
            //            System.err.println(errorMessage);
            throw new WriterException(errorMessage);
        }
    }

    private String buildQRCodeUrl(String permitUuid) {
        String baseURL = StringUtils.substringBeforeLast(StringUtils.substringBeforeLast(getCurrentAPIURL(), "/"), "/");
        return baseURL + "/report/" + permitUuid;
    }

    private void setCompanyActivities(Map<String, String> map, List<Activity> sortedCompanyActivityList) {
        if (!sortedCompanyActivityList.isEmpty()) {
            map.put("${mainActivity}", sortedCompanyActivityList.get(0).getName());
        } else {
            map.put("${mainActivity}", "");
            map.put("${subActivity}", "");
        }
        if (sortedCompanyActivityList.size() >= 2) {
            map.put("${subActivity}", sortedCompanyActivityList.get(1).getName());
        } else {
            map.put("${subActivity}", "");
        }
    }

    private String getCurrentAPIURL() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletRequest request = servletRequestAttributes.getRequest();
        StringBuffer url = request.getRequestURL();
        return url.toString();
    }

    public List<AdminUserDTO> getLicensingOfficers() {
        List<AdminUserDTO> licensingOfficers = new ArrayList<>();
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        User loggedInUser = userRepository.findById(rsaUser.getUserID()).get();
        AdminUserDTO loggedInUserDTO = userMapper.userToAdminUserDTO(loggedInUser);

        boolean isLicensingOfficer = SecurityUtils.isLicensingOfficer();
        boolean isLicensingSupervisor = SecurityUtils.isLicensingSupervisor();

        if (isLicensingOfficer && !isLicensingSupervisor) {
            licensingOfficers.add(loggedInUserDTO);
        } else {
            List<User> users = userRepository.findAllByAuthority(List.of(AuthoritiesConstants.SRSA_LICENSING_OFFICER));
            users.forEach(user -> licensingOfficers.add(userMapper.userToAdminUserDTO(user)));
        }
        return licensingOfficers;
    }

    public List<AdminUserDTO> getInspectionOfficers() {
        List<AdminUserDTO> inspectionOfficers = new ArrayList<>();
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        User loggedInUser = userRepository.findById(rsaUser.getUserID()).get();
        AdminUserDTO loggedInUserDTO = userMapper.userToAdminUserDTO(loggedInUser);

        boolean isInspectionOfficer = SecurityUtils.isInspectionOfficer();
        boolean isInspectionSupervisor = SecurityUtils.isInspectionSupervisor();

        if (isInspectionOfficer && !isInspectionSupervisor) {
            inspectionOfficers.add(loggedInUserDTO);
        } else {
            List<User> users = userRepository.findAllByAuthority(List.of(AuthoritiesConstants.SRSA_INSPECTION_OFFICER));
            users.forEach(user -> inspectionOfficers.add(userMapper.userToAdminUserDTO(user)));
        }
        return inspectionOfficers;
    }

    public List<AdminUserDTO> getLicensingManagers() {
        List<String> authority = new ArrayList<>();
        authority.add(0, AuthoritiesConstants.SRSA_LICENSING_MANAGER);
        List<User> users = userRepository.findAllByAuthority(authority);
        List<AdminUserDTO> dto = new ArrayList<>();
        users.forEach(user -> {
            dto.add(userMapper.userToAdminUserDTO(user));
        });
        return dto;
    }

    public byte[] exportYachtCharterPermitUUIDByUUID(String yachtCharterPermitUUID) throws IOException, WriterException {
        Optional<YachtCharterPermit> optionalYachtCharterPermit = yachtCharterPermitRepository.findOneByUuid(
            UUID.fromString(yachtCharterPermitUUID)
        );
        if (optionalYachtCharterPermit.isEmpty()) {
            return null;
        }
        return exportYachtCharterPermit(optionalYachtCharterPermit.get());
    }

    public YachtCharterPermit updateWFInfo(YachtCharterPermitDTO permitRequestDTO, AdminUserDTO officer, String role) {
        LOG.debug("Request to update TechnicalPermit work flow: {}", permitRequestDTO);
        YachtCharterPermit permitRequest = null;
        Optional<YachtCharterPermit> permitRequestOptional = yachtCharterPermitRepository.findById(permitRequestDTO.getId());
        if (permitRequestOptional.isPresent()) {
            permitRequest = permitRequestOptional.get();
            permitRequest.setRequiredPayFees(permitRequestDTO.getRequiredPayFees());
            permitRequest.setExceptionalApproval(permitRequestDTO.getExceptionalApproval());
            permitRequest.setRequestStatus(permitRequestDTO.getRequestStatus());
            permitRequest.setRequestNote(permitRequestDTO.getRequestNote());
            if (permitRequestDTO.getRequestStatus() == YachtCharterPermitStatusType.PENDING_PAYMENT) {
                permitRequest.setPaymentPeriodDate(
                    Instant.now().plus(permitRequest.getServiceType().getPaymentPeriodDays(), ChronoUnit.DAYS)
                );
            }

            if (permitRequestDTO.getRequestStatus() == YachtCharterPermitStatusType.COMPLETED) {
                Calendar currentDate = Calendar.getInstance();
                permitRequest.setIssueDate(currentDate.getTime());
                // As per BRD duration of permit is 6 months
                int durationMonths = 6;
                //                    switch (permitRequestDTO.getLicenseRequest().getRequestedLicenseDuration()) {
                //                        case TWO_YEAR -> 2;
                //                        case THREE_YEAR -> 3;
                //                        default -> 1;
                //                    };

                currentDate.add(Calendar.MONTH, durationMonths);
                permitRequest.setExpiryDate(currentDate.getTime());
            }
            permitRequest = yachtCharterPermitRepository.save(permitRequest);

            User assignedUser = null;
            if (officer != null) {
                assignedUser = userRepository.findById(officer.getId()).orElse(null);
            }
            if (assignedUser == null) {
                Long currentUserId = SecurityUtils.getCurrentUser().getUserID();
                assignedUser = userRepository.findById(currentUserId).orElse(null);
            }
            if (assignedUser != null) {
                permitRequest.setAssignedEmployee(assignedUser);
                permitRequest.setAssignedEmployeeName(assignedUser.getFirstName());
                permitRequest.setAssignedEmployeeRole(role);
            }
            saveYachtCharterPermitStatus(permitRequest);
        }
        return permitRequest;
    }

    private void saveYachtCharterPermitStatus(YachtCharterPermit technicalPermit) {
        YachtCharterPermitStatus technicalPermitStatus = new YachtCharterPermitStatus();
        technicalPermitStatus.setYachtCharterPermit(technicalPermit);
        technicalPermitStatus.setRequestStatus(technicalPermit.getRequestStatus());
        technicalPermitStatus.setRequestNote(technicalPermit.getRequestNote());
        technicalPermitStatus.setRequestNumber(technicalPermit.getRequestNumber());
        technicalPermitStatus.setAssignedEmployeeName(technicalPermit.getAssignedEmployeeName());
        technicalPermitStatus.setAssignedEmployee(technicalPermit.getAssignedEmployee());
        yachtCharterPermitStatusRepository.save(technicalPermitStatus);
    }

    public ResponseEntity<Boolean> selectLicensingManagers(YachtCharterPermitDTO yachtCharterPermitDTO, AdminUserDTO officer) {
        yachtCharterPermitDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_MANAGEMENT_LICENSING);
        updateWFInfo(yachtCharterPermitDTO, officer, AuthoritiesConstants.SRSA_LICENSING_MANAGER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> selectLicensingOfficer(YachtCharterPermitDTO permitRequestDTO, AdminUserDTO officer) {
        permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.UNDER_PROCESS);
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SRSA_LICENSING_OFFICER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> selectInspectionOfficers(YachtCharterPermitDTO permitRequestDTO, AdminUserDTO officer) {
        permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.UNDER_INSPECTION);
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SRSA_INSPECTION_OFFICER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> returnYachtCharterPermit(YachtCharterPermitDTO permitRequestDTO) {
        String role = "";
        if (SecurityUtils.isLicensingOfficer() && permitRequestDTO.getRequestStatus().equals(YachtCharterPermitStatusType.UNDER_PROCESS)) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.RETURNED_LICENSING_OFFICER);
            role = AuthoritiesConstants.SRSA_LICENSING_OFFICER;
        }
        if (
            SecurityUtils.isLicensingManager() &&
            permitRequestDTO.getRequestStatus().equals(YachtCharterPermitStatusType.PENDING_MANAGEMENT_LICENSING)
        ) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.RETURNED_LICENSING_MANAGER);
            role = AuthoritiesConstants.SRSA_LICENSING_MANAGER;
        }
        if (
            SecurityUtils.isInspectionOfficer() && permitRequestDTO.getRequestStatus().equals(YachtCharterPermitStatusType.UNDER_INSPECTION)
        ) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.RETURNED_INSPECTION_OFFICER);
            role = AuthoritiesConstants.SRSA_INSPECTION_OFFICER;
        }
        updateWFInfo(permitRequestDTO, null, role);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> rejectYachtCharterPermit(YachtCharterPermitDTO permitRequestDTO) {
        permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.REJECTED);
        AdminUserDTO officer = new AdminUserDTO();
        officer.setId(SecurityUtils.getCurrentUser().getUserID());
        updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SERVICE_PROVIDER);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> unAssignYachtCharterPermit(YachtCharterPermitDTO permitRequestDTO) {
        String role = "";

        if (permitRequestDTO.getRequestStatus() == YachtCharterPermitStatusType.UNDER_PROCESS) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_REVIEW);
            role = AuthoritiesConstants.SRSA_LICENSING_SUPERVISOR;
        } else if (permitRequestDTO.getRequestStatus() == YachtCharterPermitStatusType.UNDER_INSPECTION) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_INSPECTION);
            role = AuthoritiesConstants.SRSA_INSPECTION_SUPERVISOR;
        }

        AdminUserDTO officer = new AdminUserDTO();
        officer.setId(SecurityUtils.getCurrentUser().getUserID());
        updateWFInfo(permitRequestDTO, officer, role);
        return ResponseEntity.ok().body(true);
    }

    public Resource getAttachmentVisitFile(Long permitVisitId) {
        Optional<YachtCharterPermitVisitAttachment> visitAttachmentOptional =
            yachtCharterPermitVisitAttachmentRepository.findFirstByYachtCharterPermitVisitIdOrderByCreatedDateDesc(permitVisitId);
        if (visitAttachmentOptional.isEmpty()) {
            LOG.error("Couldn't find attachment for visit with id {}", permitVisitId);
            return null;
        }
        YachtCharterPermitVisitAttachment visitAttachment = visitAttachmentOptional.get();
        return downloadUtil.getFileAsResourceWithPath(visitAttachment.getUrl(), visitAttachment.getName());
    }

    public ResponseEntity<Boolean> licensingOfficerSubmit(Boolean needVisit, YachtCharterPermitDTO permitRequestDTO) {
        String role = "";
        if (needVisit) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_INSPECTION);
            role = AuthoritiesConstants.SRSA_LICENSING_SUPERVISOR;
        } else {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_MANAGEMENT_LICENSING);
            role = AuthoritiesConstants.SRSA_LICENSING_MANAGER;
        }
        updateWFInfo(permitRequestDTO, null, role);
        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> sendToInspectionManager(YachtCharterPermitDTO permitRequestDTO, List<MultipartFile> files) {
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        Optional<User> user = userRepository.findOneWithAuthoritiesById(rsaUser.getUserID());
        if (user.isPresent()) {
            permitRequestDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_REPORT_APPROVAL);
            YachtCharterPermit yachtCharterPermit = updateWFInfo(permitRequestDTO, null, AuthoritiesConstants.SRSA_INSPECTION_MANAGER);
            YachtCharterPermitVisit yachtCharterPermitVisit = yachtCharterPermitVisitRepository
                .findFirstByYachtCharterPermitIdOrderByCreatedDateDesc(yachtCharterPermit.getId())
                .get();
            yachtCharterPermitVisit.setVisitStatus(VisitStatus.COMPLETED);
            yachtCharterPermitVisitRepository.save(yachtCharterPermitVisit);
            if (files != null && !files.isEmpty()) {
                List<YachtCharterPermitAttachmentDTO> attachmentDTOS = new ArrayList<>();
                Set<YachtCharterPermitAttachment> attachmentsSaved = yachtCharterPermit.getAttachments();
                yachtCharterPermit.setAttachments(null);
                for (MultipartFile multipartFile : files) {
                    try {
                        if (!Objects.requireNonNull(multipartFile.getOriginalFilename()).equalsIgnoreCase("no-file-h")) {
                            // TODO pass allowed max size for files here
                            yachtCharterPermitAttachmentService.saveFileAndReturnPath(
                                Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                                multipartFile.getOriginalFilename(),
                                yachtCharterPermit,
                                attachmentDTOS,
                                false,
                                null
                            );
                        }
                    } catch (IOException e) {
                        throw new RuntimeException("failed to save attachment in yacht charter permit  saving : " + e.getMessage());
                    }
                }
                yachtCharterPermit.setAttachments(attachmentsSaved);
                if (!attachmentDTOS.isEmpty()) {
                    attachmentsToEntity(yachtCharterPermit, yachtCharterPermitVisit, attachmentDTOS);
                }
            }
        }

        return ResponseEntity.ok().body(true);
    }

    private void attachmentsToEntity(
        YachtCharterPermit permitRequest,
        YachtCharterPermitVisit permitVisit,
        List<YachtCharterPermitAttachmentDTO> attachmentDTOS
    ) {
        List<YachtCharterPermitAttachment> attachments = yachtCharterPermitAttachmentMapper.toEntity(attachmentDTOS);
        for (YachtCharterPermitAttachment attachment : attachments) {
            attachment.yachtCharterPermit(permitRequest);
            YachtCharterPermitVisitAttachment visitAttachment = new YachtCharterPermitVisitAttachment();
            visitAttachment.setYachtCharterPermitVisit(permitVisit);
            visitAttachment.setUrl(attachment.getDocUrl());
            visitAttachment.setName(attachment.getDocName());
            yachtCharterPermitVisitAttachmentRepository.save(visitAttachment);
        }
        yachtCharterPermitAttachmentRepository.saveAll(attachments);
    }

    public ResponseEntity<Boolean> editVisitReportAndResendToInspectionManager(
        YachtCharterPermitDTO yachtCharterPermitDTO,
        List<MultipartFile> files
    ) {
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        Optional<User> user = userRepository.findOneWithAuthoritiesById(rsaUser.getUserID());
        if (user.isPresent()) {
            yachtCharterPermitDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_REPORT_APPROVAL);
            YachtCharterPermit yachtCharterPermit = updateWFInfo(yachtCharterPermitDTO, null, AuthoritiesConstants.SRSA_INSPECTION_MANAGER);
            YachtCharterPermitVisit yachtCharterPermitVisit = yachtCharterPermitVisitRepository
                .findFirstByYachtCharterPermitIdOrderByCreatedDateDesc(yachtCharterPermit.getId())
                .get();
            if (files != null && !files.isEmpty()) {
                List<YachtCharterPermitAttachmentDTO> attachmentDTOS = new ArrayList<>();
                for (MultipartFile multipartFile : files) {
                    try {
                        // TODO pass allowed max size for files here
                        yachtCharterPermitAttachmentService.saveFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            yachtCharterPermit,
                            attachmentDTOS,
                            false,
                            null
                        );
                    } catch (IOException e) {
                        throw new RuntimeException("failed to save attachment in technical permit  saving : " + e.getMessage());
                    }
                }
                attachmentsToEntity(yachtCharterPermit, yachtCharterPermitVisit, attachmentDTOS);
            }
        }

        return ResponseEntity.ok().body(true);
    }

    public ResponseEntity<Boolean> scheduleVisit(YachtCharterPermitDTO yachtCharterPermitDTO, LocalDateTime dateTime) {
        RsaUser rsaUser = SecurityUtils.getCurrentUser();
        Optional<User> user = userRepository.findOneWithAuthoritiesById(rsaUser.getUserID());
        if (user.isPresent()) {
            YachtCharterPermitVisit yachtCharterPermitVisit = new YachtCharterPermitVisit();
            if (yachtCharterPermitDTO.getRequestStatus() == YachtCharterPermitStatusType.PENDING_VISIT) {
                yachtCharterPermitVisit =
                    yachtCharterPermitVisitRepository
                        .findFirstByYachtCharterPermitIdOrderByCreatedDateDesc(yachtCharterPermitDTO.getId())
                        .get();
            }
            yachtCharterPermitDTO.setRequestStatus(YachtCharterPermitStatusType.PENDING_VISIT);
            AdminUserDTO officer = new AdminUserDTO();
            officer.setId(SecurityUtils.getCurrentUser().getUserID());
            YachtCharterPermit permitRequest = updateWFInfo(yachtCharterPermitDTO, officer, AuthoritiesConstants.SRSA_INSPECTION_OFFICER);

            yachtCharterPermitVisit.setYachtCharterPermit(permitRequest);
            yachtCharterPermitVisit.setUser(user.get());
            yachtCharterPermitVisit.setVisitDateG(dateTime);
            yachtCharterPermitVisit.setVisitStatus(VisitStatus.SCHEDULED);
            yachtCharterPermitVisitRepository.save(yachtCharterPermitVisit);
        }

        return ResponseEntity.ok().body(true);
    }

    public List<YachtCharterPermitVisit> getPermitVisit(YachtCharterPermit yachtCharterPermit) {
        return yachtCharterPermitVisitRepository.findAllByYachtCharterPermitIdOrderByCreatedDateAsc(yachtCharterPermit.getId());
    }

    public ResponseEntity<Boolean> sendToPayYachtCharterPermit(YachtCharterPermitDTO permitRequestDTO, List<MultipartFile> files) {
        permitRequestDTO.setRequestStatus(
            BooleanUtils.isTrue(permitRequestDTO.getExceptionalApproval())
                ? (BooleanUtils.isTrue(permitRequestDTO.getRequiredPayFees())
                        ? YachtCharterPermitStatusType.PENDING_PAYMENT
                        : YachtCharterPermitStatusType.COMPLETED)
                : YachtCharterPermitStatusType.PENDING_PAYMENT
        );

        AdminUserDTO officer = new AdminUserDTO();
        officer.setId(SecurityUtils.getCurrentUser().getUserID());
        YachtCharterPermit permitRequest = updateWFInfo(permitRequestDTO, officer, AuthoritiesConstants.SERVICE_PROVIDER);
        if (files != null && !files.isEmpty()) {
            List<YachtCharterPermitAttachmentDTO> attachmentDTOS = new ArrayList<>();
            Set<YachtCharterPermitAttachment> attachmentsSaved = permitRequest.getAttachments();
            permitRequest.setAttachments(null);
            for (MultipartFile multipartFile : files) {
                try {
                    if (!Objects.requireNonNull(multipartFile.getOriginalFilename()).equalsIgnoreCase("no-file-h")) {
                        yachtCharterPermitAttachmentService.saveFileAndReturnPath(
                            Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                            multipartFile.getOriginalFilename(),
                            permitRequest,
                            attachmentDTOS,
                            false,
                            5L
                        );
                    }
                } catch (IOException e) {
                    throw new RuntimeException("failed to save attachment in technical permit  saving : " + e.getMessage());
                }
            }
            permitRequest.setAttachments(attachmentsSaved);
            List<YachtCharterPermitAttachment> attachments = yachtCharterPermitAttachmentMapper.toEntity(attachmentDTOS);
            for (YachtCharterPermitAttachment attachment : attachments) {
                attachment.yachtCharterPermit(permitRequest);
            }
            yachtCharterPermitAttachmentRepository.saveAll(attachments);
        }
        return ResponseEntity.ok().body(true);
    }

    public List<PermitChanges> getPermitChanges(String requestNumber) {
        return permitChangesRepository.findAllByRequestNumber(requestNumber);
    }
}
