<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <property name="now" value="GETDATE()" dbms="mssql"/>
    <property name="floatType" value="float" dbms="mssql"/>
    <property name="clobType" value="clob" dbms="mssql"/>
    <property name="blobType" value="blob" dbms="mssql"/>
    <property name="uuidType" value="uuid" dbms="mssql"/>
    <property name="datetimeType" value="datetime" dbms="mssql"/>

    <include file="config/liquibase/changelog/20240229170900_entity_yakeen_token.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240226164600_initial_schema.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20231204121338_added_password_history_entity.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240229170100_entity_service_request.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240307195200_entity_absher_otp_data.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240307203900_entity_otp_data.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240229221300_entity_user_info.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240327113000_entity_general_address.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240327113000_entity_national_address.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240325113000_entity_company.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/202403020143000_entity_registration_request.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240327143000_entity_user_company.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240418130000_added_entity_BlackListedToken.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/20240429102448_added_entity_Attachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102449_added_entity_AttachmentType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102451_added_entity_Country.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102452_added_entity_FuelTypes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102453_added_entity_InvestmentType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102454_added_entity_LicenseProvider.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102455_added_entity_LicenseRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102456_added_entity_LicenseRequestCountry.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102457_added_entity_LicenseRequestCountryHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102458_added_entity_LicenseRequestFuelTypes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102459_added_entity_LicenseRequestFuelTypesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102500_added_entity_LicenseRequestHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102501_added_entity_LicenseRequestMarineTourServices.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102502_added_entity_LicenseRequestMarineTourServicesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102503_added_entity_LicenseRequestMaritimeMedium.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102504_added_entity_LicenseRequestMaritimeMediumHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102505_added_entity_LicenseRequestMaritimeServices.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102506_added_entity_LicenseRequestMaritimeServicesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102507_added_entity_LicenseRequestProvidedLanguages.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102508_added_entity_LicenseRequestProvidedLanguagesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102509_added_entity_MarineTourOperatorServices.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102510_added_entity_MaritimeMedium.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102511_added_entity_MaritimeTourismServices.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102513_added_entity_PermitRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102514_added_entity_PermitRequestHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102515_added_entity_PermitRequestStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102516_added_entity_ProvidedLanguages.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102517_added_entity_ServiceType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240502093918_added_entity_DocumentType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240502093919_added_entity_ServiceTypeDocumentType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240502093920_added_entity_TermsConditionsType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240502093921_added_entity_ServiceTypeTermsConditions.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240507132435_added_entity_LiabilityType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240507132436_added_entity_ServiceTypeLiabilityType.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_PaymentRequest.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20250702-allow-null-company-id-payment-request.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_PaymentRequestCustomer.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_PaymentRequestInvoice.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_PaymentRequestInvoiceLineItem.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_PaymentRequestCreateInvoiceResponse.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240526212600_added_entity_UserType.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240527182700_added_entity_PaymentRequestReturnResult.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240527183600_added_entity_PaymentRequestNotification.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240601155500_added_entity_GovEmployeeType.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240429102499_added_entity_Docks.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240429102499_added_entity_LicenseRequestDocks.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240429102500_added_entity_LicenseRequestDocksHistory.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240822102448_added_entity_permitchanges.xml" relativeToChangelogFile="false"/>


    <include file="config/liquibase/changelog/20240429102448_added_entity_constraints_Attachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102454_added_entity_constraints_LicenseProvider.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102455_added_entity_constraints_LicenseRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102456_added_entity_constraints_LicenseRequestCountry.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102457_added_entity_constraints_LicenseRequestCountryHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102458_added_entity_constraints_LicenseRequestFuelTypes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102459_added_entity_constraints_LicenseRequestFuelTypesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102500_added_entity_constraints_LicenseRequestHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102501_added_entity_constraints_LicenseRequestMarineTourServices.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102502_added_entity_constraints_LicenseRequestMarineTourServicesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102503_added_entity_constraints_LicenseRequestMaritimeMedium.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102504_added_entity_constraints_LicenseRequestMaritimeMediumHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102505_added_entity_constraints_LicenseRequestMaritimeServices.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102506_added_entity_constraints_LicenseRequestMaritimeServicesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102507_added_entity_constraints_LicenseRequestProvidedLanguages.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102508_added_entity_constraints_LicenseRequestProvidedLanguagesHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102513_added_entity_constraints_PermitRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102514_added_entity_constraints_PermitRequestHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240429102515_added_entity_constraints_PermitRequestStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240502093919_added_entity_constraints_ServiceTypeDocumentType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240502093921_added_entity_constraints_ServiceTypeTermsConditions.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240507132436_added_entity_constraints_ServiceTypeLiabilityType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240505170000_entity_service_rating.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/2024050517045_entity_permit_visit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/2024050517045_entity_visit_attachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240529102451_added_entity_Mock.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240418130000_added_entity_activeToken.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_constraints_PaymentRequest.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_constraints_PaymentRequestInvoice.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240519132100_added_entity_constraints_PaymentRequestInvoiceLineItem.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240526223200_added_entity_constraints_UserType.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240429102500_added_entity_constraints_LicenseRequestDocks.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240429102500_added_entity_constraints_LicenseRequestDocksHistory.xml" relativeToChangelogFile="false"/>

    <!--Quartz DB Changes-->
    <include file="/config/liquibase/changelog/20240710192200_quartz_db_changes.xml" relativeToChangelogFile="false"/>

    <include file="/config/liquibase/changelog/20240429102448_added_entity_User_Attachment.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240429102448_added_entity_constraints_user_Attachment.xml" relativeToChangelogFile="false"/>

    <include file="/config/liquibase/changelog/20240803002448_added_entity_Company_Attachment.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240803102448_added_entity_constraints_company_Attachment.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240804202458_added_entity_RelationshipWithOrganizationTypes.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240812150000_entity_profile_update_request.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240820130000_entity_marine_medium.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20240902160000_mta_delegation.xml" relativeToChangelogFile="false"/>


    <include file="config/liquibase/changelog/20240908111135_added_entity_ArrivalRequestNationality.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111137_added_entity_PortCountry.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111136_added_entity_PortCode.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111138_added_entity_Routes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111139_added_entity_CargoType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111140_added_entity_Container.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111141_added_entity_Passenger.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111436_added_entity_ArrivalDepNotification.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111437_added_entity_ArrivalRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111640_added_entity_DepartureRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240909071916_added_entity_ContainerType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240909071917_added_entity_ArrivalDepAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155324_added_entity_TechPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155325_added_entity_MarineAccessoriesItem.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155326_added_entity_TechnicalPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155327_added_entity_TechPermitPort.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155328_added_entity_TechPermitNationality.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155329_added_entity_TechPermitPassengers.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155330_added_entity_TechnicalPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/2024050517045_entity_tech_permit_visit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155331_added_entity_TechnicalPermitActivity.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155332_added_entity_TechnicalPermitHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155333_added_entity_TechnicalPermitActivityHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074849_added_entity_TourismPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074850_added_entity_TourismPermitNationality.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074851_added_entity_TourismPermitPassengerAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074852_added_entity_TourismPermitPassengers.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074853_added_entity_TourismPermitMarine.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074854_added_entity_TourismPermitRegion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074855_added_entity_TourismPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074856_added_entity_TourismPermitRegionStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074857_added_entity_TourismPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241030084650_added_entity_PermitRegion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110837_added_entity_TransitPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110838_added_entity_TransitPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110839_added_entity_TransitPermitDestination.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110840_added_entity_TransitPermitNationality.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110841_added_entity_TransitPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110842_added_entity_TransitPermitHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100431_added_entity_YachtCharterPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100432_added_entity_YachtCharterPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100433_added_entity_YachtCharterPermitDestination.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100434_added_entity_YachtCharterPermitNationality.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100435_added_entity_YachtCharterPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100436_added_entity_YachtCharterPermitHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250724154200-add-license-fields-to-permits.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->

    <include file="config/liquibase/changelog/20240908111138_added_entity_constraints_Routes.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111141_added_entity_constraints_Passenger.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111436_added_entity_constraints_ArrivalDepNotification.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111437_added_entity_constraints_ArrivalRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111640_added_entity_constraints_DepartureRequest.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240908111140_added_entity_constraints_Container.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240909071917_added_entity_constraints_ArrivalDepAttachment.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/20240915155324_added_entity_constraints_TechPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155325_added_entity_constraints_MarineAccessoriesItem.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155326_added_entity_constraints_TechnicalPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155329_added_entity_constraints_TechPermitPassengers.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155330_added_entity_constraints_TechnicalPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155331_added_entity_constraints_TechnicalPermitActivity.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155332_added_entity_constraints_TechnicalPermitHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240915155333_added_entity_constraints_TechnicalPermitActivityHistory.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/2024050517045_entity_tech_permit_visit_attachment.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/20240919150000_added_entity_ArrivalReason.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20240929_added_entity_disclosure.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20241015_entity_navigation_permit.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20241015_added_entity_navigation_permit_attachment.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/add-navigation_permit-FKs.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074849_added_entity_constraints_TourismPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074851_added_entity_constraints_TourismPermitPassengerAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074852_added_entity_constraints_TourismPermitPassengers.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074854_added_entity_constraints_TourismPermitRegion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074855_added_entity_constraints_TourismPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074856_added_entity_constraints_TourismPermitRegionStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241029074857_added_entity_constraints_TourismPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241118120000_super_admin_user.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110837_added_entity_constraints_TransitPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110838_added_entity_constraints_TransitPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110841_added_entity_constraints_TransitPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215110842_added_entity_constraints_TransitPermitHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100431_added_entity_constraints_YachtCharterPermitAttachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100432_added_entity_constraints_YachtCharterPermitStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100435_added_entity_constraints_YachtCharterPermit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20241215100436_added_entity_constraints_YachtCharterPermitHistory.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/add_yacht_charter_permit_attachment_types.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
    <include file="config/liquibase/changelog/20240515-add-yacht-charter-permit-columns.xml"/>
    <include file="config/liquibase/changelog/2024050517045_entity_yacht_charter_permit_visit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/2024050517045_entity_yacht_charter_permit_visit_attachment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/create_all_permit_requests_view.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/update-company-owner-table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/update-wathq-mock-values.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/alter-identity-type-to-varchar-max.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/025-06-12-create-wathq-api-response.xml"/>
    <include file="config/liquibase/changelog/20250617-drop-constraint-arrival-request-nationalty.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250720013511_added_entity_BeachActivites.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250720013511_added_entity_BeachEquipment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250720012205_added_entity_LicenseRequestBeachActivities.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250720012205_added_entity_LicenseRequestBeachEquipment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250720012105_added_entity_constraints_LicenseRequestBeachActivites.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250720012105_added_entity_constraints_LicenseRequestBeachEquipment.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250717163000_port_countries_nationalities_codes.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20250730160000_insert_permit_region.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20250802-add-tourism-visa-request-entity.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/20250806-add-applicant-to-visa-attachment.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
