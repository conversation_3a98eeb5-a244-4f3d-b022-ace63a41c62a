package com.elm.srsa.web.rest;

import com.elm.srsa.domain.*;
import com.elm.srsa.domain.enumeration.StatusType;
import com.elm.srsa.repository.BeachActivitiesRepository;
import com.elm.srsa.repository.BeachEquipmentRepository;
import com.elm.srsa.repository.PermitRequestRepository;
import com.elm.srsa.service.AttachmentService;
import com.elm.srsa.service.PermitRequestService;
import com.elm.srsa.service.PermitRequestViewQueryService;
import com.elm.srsa.service.PermitRequestViewService;
import com.elm.srsa.service.criteria.PermitRequestViewCriteria;
import com.elm.srsa.service.dto.AdminUserDTO;
import com.elm.srsa.service.dto.AssignLicensingOfficerDTO;
import com.elm.srsa.service.dto.PermitRequestDTO;
import com.elm.srsa.service.dto.PermitRequestListDTO;
import com.elm.srsa.util.DateTimeParser;
import com.elm.srsa.util.FileDownloadUtil;
import com.elm.srsa.util.JsonUtils;
import com.elm.srsa.web.rest.errors.BadRequestAlertException;
import com.google.zxing.WriterException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.elm.srsa.domain.PermitRequest}.
 */
@RestController
@RequestMapping("/api/permit-requests")
public class PermitRequestResource {

    private final Logger log = LoggerFactory.getLogger(PermitRequestResource.class);

    private static final String ENTITY_NAME = "permitRequest";
    private final AttachmentService attachmentService;
    private final FileDownloadUtil fileDownloadUtil;
    private final BeachActivitiesRepository beachActivitiesRepository;
    private final BeachEquipmentRepository beachEquipmentRepository;

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PermitRequestService permitRequestService;

    private final PermitRequestViewService permitRequestViewService;
    private final PermitRequestViewQueryService permitRequestViewQueryService;
    private final PermitRequestRepository permitRequestRepository;

    public PermitRequestResource(
        PermitRequestService permitRequestService,
        PermitRequestViewService permitRequestViewService,
        PermitRequestViewQueryService permitRequestViewQueryService,
        PermitRequestRepository permitRequestRepository,
        AttachmentService attachmentService,
        FileDownloadUtil fileDownloadUtil,
        BeachActivitiesRepository beachActivitiesRepository,
        BeachEquipmentRepository beachEquipmentRepository
    ) {
        this.permitRequestService = permitRequestService;
        this.permitRequestViewService = permitRequestViewService;
        this.permitRequestViewQueryService = permitRequestViewQueryService;
        this.permitRequestRepository = permitRequestRepository;
        this.attachmentService = attachmentService;
        this.fileDownloadUtil = fileDownloadUtil;
        this.beachActivitiesRepository = beachActivitiesRepository;
        this.beachEquipmentRepository = beachEquipmentRepository;
    }

    /**
     * {@code POST  /permit-requests} : Create a new permitRequest.
     *
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new permitRequestDTO, or with status {@code 400 (Bad Request)} if the permitRequest has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    @PreAuthorize("hasAnyAuthority('ROLE_SERVICE_PROVIDER')")
    public ResponseEntity<PermitRequestDTO> createPermitRequest(
        @RequestParam("jsonObject") String jsonOrderVM,
        @RequestParam(value = "documents") MultipartFile[] file,
        HttpServletRequest request,
        HttpServletResponse res
    ) throws URISyntaxException {
        PermitRequestDTO permitRequestDTO = (PermitRequestDTO) JsonUtils.parseToObject(jsonOrderVM, PermitRequestDTO.class);
        log.debug("REST request to save PermitRequest : {}", permitRequestDTO);
        if (permitRequestDTO.getId() != null) {
            throw new BadRequestAlertException("A new permitRequest cannot already have an ID", ENTITY_NAME, "idexists");
        }
        if (file == null || file.length == 0) {
            throw new BadRequestAlertException("A new permitRequest need to have files", ENTITY_NAME, "documentsNeeded");
        }

        List<MultipartFile> files = List.of(file);
        permitRequestDTO = permitRequestService.save(permitRequestDTO, files);

        return ResponseEntity
            .created(new URI("/api/permit-requests/" + permitRequestDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, permitRequestDTO.getId().toString()))
            .body(permitRequestDTO);
    }

    /**
     * {@code PUT  /permit-requests/:id} : Updates an existing permitRequest.
     *
     * @param id the id of the permitRequestDTO to save.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated permitRequestDTO,
     * or with status {@code 400 (Bad Request)} if the permitRequestDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the permitRequestDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('ROLE_SERVICE_PROVIDER','ROLE_SRSA_USER')")
    public ResponseEntity<PermitRequestDTO> updatePermitRequest(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestParam("jsonObject") String jsonOrderVM,
        @RequestParam(value = "documents") MultipartFile[] files,
        HttpServletRequest request,
        HttpServletResponse res
    ) throws URISyntaxException {
        PermitRequestDTO permitRequestDTO = (PermitRequestDTO) JsonUtils.parseToObject(jsonOrderVM, PermitRequestDTO.class);
        log.debug("REST request to update PermitRequest : {}, {}", id, permitRequestDTO);
        if (permitRequestDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, permitRequestDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!permitRequestRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        permitRequestDTO = permitRequestService.updatePermitRequest(permitRequestDTO, files);
        return ResponseEntity
            .ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, permitRequestDTO.getId().toString()))
            .body(permitRequestDTO);
    }

    /**
     * {@code GET  /permit-requests} : get all the permitRequests.
     *
     * @param pageable the pagination information.
     * @param criteria the criteria which the requested entities should match.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of permitRequests in body.
     */
    @GetMapping("")
    @PreAuthorize("hasAnyAuthority('ROLE_SERVICE_PROVIDER','ROLE_SRSA_USER','ROLE_SRSA_ADMIN','ROLE_ADMIN', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<List<PermitRequestListDTO>> getAllPermitRequests(
        PermitRequestViewCriteria criteria,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        return permitRequestViewService.getAllPermitRequests(criteria, pageable);
    }

    /**
     * {@code GET  /permit-requests} : get all the permitRequests.
     *
     * @param pageable the pagination information.
     * @param criteria the criteria which the requested entities should match.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of permitRequests in body.
     */
    @GetMapping("/licenses")
    @PreAuthorize("hasAnyAuthority('ROLE_SERVICE_PROVIDER','ROLE_SRSA_USER')")
    public ResponseEntity<List<PermitRequestListDTO>> getAllCompletedPermitRequests(
        PermitRequestViewCriteria criteria,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        PermitRequestViewCriteria.StatusTypeFilter statusTypeFilter = new PermitRequestViewCriteria.StatusTypeFilter();
        statusTypeFilter.setEquals(StatusType.COMPLETED);
        List<PermitRequestViewCriteria.StatusTypeFilter> statusTypeFilterList = new ArrayList<>();
        statusTypeFilterList.add(statusTypeFilter);
        criteria.setRequestStatus(statusTypeFilterList);

        return permitRequestViewService.getAllPermitRequests(criteria, pageable);
    }

    /**
     * {@code GET  /permit-requests/count} : count all the permitRequests.
     *
     * @param criteria the criteria which the requested entities should match.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the count in body.
     */
    @GetMapping("/count")
    @PreAuthorize("hasAnyAuthority('ROLE_SERVICE_PROVIDER','ROLE_SRSA_USER')")
    public ResponseEntity<Long> countPermitRequests(PermitRequestViewCriteria criteria) {
        log.debug("REST request to count PermitRequests by criteria: {}", criteria);
        return ResponseEntity.ok().body(permitRequestViewQueryService.countByCriteria(criteria));
    }

    /**
     * {@code GET  /permit-requests/:id} : get the "id" permitRequest.
     *
     * @param id the id of the permitRequestDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the permitRequestDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('ROLE_SERVICE_PROVIDER','ROLE_SRSA_USER','ROLE_SRSA_ADMIN','ROLE_ADMIN', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<PermitRequest> getPermitRequest(@PathVariable("id") Long id) {
        log.debug("REST request to get PermitRequest : {}", id);
        Optional<PermitRequest> permitRequest = permitRequestService.findOne(id);
        permitRequest.get().getLicenseRequest();
        return ResponseUtil.wrapOrNotFound(permitRequest);
    }

    @GetMapping("/export-pdf/{permitRequestId}")
    @PreAuthorize(
        "hasAnyAuthority('ROLE_SRSA_LICENSING_SUPERVISOR','ROLE_SRSA_LICENSING_OFFICER', 'ROLE_SERVICE_PROVIDER','ROLE_ADMIN','ROLE_SUPER_ADMIN')"
    )
    public ResponseEntity<byte[]> generatePdf(@PathVariable("permitRequestId") Long permitRequestId) throws IOException, WriterException {
        byte[] pdfContent = permitRequestService.exportPermitRequestById(permitRequestId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("attachment", "permmit.pdf");
        return ResponseEntity.ok().headers(headers).contentLength(pdfContent.length).body(pdfContent);
    }

    @GetMapping("/report/{permitRequestUUID}")
    //    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_LICENSING_SUPERVISOR','ROLE_SRSA_LICENSING_OFFICER')") This was public and must be public
    public ResponseEntity<byte[]> generatePdfFromQR(@PathVariable("permitRequestUUID") String permitRequestUUID)
        throws IOException, WriterException {
        byte[] pdfContent = permitRequestService.exportPermitRequestByUUID(permitRequestUUID);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", permitRequestUUID + ".pdf");
        return ResponseEntity.ok().headers(headers).body(pdfContent);
    }

    @GetMapping("/getLicensingOfficers")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_LICENSING_SUPERVISOR','ROLE_SRSA_LICENSING_OFFICER', 'ROLE_SUPER_ADMIN')")
    public List<AdminUserDTO> getLicensingOfficers() {
        return permitRequestService.getLicensingOfficers();
    }

    @PostMapping("/selectLicensingOfficer")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_LICENSING_SUPERVISOR','ROLE_SRSA_LICENSING_OFFICER', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Boolean> selectLicensingOfficer(@RequestBody AssignLicensingOfficerDTO assignLicensingOfficerDTO) {
        return permitRequestService.selectLicensingOfficer(
            assignLicensingOfficerDTO.getPermitRequestDTO(),
            assignLicensingOfficerDTO.getAdminUserDTO()
        );
    }

    @GetMapping("/getInspectionOfficers")
    @PreAuthorize(
        "hasAnyAuthority('ROLE_SRSA_INSPECTION_SUPERVISOR','ROLE_SRSA_INSPECTION_MANAGER','ROLE_SRSA_INSPECTION_OFFICER', 'ROLE_SUPER_ADMIN')"
    )
    public List<AdminUserDTO> getInspectionOfficers() {
        return permitRequestService.getInspectionOfficers();
    }

    @PostMapping("/selectInspectionOfficers")
    @PreAuthorize(
        "hasAnyAuthority('ROLE_SRSA_INSPECTION_SUPERVISOR','ROLE_SRSA_INSPECTION_OFFICER','ROLE_SRSA_INSPECTION_MANAGER', 'ROLE_SUPER_ADMIN')"
    )
    public ResponseEntity<Boolean> selectInspectionOfficers(@RequestBody AssignLicensingOfficerDTO assignLicensingOfficerDTO) {
        return permitRequestService.selectInspectionOfficers(
            assignLicensingOfficerDTO.getPermitRequestDTO(),
            assignLicensingOfficerDTO.getAdminUserDTO()
        );
    }

    @GetMapping("/getLicensingManagers")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_INSPECTION_MANAGER', 'ROLE_SUPER_ADMIN')")
    public List<AdminUserDTO> getLicensingManager() {
        return permitRequestService.getLicensingManagers();
    }

    @PostMapping("/selectLicensingManagers")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_INSPECTION_MANAGER', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Boolean> selectLicensingManager(@RequestBody AssignLicensingOfficerDTO assignLicensingOfficerDTO) {
        return permitRequestService.selectLicensingManagers(
            assignLicensingOfficerDTO.getPermitRequestDTO(),
            assignLicensingOfficerDTO.getAdminUserDTO()
        );
    }

    @PostMapping("/returnPermitRequest")
    @PreAuthorize(
        "hasAnyAuthority('ROLE_SRSA_LICENSING_OFFICER','ROLE_SRSA_LICENSING_MANAGER','ROLE_SRSA_INSPECTION_OFFICER', 'ROLE_SUPER_ADMIN')"
    )
    public ResponseEntity<Boolean> returnPermitRequest(@RequestBody PermitRequestDTO permitRequestDTO) {
        return permitRequestService.returnPermitRequest(permitRequestDTO);
    }

    @PostMapping("/rejectPermitRequest")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_LICENSING_MANAGER', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Boolean> rejectPermitRequest(@RequestBody PermitRequestDTO permitRequestDTO) {
        return permitRequestService.rejectPermitRequest(permitRequestDTO);
    }

    @PostMapping("/unAssignPermitRequest")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_LICENSING_SUPERVISOR','ROLE_SRSA_INSPECTION_SUPERVISOR', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Boolean> unAssignPermitRequest(@RequestBody PermitRequestDTO permitRequestDTO) {
        return permitRequestService.unAssignPermitRequest(permitRequestDTO);
    }

    @PostMapping("/sendToPayPermitRequest")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_LICENSING_MANAGER', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Boolean> sendToPayPermitRequest(
        @RequestPart("jsonObject") String jsonOrderVM,
        @RequestParam(value = "documents", required = false) MultipartFile[] file
    ) {
        PermitRequestDTO permitRequestDTO = (PermitRequestDTO) JsonUtils.parseToObject(jsonOrderVM, PermitRequestDTO.class);
        List<MultipartFile> files = null;
        if (file != null) {
            files = List.of(file);
        }
        return permitRequestService.sendToPayPermitRequest(permitRequestDTO, files);
    }

    @GetMapping("/getAttachment/{attachmentId}")
    public ResponseEntity<?> getAttachment(@PathVariable Long attachmentId, HttpServletRequest request) {
        Optional<Attachment> attachmentById = attachmentService.getAttachmentById(attachmentId);
        if (attachmentById.isEmpty()) {
            return new ResponseEntity<>("marine.error.attachmentError", HttpStatus.NOT_FOUND);
        }
        Attachment attachment = attachmentById.get();
        Resource resource;

        try {
            resource = fileDownloadUtil.getFileAsResourceWithPath(attachment.getDocUrl(), attachment.getDocName());
        } catch (Exception e) {
            return new ResponseEntity<>("marine.error.attachmentError", HttpStatus.NOT_FOUND);
        }
        String contentType;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            return new ResponseEntity<>("marine.error.attachmentError", HttpStatus.NOT_FOUND);
        }
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        return ResponseEntity
            .ok()
            .contentType(MediaType.parseMediaType(contentType))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
            .body(resource);
    }

    @GetMapping("/getAttachmentVisitFile")
    public ResponseEntity<?> getAttachmentVisitFile(@RequestParam Long permitVisit, HttpServletRequest request) {
        Resource resource = permitRequestService.getAttachmentVisitFile(permitVisit);
        if (resource == null) {
            return new ResponseEntity<>("File not found", HttpStatus.NOT_FOUND);
        }
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            log.info("Could not determine file type.");
        }
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        return ResponseEntity
            .ok()
            .contentType(MediaType.parseMediaType(contentType))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
            .body(resource);
    }

    @PostMapping("/licensingOfficerSubmit/{needVisit}")
    public ResponseEntity<Boolean> licensingOfficerSubmit(
        @PathVariable("needVisit") Boolean needVisit,
        @RequestBody PermitRequestDTO permitRequestDTO
    ) {
        return permitRequestService.licensingOfficerSubmit(needVisit, permitRequestDTO);
    }

    @PostMapping("/sendToInspectionManager")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_INSPECTION_OFFICER', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Boolean> sendToInspectionManager(
        @RequestParam("jsonObject") String jsonOrderVM,
        @RequestParam("visitDate") String visitDate,
        @RequestParam(value = "documents") MultipartFile[] file
    ) throws ParseException {
        PermitRequestDTO permitRequestDTO = (PermitRequestDTO) JsonUtils.parseToObject(jsonOrderVM, PermitRequestDTO.class);
        Date date = null;
        if (visitDate != null && !visitDate.equalsIgnoreCase("null") && !visitDate.equalsIgnoreCase("undefined")) {
            LocalDateTime localDateTime = DateTimeParser.stringToLocalDateTime(visitDate.substring(1, visitDate.length() - 1));
            date = Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
        }
        List<MultipartFile> files = null;
        if (file != null) {
            files = List.of(file);
        }
        return permitRequestService.sendToInspectionManager(permitRequestDTO, files);
    }

    @PostMapping("/editVisitReport")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_INSPECTION_OFFICER')")
    public ResponseEntity<Boolean> editVisitReportAndResendToInspectionManager(
        @RequestParam("jsonObject") String jsonOrderVM,
        @RequestParam(value = "documents") MultipartFile[] file
    ) throws ParseException {
        PermitRequestDTO permitRequestDTO = (PermitRequestDTO) JsonUtils.parseToObject(jsonOrderVM, PermitRequestDTO.class);
        List<MultipartFile> files = null;
        if (file != null) {
            files = List.of(file);
        }
        return permitRequestService.editVisitReportAndResendToInspectionManager(permitRequestDTO, files);
    }

    @PostMapping("/scheduleVisit")
    @PreAuthorize("hasAnyAuthority('ROLE_SRSA_INSPECTION_OFFICER')")
    public ResponseEntity<Boolean> scheduleVisit(
        @RequestParam("jsonObject") String jsonOrderVM,
        @RequestParam("visitDate") String visitDate
    ) throws ParseException {
        PermitRequestDTO permitRequestDTO = (PermitRequestDTO) JsonUtils.parseToObject(jsonOrderVM, PermitRequestDTO.class);
        LocalDateTime localDateTime = DateTimeParser.stringToLocalDateTime(visitDate.substring(1, visitDate.length() - 1));
        return permitRequestService.scheduleVisit(permitRequestDTO, localDateTime);
    }

    @PostMapping("/getPermitVisit")
    public List<PermitVisit> getPermitVisit(@RequestBody PermitRequest permitRequest) {
        return permitRequestService.getPermitVisit(permitRequest);
    }

    @GetMapping("/getPermitChanges/{requestNumber}")
    public ResponseEntity<List<PermitChanges>> getPermitChanges(@PathVariable("requestNumber") String requestNumber) {
        List<PermitChanges> permitChanges = permitRequestService.getPermitChanges(requestNumber);
        return ResponseEntity.ok().body(permitChanges);
    }

    @GetMapping("/beachActivities")
    public ResponseEntity<List<BeachActivities>> getBeachActivities() {
        List<BeachActivities> beachActivities = beachActivitiesRepository.findAll();
        return ResponseEntity.ok().body(beachActivities);
    }

    @GetMapping("/beachEquipment")
    public ResponseEntity<List<BeachEquipment>> getBeachEquipment() {
        List<BeachEquipment> beachEquipments = beachEquipmentRepository.findAll();
        return ResponseEntity.ok().body(beachEquipments);
    }

    @GetMapping("/SP/hasYachtRentalFacility")
    public Boolean hasYachtRentalFacility() {
        return this.permitRequestService.srHasYachtRentalFacility();
    }
}
