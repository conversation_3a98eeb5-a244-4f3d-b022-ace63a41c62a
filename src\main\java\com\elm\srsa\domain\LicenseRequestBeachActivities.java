package com.elm.srsa.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

/**
 * A LicenseRequestMaritimeMedium.
 */
@Entity
@Table(name = "license_request_beach_activities")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Where(clause = "record_activity = true")
@SQLDelete(sql = "UPDATE  license_request_beach_activities SET record_activity = 0 WHERE id = ?")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class LicenseRequestBeachActivities extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    //    @Column(name = "other_value")
    //    private String otherValue;

    @ManyToOne(fetch = FetchType.LAZY)
    //    @JsonIgnoreProperties(
    //        value = {
    //            "marinaLocation",
    //            "maritimeTourismAgent",
    //            "licenseRequestMaritimeMediums",
    //            "licenseRequestProvidedLanguages",
    //            "licenseRequestMaritimeServices",
    //            "licenseRequestMarineTourServices",
    //            "licenseRequestCountries",
    //            "licenseRequestFuelTypes",
    //        },
    //        allowSetters = true
    //    )
    private LicenseRequest licenseRequest;

    @ManyToOne(fetch = FetchType.EAGER)
    private BeachActivities beachActivities;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    // Constructors
    public LicenseRequestBeachActivities() {}

    public LicenseRequestBeachActivities(LicenseRequest licenseRequest, BeachActivities beachActivities) {
        this.licenseRequest = licenseRequest;
        this.beachActivities = beachActivities;
    }

    //    public String getOtherValue() {
    //        return otherValue;
    //    }
    //
    //    public void setOtherValue(String otherValue) {
    //        this.otherValue = otherValue;
    //    }

    public Long getId() {
        return this.id;
    }

    public LicenseRequestBeachActivities id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LicenseRequest getLicenseRequest() {
        return this.licenseRequest;
    }

    public void setLicenseRequest(LicenseRequest licenseRequest) {
        this.licenseRequest = licenseRequest;
    }

    public LicenseRequestBeachActivities licenseRequest(LicenseRequest licenseRequest) {
        this.setLicenseRequest(licenseRequest);
        return this;
    }

    public BeachActivities getBeachActivities() {
        return beachActivities;
    }

    public void setBeachActivities(BeachActivities beachActivities) {
        this.beachActivities = beachActivities;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LicenseRequestBeachActivities)) {
            return false;
        }
        return getId() != null && getId().equals(((LicenseRequestBeachActivities) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "LicenseRequestMaritimeMedium{" +
            "id=" + getId() +
            "}";
    }
}
