package com.elm.srsa.mofa.client.request;

import java.util.List;

public class TourismVisaRequestDTO {

    private List<ApplicantDTO> applicants;
    private String iban;

    // Getters and Setters
    public List<ApplicantDTO> getApplicants() {
        return applicants;
    }

    public void setApplicants(List<ApplicantDTO> applicants) {
        this.applicants = applicants;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }
}
