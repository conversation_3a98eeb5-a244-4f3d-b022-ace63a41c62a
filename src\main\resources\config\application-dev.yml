# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.elm.srsa: DEBUG

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  quartz:
    enabled: true
    properties:
      org.quartz.jobStore.selectWithLockSQL: SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ***************************************************************
    username: sa
    password: Aa123456
    hikari:
      poolName: Hikari
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    database-platform: org.hibernate.dialect.SQLServer2012Dialect
    database: SQL_SERVER
    show-sql: true
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: true
      hibernate.jdbc.time_zone: Asia/Riyadh
      hibernate.hbm2ddl.auto: none
      hibernate.show_sql: false
      hibernate.format_sql: false
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev
  mail:
    host: localhost
    port: 25
    username:
    password:
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false

management:
  endpoint:
    quartz:
      enabled: false

server:
  port: 8080

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  cache: # Cache configuration
    ehcache: # Ehcache configuration
      time-to-live-seconds: 3600 # By default objects stay 1 hour in the cache
      max-entries: 100 # Number of objects in each cache entry
  # CORS is only enabled by default with the "dev" profile
  cors:
    # Allow Ionic for JHipster by default (* no longer allowed in Spring Boot 2.4+)
    allowed-origins: 'http://localhost:8100,https://localhost:8100,http://localhost:9000,https://localhost:9000,http://localhost:4200,https://localhost:4200'
    # Enable CORS when running in GitHub Codespaces
    allowed-origin-patterns: 'https://*.githubpreview.dev'
    allowed-methods: '*'
    allowed-headers: '*'
    exposed-headers: 'Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params'
    allow-credentials: true
    max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: AJ8A0OWUWuwrIaCUVOdsas15Y6pJgZu7CEJDsrF/JMrh892UXknEZvzeC6FevTTWlhjF00/OmvGJfq7uzX15cZqscDPidIuQyhh9y9WgF0aj1oPtgUcgtKchMeSIbhalQGUGcv/dBGtlbKt5Z4zoYyG8r0coLQheU+hzQcJF9ExozbLHsiQB0IcOM1l9ZlGFOWazhIeh+a8raSFSBkMQQ4Ti84UTEyXzNhWQa1jHrjztB5FrflKBj0673UFlWfx66vRefEUp715n8PrunSNVxtYnotgt1t69XYvK0tWxPKAQnk6yadxTm4aitLWbueOE0Y915tV+j+x1h6RkHfNaG9G1zp9whlwzMmZJAoKgTm35lcG4UUfMTPVt4v0oMeAaAYkwKZeJT02IPgvl07WETLBvLd2AjqGuavDMbZxpRHUdIl5l32Qzh1+w1+bVHuSv3Vi3vRb90KN1Sq6K9j2jY4fYKpFT5NqNu/iiidv+nALNVUnKoha9qr6jkqwQOWKX+zDU6xR7fvvGIa73xr+Oqi6oEQy+26LNcKNQj7omcybtXrZuexDd0uF8qQQGKr8zDtDCYXcIQ7SwfTx/WVZ++7dvKiSbujMsyuRNv2lTGY8HI3J4li2SEe74bTbOl39IPMxn5qAZecuH2yXI22q71xHtuX86zF7muKEeG0ZQVp8=
        # Token is valid 24 hours
        token-validity-in-seconds: 1200
        token-validity-in-seconds-for-remember-me: 2592000
  mail: # specific JHipster mail property, for standard properties see MailProperties
    base-url: http://127.0.0.1:8080
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

elm:
  providers:
    filescan:
      mock:
        enabled: true
      rest:
        url: http://localhost:8282/dcc-engines-filescan/scan-file
      username: dccfilescan-username
      password: dccfilescan-password
      connection:
        timeout: 30000
      read:
        timeout: 30000

# application:
srsa:
  enableOneSession: false
  corsDomainValue: https://red-sea-authority-dev.apps.devocp4.elm.sa/,https://secure.quicktik.sa/payment/page/5DEDDB3782E4375E2061C9CBFCC8CB375359BD41A74B9DC4F6552E01/start
  proxy:
    enabled: true
    protocol: 'http'
    host: '*************'
    port: 8080
    ssl: true
    connectionTimeout: 10000
    readTimeout: 10000
  noProxy:
    connectionTimeout: 10000
    readTimeout: 10000
  capcha:
    enabled: true
    failAttempts: 1
    allowedTries: 3
  recaptcha:
    responseParameter: g-recaptcha-response
    verificationUrl: https://www.google.com/recaptcha/api/siteverify
    secretKey: 6LfbEq8pAAAAALWqEqInr3Q_ahiRh7PKJ1F6Eee_
    siteKey: 6LfbEq8pAAAAALxjNdFS5Rold51q4kc-ONSX3B1R
  scheduler:
    enabled: true
  yakeen:
    enabled: true
    mocked: true
    appId: '6a8020cb'
    appKey: '4a5cf5aa0d113fbbe560ec714a043e67'
    usageCode: 'USC80006'
    authServiceUrl: 'https://yakeencore.api.elm.sa/api/v2/yakeen/login'
    addressUrl: 'https://yakeencore.api.elm.sa/api/v1/yakeen/data'
    username: 'RED_SEA_AUTHORITY_TRIAL_YAK'
    password: 'MF04beIaW56e8arGWkln'
    tokenValidityInHours: 20
    authServiceCronJob:
      enabled: true
      expression: '00 00 0/12 * * ?'
    dataServiceUrl: 'https://yakeencore.api.elm.sa/api/v1/yakeen/data'
    addressServiceIdentifier: 'fe70a3e8-73c3-4a63-91e0-2b3ba36a3660'
    citizenInfoServiceId: '82d24da8-04b2-490f-b636-aa09c06b3b52'
    gccNinServiceId: '532500ab-1938-4f93-b5d8-eab8164fb11c'
    alienInfoByIqamaServiceId: 'e85f37f7-8434-4d64-88f1-ea0680a91a5d'
    borderNoServiceId: '223a9e74-36c4-4a15-b887-40b1d314a92e'
    convertDate:
      enabled: false
      serviceId: '916d4583-114b-467e-b768-3f2d3ba38418'
  notification:
    baseUrl: 'https://elm-notification-dev-api-staging.apps.devocp4.elm.sa:443'
    appKey: '804ce3cc5e37e1f6f4f6fbd5f05c5120'
    appId: '54fbd2c8'
    clientKey: '266092C6-60B4-4EB6-B37C-BD38B4BAC5DC'
    productCode: '023'
    senderName: 'Tasreeh'
    tourismHarasElhoddodMail: '<EMAIL>'

    sms:
      enabled: true
      mocked: true
      url: '/api/notification/message'
      deliveryRequired: 0
      expiryMessageTime: 0

    email:
      enabled: true
      mocked: true
      url: '/api/notification/email'
      from: '<EMAIL>'

  otp:
    enabled: true
    mocked: true
    mockCode: '5555'
    size: 4
    validityInMinutes: 2
    retryCount: 3

  wathq:
    enabled: true
    mocked: true
    useProxy: false
    baseUrl: 'https://rabs-onboarding-qa.apps.devocp4.elm.sa/api/commercial-registration'
    crFullInfoUrl: '/fullinfo/'
    crInfoUrl: '/info/'
    crAddressUrl: '/address/'
    crManagersUrl: '/managers/'
    crTreeUrl: '/branches/'
    crOwnersUrl: '/owners/'
    3scale:
      appId: '258b8414'
      appKey: '38ce52baf0b5cb590fc121f7fe64fa91'

  absherOtp:
    enabled: true
    mocked: true
    mockCode: '5555'
    size: 4
    validityInMinutes: 2
    retryCount: 3
    keyStorePath: 'C:/Elm/saudi_red_sea_authority.jks'
    keyStoreType: 'jks'
    keyStoreAlias: 'saudi red sea authority'
    keyStorePassword: 'SRSA@2024'
    serviceUrl: 'https://otp.absher.sa/AbsherOTPService'
    clientId: '**********-0001'
    clientAuthorization: 'BQX1VtKbR9bubypDlm/H/SBpkpJlcYdahoxTVwTwC7eoYT24VUrymK7vfi/xjNWF'
    reasonAr: 'التحقق من الجوال برقم الهوية'
    reasonEn: 'Mobile number verification by id number'
    templateId: 'RedSea-OTP-01'
    templateParamName: 'Param1'
    templateParam:
      sp:
        ar: 'تسجيل مقدم خدمة'
        en: 'Service Provider Registration'
      emp:
        ar: 'تسجيل موظف'
        en: 'Employee Registration'
      forgotPassword:
        ar: 'استرجاع كلمة المرور'
        en: 'Forgot Password'
    verificationCheckValidityInMinutes: 30
    templateDefaultLanguage: 'AR'

  fasah:
    enabled: true
    mock: false
    mockDataBase: false
    baseUrl: 'https://qapigw.tabadul.sa/tabadul/qa/api/v1/mawani/yacht/'
    username: 'RED_SEA_AUTHORITY_TRIAL_YAK'
    password: 'MF04beIaW56e8arGWkln'
    appKey: '804ce3cc5e37e1f6f4f6fbd5f05c5120'
    appId: '54fbd2c8'
    clientId: 'e31a0cd6d302f2d3678c804fc6dd7c3d'
    clientKey: '67253e36c2f2f511d16be1b749baa06e'
    productCode: '023'
    senderName: 'Tasreeh'
    accountId: 'RED_SEA'
    vessalType: 'YAC'
    serverKey: 'SJJNLLJHL2-JJGN9KGKLR-9LBGBJLHMH'
    callback:
      enabled: true
      url: 'https://red-sea-authority-dev.apps.devocp4.elm.sa/api/payment/external/notification/'
      3scaleToken: 'Shared_secret_sent_from_proxy_to_API_backend_0703ef49752f9ba4'
      verificationEnabled: true
    return:
      enabled: true
      url: 'https://red-sea-authority-dev.apps.devocp4.elm.sa/api/payment/external/result/'
  #      verificationEnabled: false

  zatca:
    enabled: true
    mocked: true
    baseurl: 'https://test-trust-api.zatca.gov.sa/test/government/v2/ship-inspection'
    clientId: 'e67a34cc33e7fc2a1508681e08666f69'
    clientSecret: '8eb52a2a2988f83f59fa077525ec3638'

  mofa:
    enabled: true
    mock: true
    baseUrl: 'https://gsn-api.mofa.gov.sa'
    tokenUrl: '/api/auth/connect/token'
    issueVisaUrl: '/api/tga/visas'
    submitVisaUrl: '/api/tga/tourism-requests'
    arabicCountryUrl: '/api/tga/arabic-countries'
    countryUrl: '/api/tga/countries'
    clientSecret: 'secret'
    scope: 'redseaScope'
    grantType: 'client_credentials'
    audience: 'mofa_identity_takamol_api'
    clientId: '45548558485'
    iban: '*********************'

  tga:
    enabled: true
    mocked: true
    renewValidDays: 5
    baseurl: 'http://************:90/API.Gateway/gateway/'
    systemName: 'RedSea'
    serviceName: 'Marines'
    secretToken: 'FB75EB30-0DA8-43EE-99C1-B44FB45F6522'
    callback:
      enabled: true
      url: 'http://localhost:8080/api/navigation-permit/external/tga/update-request/'
      3scaleToken: 'Shared_secret_sent_from_proxy_to_API_backend_0703ef49752f9ba4'
    requestParameter:
      systemName: 'RedSeaSystem'
      accountId: 'c3ba86d0-be9a-40fb-a738-da3ae9bbdd3f'
      userId: '********-0000-0000-0000-********0000'
      createdBy: '********-0000-0000-0000-********0000'
      lockedByAccount: '********-0000-0000-0000-********0000'
      requestId: '********-0000-0000-0000-********0000'
      attachmentStatusId: '522608ee-84d6-4457-4e5c-08d98cb13cae'
      sequence: 1
      saudiRequestType: 112
      foreignRequestType: 122

  generalConfig:
    cspConfig: script-src 'self' https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js http://* https://www.gstatic.com https://www.google.com https://kit.fontawesome.com https://* https://code.jquery.com https://cdn.jsdelivr.net https://stackpath.bootstrapcdn.com https://ka-p.fontawesome.com 'sha256-AbCdEf123...' 'sha256-XyZ456...'; style-src 'self' https://maxcdn.bootstrapcdn.com https://fonts.googleapis.com https://stackpath.bootstrapcdn.com; font-src 'self' https://fonts.gstatic.com https://ka-p.fontawesome.com; img-src 'self' data:; object-src 'none'; base-uri 'self'; frame-ancestors 'none'
    rsaDomain: rsa
    googleMapApiKey: 'AIzaSyCBQbcD9jDt17OoC3bIpMcAGnYh5ZFwb68'
    allowConnectivityTest: false

  documentAttachmentConfiguration:
    permitRequestAttachmentsPath: C:\\srsa\\attachment\\permitRequest
    userAttachmentsPath: C:\\srsa\\attachment\\userAttachment
    companyAttachmentsPath: C:\\srsa\\attachment\\companyAttachment
    marineAttachmentsPath: C:\\srsa\\attachment\\marineMedium
    mtaDelegationAttachmentsPath: C:\\srsa\\attachment\\mtaDelegation
    arrDepRequestAttachmentsPath: C:\\srsa\\attachment\\arrDepRequest
    navigationPermitAttachmentsPath: C:\\srsa\\attachment\\navigationPermit
    transitPermitAttachmentsPath: C:\\srsa\\attachment\\transitPermit
    baseAttachmentsPath: C:\\srsa\\attachment\\

  Cookie:
    secure: false
    httpOnly: true
    sameSite: strict

  vatRate: 00
  elmFee:
    vatRate: 15

  htmlToPdf:
    path: C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf
    pdfParams: --enable-local-file-access --page-size A4 -T 0mm -B 0mm -L 0 -R 0

  registration:
    idNumber:
      acceptedNumberOfDaysBeforeExpiry: 90
    crNumber:
      acceptedNumberOfDaysBeforeExpiry: 90
    nationalAddress:
      #accepted values validate, useDefault
      action: 'validate'
      default:
        region: 'الرياض'
        city: 'الرياض'
        districtId: 12
        district: 'السليمانية'
        street: 'شارع تركي السديري'
        buildingNumber: 1234
        postalCode: 11125
        additionalNumber: 1234
        unitNumber: 33

  payment:
    gracePeriodInHours: 4
    cronJob:
      enabled: true
      expression: '0 0/30 * * * ?'

  quickTik:
    enabled: true
    mocked: true
    useProxy: false
    baseUrl: 'https://secure.quicktik.sa'
    createInvoice:
      enabled: true
      url: '/payment/new/invoice'
      validityPeriodInDays: 7
      cronJob:
        enabled: true
        expression: '0 0/30 * * * ?'
      tranType: 'sale'
      tranClass: 'ecom'
      cartCurrency: 'SAR'
      payPageLang: 'ar'
      hideShipping: true
      tokenise: 2
      showSaveCard: true
      customerCountry: 'SA'
      customerNumberPrefix: 'SRSA-'
      invoiceNumberPrefix: 'SRSA-INV-'
    invoiceStatus:
      enabled: true
      url: '/payment/invoice/status'
    cancelInvoice:
      enabled: true
      url: '/payment/invoice/cancel'
    downloadInvoicePdf:
      enabled: true
      url: '/payment/invoice/$invoice_id$/download/pdf'
      directory: /opt/docs/
      parameter1: '$invoice_id$'
    return:
      enabled: true
      url: 'http://localhost:8080/api/payment/external/result/'
      verificationEnabled: false
    paymentStatus:
      url: 'http://localhost:9000/payment/$paymentRequestUuid$/payment-request-status'
      parameter1: '$paymentRequestUuid$'
    callback:
      enabled: true
      url: 'http://localhost:8080/api/payment/external/notification/'
      3scaleToken: 'SJJNLLJHL2-JJGN9KGKLR-9LBGBJLHMH'
      verificationEnabled: false
    serverKey: 'SJJNLLJHL2-JJGN9KGKLR-9LBGBJLHMH'
    clientKey: 'C9KMDM-76MQ66-TQPKTK-KRKQPM'
    profileId: '47508'
  sadad:
    billerNameAr: هيئة البحر الأحمر
    billerNameEn: Red Sea Authority
    billerNumber: 387

  discoveryContentOnlyEnabled: false

  user:
    removeInactive:
      periodInDays: 3
      cronJob:
        enabled: true
        expression: '0 0/2 * * * ?'

  mtaDelegationExpiration:
    cronJob:
      enabled: true
      expression: '0 0 0 * * ?'

  tourismPermitExpiration:
    cronJob:
      enabled: true
      expression: '0 0 0 * * ?'

  rate-limit:
    capacity: 10 # requests
    duration-in-sec: 60 # seconds

