package com.elm.srsa.domain.visa;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "visa_medical_history")
@Getter
@Setter
public class VisaMedicalHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne
    @JoinColumn(name = "visa_applicant_id")
    private VisaApplicant visaApplicant;

    private Boolean hadAccidentCausingDisability;
    private Boolean hasCurrentHospitalAdmission;
    private Boolean hasCongenitalCondition;
}
