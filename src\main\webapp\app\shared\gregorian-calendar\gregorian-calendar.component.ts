import {
  AfterViewInit,
  Component,
  Input,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  forwardRef,
  NgZone,
  Output,
  EventEmitter,
  OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
declare var $: any;

export const GREGORIAN_CALENDAR_VALUE_ACCESSOR: any = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => GregorianCalendarComponent),
  multi: true,
};

@Component({
  selector: 'gregorian-calendar',
  standalone: true,
  templateUrl: './gregorian-calendar.component.html',
  styleUrls: ['./gregorian-calendar.component.css'],
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  encapsulation: ViewEncapsulation.None,
  providers: [GREGORIAN_CALENDAR_VALUE_ACCESSOR],
})
export class GregorianCalendarComponent implements AfterViewInit, OnInit, OnChanges {
  // todo: remove this.

  @ViewChild('hc') hc?: { nativeElement: any };
  @ViewChild('hcIcon') hcIcon?: { nativeElement: any };

  @Input() yearRange = '1930:2040';

  @Input() minDate = '1930-01-01';

  @Input() maxDate = '2040-12-31';

  @Input() showIcon = true;

  @Input() name: any;

  @Input() styleClass: string = '';

  @Input() placeholder = '';

  @Input() width = '260';

  @Input() hasError = false;

  @Input() dateFormat = 'yyyy-mm-dd';

  @Input() maxDateAsToday = false;

  @Input() minDateAsToday = false;

  @Input() disableDate = false;

  @Input() value: any = '';

  @Output() gregCalendar: EventEmitter<any> = new EventEmitter<any>();

  onModelChange: Function = () => {};

  onModelTouched: Function = () => {};

  constructor(
    private zone: NgZone,
    private translateService: TranslateService,
  ) {}

  ar: boolean = true;

  ngOnInit() {}

  ngOnChanges() {
    this.destroyDatePicker();
    this.initDatePicker();
  }

  ngAfterViewInit() {
    this.initDatePicker();
  }

  checkCurrentLang() {
    if (this.translateService.currentLang === 'en') {
      this.ar = false;
    } else {
      this.ar = true;
    }
  }

  getCalendarOptions(isIcon: boolean) {
    this.checkCurrentLang();
    return {
      calendar: this.ar ? $.calendars.instance('gregorian', 'ar-EG') : $.calendars.instance('gregorian', 'en'),
      showAnim: 'fadeIn',
      showSpeed: 'fast',
      yearRange: this.yearRange,
      showOtherMonths: true,
      dateFormat: this.dateFormat,
      minDate: this.minDateAsToday ? this.getTodayDate() : this.minDate,
      maxDate: this.maxDateAsToday ? this.getTodayDate() : this.maxDate,
      alignment: isIcon ? 'bottom' : 'bottomRight',
      closeText: this.ar ? 'اغلاق' : 'Close',
      clearText: this.ar ? 'مسح' : 'Clear',
      nextText: this.ar ? 'التالي' : 'Next',
      prevText: this.ar ? 'السابق' : 'Prev',
      todayText: this.ar ? 'اليوم' : 'Today',
      onSelect: (selectedDate: string | any[]) => {
        if (selectedDate.length > 0) {
          const formattedDate = selectedDate[0].formatDate(this.dateFormat);
          this.value = formattedDate;
        } else {
          this.value = '';
        }
        this.onModelChange(this.value);
        this.gregCalendar.emit(this.value);
      },
    };
  }

  destroyDatePicker() {
    if (this.showIcon && this.hcIcon !== undefined) {
      $(this.hcIcon.nativeElement).calendarsPicker('destroy');
    }
    if (this.hc !== undefined) {
      $(this.hc.nativeElement).calendarsPicker('destroy');
    }
  }

  initDatePicker() {
    // const calendarOptions = this.getCalendarOptions();
    if (this.showIcon && this.hcIcon !== undefined) {
      $(this.hcIcon.nativeElement).calendarsPicker(this.getCalendarOptions(true));
      let calIcon = '<i class="fa fa-calendar-alt" aria-hidden="true"></i>';
      if (!$(this.hcIcon.nativeElement).has('i').length) {
        $(this.hcIcon.nativeElement).append(calIcon);
      }
    }
    if (this.hc !== undefined) {
      $(this.hc.nativeElement).calendarsPicker(this.getCalendarOptions(false));
    }
  }

  getTodayDate() {
    return $.calendars.instance().today().formatDate(this.dateFormat);
  }

  writeValue(value: any): void {
    if (value === undefined || value === null || String(value).length < 1) {
      this.value = '';
    } else {
      this.value = value;
    }
  }

  registerOnChange(fn: Function): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: Function): void {
    this.onModelTouched = fn;
  }
}
