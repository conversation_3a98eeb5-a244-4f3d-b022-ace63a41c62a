package com.elm.srsa.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

/**
 * A LicenseRequestMaritimeMedium.
 */
@Entity
@Table(name = "license_request_beach_equipment")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Where(clause = "record_activity = true")
@SQLDelete(sql = "UPDATE  license_request_beach_equipment SET record_activity = 0 WHERE id = ?")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class LicenseRequestBeachEquipments extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    //    @Column(name = "other_value")
    //    private String otherValue;

    @ManyToOne(fetch = FetchType.LAZY)
    //    @JsonIgnoreProperties(
    //        value = {
    //            "marinaLocation",
    //            "maritimeTourismAgent",
    //            "licenseRequestMaritimeMediums",
    //            "licenseRequestProvidedLanguages",
    //            "licenseRequestMaritimeServices",
    //            "licenseRequestMarineTourServices",
    //            "licenseRequestCountries",
    //            "licenseRequestFuelTypes",
    //        },
    //        allowSetters = true
    //    )
    private LicenseRequest licenseRequest;

    @ManyToOne(fetch = FetchType.EAGER)
    private BeachEquipment beachEquipment;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public LicenseRequestBeachEquipments() {}

    public LicenseRequestBeachEquipments(LicenseRequest licenseRequest, BeachEquipment beachEquipment) {
        this.licenseRequest = licenseRequest;
        this.beachEquipment = beachEquipment;
    }

    //    public String getOtherValue() {
    //        return otherValue;
    //    }
    //
    //    public void setOtherValue(String otherValue) {
    //        this.otherValue = otherValue;
    //    }

    public Long getId() {
        return this.id;
    }

    public LicenseRequestBeachEquipments id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LicenseRequest getLicenseRequest() {
        return this.licenseRequest;
    }

    public void setLicenseRequest(LicenseRequest licenseRequest) {
        this.licenseRequest = licenseRequest;
    }

    public LicenseRequestBeachEquipments licenseRequest(LicenseRequest licenseRequest) {
        this.setLicenseRequest(licenseRequest);
        return this;
    }

    public BeachEquipment getBeachEquipment() {
        return beachEquipment;
    }

    public void setBeachEquipment(BeachEquipment beachEquipment) {
        this.beachEquipment = beachEquipment;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LicenseRequestBeachEquipments)) {
            return false;
        }
        return getId() != null && getId().equals(((LicenseRequestBeachEquipments) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "LicenseRequestMaritimeMedium{" +
            "id=" + getId() +
            "}";
    }
}
