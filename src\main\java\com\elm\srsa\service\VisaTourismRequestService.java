package com.elm.srsa.service;

import com.elm.srsa.domain.ServiceType;
import com.elm.srsa.domain.visa.VisaApplicant;
import com.elm.srsa.domain.visa.VisaApplicantPreviousTravel;
import com.elm.srsa.domain.visa.VisaRequest;
import com.elm.srsa.domain.visa.VisaTourismRequestAttachment;
import com.elm.srsa.domain.visa.enumerations.*;
import com.elm.srsa.mofa.client.request.*;
import com.elm.srsa.mofa.client.response.VisaResponse;
import com.elm.srsa.mofa.client.response.VisaTourismApplication;
import com.elm.srsa.mofa.client.service.MofaApiClientService;
import com.elm.srsa.repository.ServiceTypeRepository;
import com.elm.srsa.repository.visa.VisaRequestRepository;
import com.elm.srsa.service.dto.VisaRequestDTO;
import com.elm.srsa.service.dto.VisaRequestSummaryDTO;
import com.elm.srsa.service.mapper.VisaRequestMapper;
import com.elm.srsa.web.rest.errors.BadRequestAlertException;
import com.elm.srsa.web.rest.request.VisaRequestSearchCriteria;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
public class VisaTourismRequestService {

    private static final String ENTITY_NAME = "VisaTourismRequestService";
    private final ServiceTypeRepository serviceTypeRepository;
    private final MofaApiClientService mofaApiClientService;
    private final VisaRequestRepository visaRequestRepository;
    private final VisaRequestMapper visaRequestMapper;
    private final VisaTourismRequestAttachmentService visaTourismRequestAttachmentService;

    public VisaTourismRequestService(
        ServiceTypeRepository serviceTypeRepository,
        MofaApiClientService mofaApiClientService,
        VisaRequestRepository visaRequestRepository,
        VisaRequestMapper visaRequestMapper,
        VisaTourismRequestAttachmentService visaTourismRequestAttachmentService
    ) {
        this.serviceTypeRepository = serviceTypeRepository;
        this.mofaApiClientService = mofaApiClientService;
        this.visaRequestRepository = visaRequestRepository;
        this.visaRequestMapper = visaRequestMapper;
        this.visaTourismRequestAttachmentService = visaTourismRequestAttachmentService;
    }

    private TourismVisaRequestDTO prepareVisaMofaRequest(List<VisaApplicant> applicants) {
        TourismVisaRequestDTO tourismVisaRequestDTO = new TourismVisaRequestDTO();
        List<ApplicantDTO> mofaApplicants = new ArrayList<>();
        tourismVisaRequestDTO.setApplicants(mofaApplicants);
        for (VisaApplicant visaApplicant : applicants) {
            ApplicantDTO mofaApplicantDTO = new ApplicantDTO();
            mofaApplicantDTO.setAddress(visaApplicant.getAddress());
            mofaApplicantDTO.setBirthCountry(visaApplicant.getBirthCountry());
            mofaApplicantDTO.setEmail(visaApplicant.getEmail());
            mofaApplicantDTO.setBirthPlace(visaApplicant.getBirthCountry());
            mofaApplicantDTO.setApplicationNumber(visaApplicant.getPassportNumber());
            mofaApplicantDTO.setVisaValidity(visaApplicant.getVisaDurationDays());
            mofaApplicantDTO.setDoYouWantDoUmra(visaApplicant.getWantsUmrah());
            mofaApplicantDTO.setBirthDate(visaApplicant.getBirthDate().toString());
            mofaApplicantDTO.setEntryType(visaApplicant.getNumberOfEntries().name().equalsIgnoreCase(EntryType.SINGLE.name()) ? 1 : 2);
            mofaApplicantDTO.setExpectedEntryDate(visaApplicant.getExpectedEntryDate().toString());
            mofaApplicantDTO.setFamilyNameAr(visaApplicant.getFamilyNameAr());
            mofaApplicantDTO.setFamilyNameEn(visaApplicant.getFamilyNameEn());
            mofaApplicantDTO.setFatherNameAr(visaApplicant.getFatherNameAr());
            mofaApplicantDTO.setFatherNameEn(visaApplicant.getFatherNameEn());
            mofaApplicantDTO.setFirstNameAr(visaApplicant.getFirstNameAr());
            mofaApplicantDTO.setFirstNameEn(visaApplicant.getFirstNameEn());
            mofaApplicantDTO.setGender(visaApplicant.getGender().name().equalsIgnoreCase(Gender.MALE.name()) ? 1 : 2);
            mofaApplicantDTO.setGrandFatherNameAr(visaApplicant.getGrandFatherNameAr());
            mofaApplicantDTO.setGrandFatherNameEn(visaApplicant.getGrandFatherNameEn());
            // InsuranceQuestionsDTO
            InsuranceQuestionsDTO insuranceQuestionsDTO = new InsuranceQuestionsDTO();
            insuranceQuestionsDTO.setAreYouDisabledByAccident(visaApplicant.getHasDisability());
            insuranceQuestionsDTO.setCurrentlyInHospitalOrTakingEmerTreatments(visaApplicant.isCurrentlyInHospitalOrTakingEmerTreatments());
            insuranceQuestionsDTO.setYouHaveWeaknessOrCongenitalPlatformation(visaApplicant.isYouHaveWeaknessOrCongenitalPlatformation());
            insuranceQuestionsDTO.setAreYouPregnant(visaApplicant.isAreYouPregnant());
            insuranceQuestionsDTO.setHowManyMonthsOfPregnancy(visaApplicant.getHowManyMonthsOfPregnancy());
            insuranceQuestionsDTO.setNoramalPregnancy(visaApplicant.isNoramalPregnancy());
            mofaApplicantDTO.setInsuranceQuestions(insuranceQuestionsDTO);

            mofaApplicantDTO.setIssuePassportCountry(visaApplicant.getPassportCountry());
            mofaApplicantDTO.setJob(visaApplicant.getJobTitle());
            switch (visaApplicant.getMaritalStatus()) {
                case SINGLE:
                    mofaApplicantDTO.setMaritalstatus(2);
                    break;
                case MARRIED:
                    mofaApplicantDTO.setMaritalstatus(1);
                    break;
                case DIVORCED:
                    mofaApplicantDTO.setMaritalstatus(3);
                    break;
                case WIDOWED:
                    mofaApplicantDTO.setMaritalstatus(4);
                    break;
                default:
                    mofaApplicantDTO.setMaritalstatus(5);
                    break;
            }

            mofaApplicantDTO.setMobileNo(visaApplicant.getPhoneNumber());
            mofaApplicantDTO.setNationality(visaApplicant.getNationality());
            mofaApplicantDTO.setNavigationalLicenseNumber(visaApplicant.getNavigationalLicenseNumber());

            mofaApplicantDTO.setPassportExpiryDate(visaApplicant.getPassportExpiryDate().toString());
            mofaApplicantDTO.setPassportIssueDate(visaApplicant.getPassportIssueDate().toString());
            mofaApplicantDTO.setPassportNo(visaApplicant.getPassportNumber());

            switch (visaApplicant.getPassportType()) {
                case DIPLOMATIC:
                    mofaApplicantDTO.setPassportType(1);
                    break;
                case SPECIAL:
                    mofaApplicantDTO.setMaritalstatus(2);
                    break;
                case NORMAL:
                    mofaApplicantDTO.setMaritalstatus(3);
                    break;
            }

            //          mofaApplicantDTO.setPersonalImage(visaApplicant.getP);

            mofaApplicantDTO.setPrevNationality(visaApplicant.getPrevNationality());
            if (visaApplicant.getPreviousTravels() != null) {
                List<TravelHistoryDTO> travelHistoryDTOS = new ArrayList<>();
                for (VisaApplicantPreviousTravel visaApplicantPreviousTravel : visaApplicant.getPreviousTravels()) {
                    TravelHistoryDTO travelHistoryDTO = new TravelHistoryDTO();
                    travelHistoryDTO.setCountryCode(visaApplicantPreviousTravel.getCountry());
                    travelHistoryDTO.setPurpose(visaApplicantPreviousTravel.getPurpose());
                    travelHistoryDTO.setFromDate(visaApplicantPreviousTravel.getFromDate().toString());
                    travelHistoryDTO.setToDate(visaApplicantPreviousTravel.getToDate().toString());
                    travelHistoryDTOS.add(travelHistoryDTO);
                }
                mofaApplicantDTO.setPrevTravels(travelHistoryDTOS);
            }

            mofaApplicantDTO.setReligion(visaApplicant.getReligion().name().equalsIgnoreCase(Religion.MUSLIM.name()) ? 1 : 2);
            mofaApplicantDTO.setResidenceCountry(visaApplicant.getResidenceCountry());
            mofaApplicantDTO.setGuardianPassportNumber(visaApplicant.getGuardianPassportNnumber());

            // Security Questions
            if (visaApplicant.getSecurityClearance() != null) {
                SecurityQuestionsDTO securityQuestions = new SecurityQuestionsDTO();
                SecurityQuestionDTO securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getLinkedToTerroristOrg());
                securityQuestions.setDoYouBelongToAnyTerroristOrganization(securityQuestionDTO);

                securityQuestions.setDeliverVaccinationCertificate(visaApplicant.getTookVaccines());
                securityQuestions.setDoesYouPassportContainsRestication(visaApplicant.getSecurityClearance().getHasTravelRestrictions());
                securityQuestions.setDoYouHaveAnyDisibility(visaApplicant.getHasDisability());
                securityQuestions.setHaveYouArrestedByInterpol(visaApplicant.getSecurityClearance().getInterpolArrest());

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getHasBeenArrestedForFinance());
                securityQuestions.setHaveYouArrestedForMoneyCases(securityQuestionDTO);

                securityQuestions.setHaveYouDeportedFromKSAOrOtherCountry(visaApplicant.getSecurityClearance().getDeported());

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getSentencedInHomeCountry());
                securityQuestions.setHaveYouJudjedInYourCountry(securityQuestionDTO);

                securityQuestions.setHaveYouTokeVaccinations(visaApplicant.getTookVaccines());

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getWorkedInPoliticalOrMedia());
                securityQuestions.setHaveYouWorkedInMediaOrPoliticalField(securityQuestionDTO);

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getServedInMilitary());
                securityQuestions.setHaveYouServedInArmyForces(securityQuestionDTO);
                mofaApplicantDTO.setSecurityQuestions(securityQuestions);
            }
        }
        return tourismVisaRequestDTO;
    }

    private VisaTourismApplication applyVisaOnMofa(VisaRequest visaRequest) {
        if (visaRequest != null && visaRequest.getApplicants() != null) {
            TourismVisaRequestDTO tourismVisaRequestDTO = prepareVisaMofaRequest(visaRequest.getApplicants());
            VisaTourismApplication visaTourismApplication = mofaApiClientService.submitTourismVisaRequest(tourismVisaRequestDTO);
            return visaTourismApplication;
        }
        return null;
    }

    private VisaResponse issueVisaOnMofa(VisaRequest visaRequest, VisaTourismApplication visaTourismApplication) {
        if (visaTourismApplication != null && visaTourismApplication.getApplicants() != null) {
            IssueVisaRequest issueVisaRequest = new IssueVisaRequest();
            List<VisaRequestApplicant> visaRequestApplicants = new ArrayList<>();
            for (ApplicantDTO applicantDTO : visaTourismApplication.getApplicants()) {
                VisaRequestApplicant visaRequestApplicant = new VisaRequestApplicant();
                visaRequestApplicant.setApplicantNumber(applicantDTO.getApplicationNumber());
                visaRequestApplicant.setPassportNumber(applicantDTO.getPassportNo());
                visaRequestApplicants.add(visaRequestApplicant);
            }
            issueVisaRequest.setReferenceNumber(visaTourismApplication.getApplicants().get(0).getApplicationNumber());
            issueVisaRequest.setApplicants(visaRequestApplicants);
            VisaResponse issueVisaResponse = mofaApiClientService.issueVisa(issueVisaRequest);
            return issueVisaResponse;
        }
        return null;
    }

    @Transactional
    public VisaResponse applyVisaRequest(VisaRequestDTO dto) {
        VisaRequest entity = visaRequestMapper.toEntity(dto);
        VisaTourismApplication visaTourismApplication = applyVisaOnMofa(entity);
        if (visaTourismApplication != null) {
            VisaResponse visaResponse = issueVisaOnMofa(entity, visaTourismApplication);
            return visaResponse;
        }
        return null;
    }

    @Transactional
    public VisaRequestDTO createRequest(VisaRequestDTO dto, MultipartFile passportCopy, MultipartFile personalImage) {
        calcFees(dto);
        VisaRequest entity = visaRequestMapper.toEntity(dto);
        entity.setRequestStatus(RequestStatus.DRAFT);
        if (entity.getApplicants() != null) {
            entity.getApplicants().forEach(applicant -> applicant.setVisaRequest(entity));
        }

        List<VisaTourismRequestAttachment> attachments = new ArrayList<>();
        saveAttachments(passportCopy, attachments, entity);
        saveAttachments(personalImage, attachments, entity);
        entity.setAttachments(new HashSet<>(attachments));
        VisaRequest saved = visaRequestRepository.save(entity);
        return visaRequestMapper.toDto(saved);
    }

    private void calcFees(VisaRequestDTO dto) {
        Optional<ServiceType> serviceTypeOptional = serviceTypeRepository.findByCode("TVR");
        if (serviceTypeOptional.isPresent() && !dto.getApplicants().isEmpty()) {
            BigDecimal fees = serviceTypeOptional.get().getFees().multiply(BigDecimal.valueOf(dto.getApplicants().size()));
            dto.setTotalFee(fees);
        } else {
            dto.setTotalFee(BigDecimal.ZERO);
        }
    }

    private void saveAttachments(MultipartFile multipartFile, List<VisaTourismRequestAttachment> attachments, VisaRequest visaRequest) {
        if (!Objects.equals(multipartFile.getOriginalFilename(), "no-file-h")) {
            try {
                if (multipartFile.getBytes().length >= 1) {
                    visaTourismRequestAttachmentService.saveFileAndReturnPath(
                        Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                        multipartFile.getOriginalFilename(),
                        visaRequest,
                        attachments
                    );
                }
            } catch (IOException e) {
                throw new RuntimeException("fail to save attachment in visa tourism  request : " + e.getMessage());
            }
        }
    }

    public VisaRequestDTO updateRequest(Long id, VisaRequestDTO dto, MultipartFile passportCopy, MultipartFile personalImage) {
        calcFees(dto);
        return visaRequestRepository
            .findById(id)
            .map(existing -> {
                if (existing.getRequestStatus() != RequestStatus.DRAFT) {
                    throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
                }

                existing.setVisaType(dto.getVisaType());
                existing.setTotalFee(dto.getTotalFee());
                existing.setUpdatedAt(LocalDateTime.now());

                if (dto.getApplicants() != null) {
                    List<VisaApplicant> applicants = dto
                        .getApplicants()
                        .stream()
                        .map(visaRequestMapper::toEntity)
                        .peek(app -> app.setVisaRequest(existing))
                        .toList();

                    existing.getApplicants().clear();
                    existing.getApplicants().addAll(applicants);
                }

                existing.getAttachments().clear();
                List<VisaTourismRequestAttachment> attachments = new ArrayList<>();

                if (passportCopy != null && !passportCopy.isEmpty()) {
                    saveAttachments(passportCopy, attachments, existing);
                }

                if (personalImage != null && !personalImage.isEmpty()) {
                    saveAttachments(personalImage, attachments, existing);
                }
                existing.setAttachments(new HashSet<>(attachments));
                VisaRequest updated = visaRequestRepository.save(existing);
                return visaRequestMapper.toDto(updated);
            })
            .orElseThrow(() -> new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid"));
    }

    @Transactional(readOnly = true)
    public Optional<VisaRequestDTO> getRequestWithApplicants(Long id) {
        return visaRequestRepository.findById(id).map(visaRequestMapper::toDto);
    }

    public List<VisaRequestSummaryDTO> getAllRequestSummaries() {
        List<VisaRequest> requests = visaRequestRepository.findAll();
        return visaRequestMapper.toSummaryDtoList(requests);
    }

    public Page<VisaRequestSummaryDTO> searchVisaRequests(VisaRequestSearchCriteria criteria, Pageable pageable) {
        Specification<VisaRequest> spec = Specification.where(null);

        if (criteria.getRequestNumber() != null && !criteria.getRequestNumber().isBlank()) {
            spec =
                spec.and((root, query, cb) ->
                    cb.like(cb.lower(root.get("requestNumber")), "%" + criteria.getRequestNumber().toLowerCase() + "%")
                );
        }

        if (criteria.getStatuses() != null && !criteria.getStatuses().isEmpty()) {
            spec = spec.and((root, query, cb) -> root.get("requestStatus").in(criteria.getStatuses()));
        }

        Page<VisaRequest> result = visaRequestRepository.findAll(spec, pageable);
        return result.map(visaRequestMapper::toSummaryDto);
    }
}
