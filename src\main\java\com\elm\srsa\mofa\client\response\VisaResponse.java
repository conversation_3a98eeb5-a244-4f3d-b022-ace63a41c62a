package com.elm.srsa.mofa.client.response;

import java.util.List;

public class VisaResponse {

    private List<ApplicantResponse> applicants;
    private Long referenceNumber;

    // Constructors, getters, and setters
    public VisaResponse() {}

    public VisaResponse(List<ApplicantResponse> applicants, Long referenceNumber) {
        this.applicants = applicants;
        this.referenceNumber = referenceNumber;
    }

    // Getters and Setters
    public List<ApplicantResponse> getApplicants() {
        return applicants;
    }

    public void setApplicants(List<ApplicantResponse> applicants) {
        this.applicants = applicants;
    }

    public Long getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(Long referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
}
