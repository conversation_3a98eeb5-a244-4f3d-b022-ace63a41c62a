{"global": {"lang-ar-en": "en", "lang-Label": "العربية", "empInfo": "Employee Information", "benfInfo": "Beneficiary Information", "userDate": "User Data", "rEmpInfo": "Register employee information", "selectregis": "Select the registration type", "compInfo": "Facility Information", "userDate&com": "User and facility data", "rUserInfo": "Register user information", "T&C": "Terms and Conditions", "privacy-policy": "Privacy policy", "title": "SRSA", "search": "Search", "table": {"noData": "No data available", "loading": "Loading...", "actions": "Actions", "sort": "Sort", "page": "Page", "of": "of", "items": "items", "itemsPerPage": "Items per page"}, "boolean": {"yes": "Yes", "no": "No"}, "browsehappy": "You are using an <strong>outdated</strong> browser. Please <a href=\"http://browsehappy.com/?locale=en\">upgrade your browser</a> to improve your experience.", "termsAndConditions": {"title": "The basic terms and conditions apply to your use of our website, which can be accessed through the link <a href=\"https://redsea.gov.sa/ar\"></a> The Terms and Conditions apply fully to your use of this website. By using this website, you accept all terms and conditions contained herein. You must not use this website if you do not accept any of the terms and conditions. The terms and conditions for this website have been created.", "text1": "You must be at least 18 years old to register an account.", "text2": "You are required to provide accurate, current, and complete information during the registration process.", "text3": "You must update your information promptly if it changes.", "text4": "Misrepresentation or providing false information may result in the suspension or termination of your account.", "text5": "You are solely responsible for maintaining the confidentiality of your account credentials, including your username and password.", "text6": "You agree not to share your account details with others.", "text7": "You are responsible for all activities that occur under your account.", "text8": "If you suspect unauthorized use of your account, you must notify us immediately.", "text9": "We are not liable for any loss or damage arising from unauthorized access to your account due to your failure to secure your credentials.", "text10": "You agree to use the platform in compliance with all applicable laws, regulations, and guidelines.", "text11": "Unauthorized use of the platform, including but not limited to hacking, data mining, or any activity that may compromise the security or integrity of the platform, is prohibited and may result in legal action.", "text12": "We take the protection of your personal data seriously and will handle it in accordance with our Privacy Policy.", "text13": "By registering an account, you consent to the collection, storage, and use of your personal information as described in the Privacy Policy.", "text14": "You agree to share your data with third-party service providers for the purpose of platform maintenance, customer support, or other operational needs, in compliance with applicable data protection laws.", "text15": "We reserve the right to modify these terms and conditions at any time.", "text16": "Any changes will be effective immediately upon posting on the platform.", "text17": "It is your responsibility to review the terms periodically for any updates.", "text18": "Continued use of your account after any modifications constitutes your acceptance of the revised terms.", "text19": "If you do not agree with the changes, you must discontinue using the platform and may request to close your account.", "text20": "We may terminate or suspend your account at our sole discretion, without prior notice, if we believe that you have violated these terms, engaged in unlawful activities, or for any other reason.", "text21": "Upon termination, your right to use the platform will immediately cease, and any content or data associated with your account may be deleted.", "text22": "These terms and conditions are governed by the laws of Saudi Arabia, and any disputes arising out of or relating to these terms shall be subject to the exclusive jurisdiction of the courts in Saudi Arabia.", "text23": "By registering an account, you agree to submit to the jurisdiction of these courts for any legal matters related to your account or use of the platform.", "text24": "If any provision of these terms is found to be invalid or unenforceable, the remaining provisions will remain in full force and effect.", "text25": "Our failure to enforce any right or provision in these terms does not constitute a waiver of such right or provision.", "accept": "Accept", "reviewTermsAndConditions": "Please review and agree to the terms and conditions", "reviewPrivacyPolicy": "Review Privacy policy"}, "mobileOtpModal": {"securityCheck": "Security check", "4digit": "Please enter the code sent to your mobile phone, which consists of four numbers:", "sendAgain": "Send again", "notReceived": "Not received yet", "securityCheckEmail": "Please enter the code sent to your Email, which consists of four numbers:"}, "menu": {"home": "Home", "ourVisionAndGoals": "Our vision and goals", "electronicServices": "Rlectronic services", "Regulations": "Regulations", "callUs": "Call us", "jhipster-needle-menu-add-element": "JHipster will add additional menu entries here (do not translate!)", "sideBar": {"welcome1": "Welcome", "welcome2": "on the Authority's platform", "welcome3": "Saudi Arabia of the Red Sea", "welcome4": "To enable a fruitful tourism economy through the Kingdom's Red Sea coast"}, "entities": {"main": "Entities", "jhipster-needle-menu-add-entry": "JHipster will add additional entities here (do not translate!)", "attachment": "Attachment", "attachmentType": "Attachment Type", "company": "Company", "country": "Country", "fuelTypes": "Fuel Types", "investmentType": "Investment Type", "licenseProvider": "License Provider", "licenseRequest": "License Request", "licenseRequestCountry": "License Request Country", "licenseRequestCountryHistory": "License Request Country History", "licenseRequestFuelTypes": "License Request Fuel Types", "licenseRequestFuelTypesHistory": "License Request Fuel Types History", "licenseRequestHistory": "License Request History", "licenseRequestMarineTourServices": "License Request Marine Tour Services", "licenseRequestMarineTourServicesHistory": "License Request Marine Tour Services History", "licenseRequestMaritimeMedium": "License Request Maritime Medium", "licenseRequestMaritimeMediumHistory": "License Request Maritime Medium History", "licenseRequestMaritimeServices": "License Request Maritime Services", "licenseRequestMaritimeServicesHistory": "License Request Maritime Services History", "licenseRequestProvidedLanguages": "License Request Provided Languages", "licenseRequestProvidedLanguagesHistory": "License Request Provided Languages History", "marineTourOperatorServices": "Marine Tour Operator Services", "maritimeMedium": "Maritime Medium", "maritimeTourismServices": "Maritime Tourism Services", "nationalAddress": "National Address", "permitRequest": "Permit Request", "permitRequestHistory": "Permit Request History", "permitRequestStatus": "Permit Request Status", "providedLanguages": "Provided Languages", "serviceType": "Service Type", "arrivalRequestNationality": "Arrival Request Nationality", "portCode": "Port Code", "portCountry": "Port Country", "routes": "Routes", "cargoType": "Cargo Type", "container": "Container", "passenger": "Passenger", "arrivalDepNotification": "Arrival Dep Notification", "arrivalRequest": "Arrival Request", "departureRequest": "Departure Request", "containerType": "Container Type", "arrivalDepAttachment": "Arrival Dep Attachment", "marineMedium": "Marine Medium", "techPermitAttachment": "Tech Permit Attachment", "marineAccessoriesItem": "Marine Accessories Item", "technicalPermitStatus": "Technical License (Leisure Tourism) Status", "techPermitPort": "Tech Permit Port", "techPermitNationality": "Tech Permit Nationality", "techPermitPassengers": "Tech Permit Passengers", "technicalPermit": "Technical License (Leisure Tourism)", "technicalPermitActivity": "Technical License (Leisure Tourism) Activity", "technicalPermitHistory": "Technical License (Leisure Tourism) History", "technicalPermitActivityHistory": "Technical License (Leisure Tourism) Activity History", "tourismPermitAttachment": "Tourism Permit Attachment", "tourismPermitNationality": "Tourism Permit Nationality", "tourismPermitPassengerAttachment": "Tourism Permit Passenger Attachment", "tourismPermitPassengers": "Tourism Permit Passengers", "tourismPermitMarine": "Tourism Permit Marine", "tourismPermitRegion": "Tourism Permit Region", "tourismPermit": "Tourism Permit", "tourismPermitRegionStatus": "Tourism Permit Region Status", "tourismPermitStatus": "Tourism Permit Status", "yachtCharterPermitAttachment": "Yacht Charter Permit Attachment", "yachtCharterPermitStatus": "Yacht Charter Permit Status", "yachtCharterPermitDestination": "Yacht Charter Permit Destination", "yachtCharterPermitNationality": "Yacht Charter Permit Nationality", "yachtCharterPermit": "Yacht Charter Permit", "yachtCharterPermitHistory": "Yacht Charter Permit History", "transitPermitAttachment": "Transit Permit Attachment", "transitPermitStatus": "Transit Permit Status", "transitPermitDestination": "Transit Permit Destination", "transitPermitNationality": "Transit Permit Nationality", "transitPermit": "Transit Permit", "transitPermitHistory": "Transit Permit History", "visaTourism": "Visa Tourism"}, "account": {"main": "Account", "settings": "Settings", "password": "Password", "sessions": "Sessions", "login": "Sign in", "logout": "Sign out", "register": "Register", "profile": "Profile"}, "admin": {"main": "Administration", "userManagement": "User management", "permitManagement": "Permit management", "techPermit": "Technical Permit", "visaTourism": "Tourism Visa Management", "yachtCharterPermit": "Yacht Charter Permit", "transitPermit": "Transit Permit", "tourismPermit": "Tourism Permit", "tracker": "User tracker", "metrics": "Metrics", "health": "Health", "configuration": "Configuration", "logs": "Logs", "apidocs": "API", "database": "Database", "jhipster-needle-menu-add-admin-element": "JHipster will add additional menu entries here (do not translate!)", "paymentRequests": "Payment Requests", "invoices": "Invoices"}, "language": "Language", "next": "Next", "previous": "Previous"}, "form": {"changePhoneNo": "Change phone number?", "change": "Change", "changeEmail": "Change email?", "companyName": "Facility information", "userInfo": "SRSA User Info", "individualUserInfo": "Individual Information", "placeholderMandatory": "Mandatory", "username.label": "Username", "username.placeholder": "Your username", "currentpassword.label": "Current password", "currentpassword.placeholder": "Current password", "passwordLengthMinCharacters": "Minimum password length is 8 characters", "passwordLengthMaxCharacters": "Maximum password length is 50 characters", "passwordLengthUppercaseLetters": "Must contain at least n uppercase letter [A-Z]", "passwordLengthLowercaseLetters": "Must contain at least n lowercase letter [a-z]", "passwordNumbers": "Must contain digits [0-9]", "passwordSpecialCharacters": "Must contain at least n special character [ ! , @ , $ , % , ? , & , * ]", "newpassword.label": "New password", "newpassword.placeholder": "New password", "confirmpassword.label": "New password confirmation", "confirmpassword.placeholder": "Confirm the new password", "emailConfirm.label": "Confirm Email", "email.label": "Email", "email.placeholder": "Your email", "idNumber.label": "ID number", "idNumber": "ID/Iqama number", "idNumber.placeholder": "Your ID number", "commercialRegistrationNo.label": "Commercial registration No", "mobileNumber.label": "Mobile number", "mobileNumber.placeholder": "Your Mobile number", "employeeId.label": "Employee ID", "employeeId.placeholder": "Your Employee ID", "employeeType.label": "Employee Entity", "birthDate.label": "Date of birth", "gregorian": "<PERSON><PERSON>", "hijri": "<PERSON><PERSON><PERSON>", "verify": "Verify", "Verifying": "Verifying...", "Verified": "Verified", "operationSuccessfully": "Operation accomplished successfully", "passwordPolicy": "Password Policy :", "nationalNumber": "National Number", "passportName": "Full Name as Passport", "nationality": "Nationality", "passportNumber": "Passport Number", "passportCountry": "Passport Issued Country", "male": "Male", "female": "Female", "passportExpiryDate": "Passport Expiry Date", "nationalIdImage": "National Id Image", "passportImage": "Passport Image", "organizationName": "Organization Name", "organizationReferenceNumber": "Organization Reference Number", "entityIssuingCommercialLicense": "Entity Issuing Commercial License", "businessDescription": "Business Description", "companyNationality": "Company Nationality", "commercialLicenseExpiryDate": "Commercial License Expiry Date", "commercialLicenseCertificate": "Commercial License Certificate", "relationshipWithOrganization": "Relationship With Organization", "frequentlyAskedQuestions": "Frequently asked questions", "downloadUserGuide": "Download User Guide", "yes": "Yes", "no": "No"}, "messages": {"info": {"authenticated": {"prefix": "If you want to ", "link": "sign in", "suffix": ", you can try the default accounts:<br/>- Administrator (login=\"admin\" and password=\"admin\") <br/>- User (login=\"user\" and password=\"user\")."}, "register": {"noaccount": "You don't have an account yet?", "link": "Register a new account", "selectRegistration": "Please select your registration type", "serviceProvider": "Service Provider", "beneficiary": "Service Beneficiary", "doHaveAccount": "Do you have an account?", "loginNow": " <PERSON><PERSON>"}}, "error": {"dontmatch": "The password and its confirmation do not match!", "phoneNoNeeds": "Your mobile number is required.", "phoneNoNotValid": "The mobile number is invalid. It must start with 9665.", "mobileNotValid": "The mobile number is invalid. It must be only numbers", "userAtleast1Char": "Your username is required to be at least 1 character long.", "userCannotLong50Char": "Your username cannot be longer than 50 characters.", "emailNeeds": "Your email is required.", "emailNotValid": "Your email address is incorrect.", "emailAtleast4Char": "Email cannot be less than 4 characters.", "emailCannotLong50Char": "Email cannot be more than 50 characters.", "passengersIdNodValid": "Passenger ID  Not Valid", "saveerror": "Error saving tourism permit"}, "validate": {"newpassword": {"required": "Your password is required.", "minlength": "Your password is required to be at least 4 characters.", "maxlength": "Your password cannot be longer than 50 characters.", "strength": "Password strength:"}, "confirmpassword": {"required": "Your confirmation password is required.", "minlength": "Your confirmation password is required to be at least 4 characters.", "maxlength": "Your confirmation password cannot be longer than 50 characters."}, "email": {"required": "Your email is required.", "invalid": "Your email is invalid.", "minlength": "Your email is required to be at least 5 characters.", "maxlength": "Your email cannot be longer than 50 characters.", "emailPattern": "Please use valid email."}}}, "field": {"id": "ID"}, "ribbon": {"dev": "Development"}, "item-count": "Showing {{first}} - {{second}} of {{total}} items.", "footer": {"message1": "Having any trouble ?", "message2": "Contact us"}}, "entity": {"action": {"addblob": "Add blob", "addimage": "Add image", "back": "Back", "cancel": "Cancel", "delete": "Delete", "suspend": "Suspend", "activate": "Activate", "edit": "Edit", "assignToLicensingOfficer": "Assign to Licensing Officer", "returnRequestForData": "Return request for more data", "open": "Open", "save": "Save", "view": "View", "close": "Close", "confirm": "Confirm", "show": "Show {{otherEntity}}", "print": "Print", "writeNotes": "Write Notes", "inspectionReview": "Inspection Review"}, "detail": {"field": "Field", "value": "Value"}, "delete": {"title": "Confirm delete operation"}, "validation": {"required": "This field is required.", "minlength": "This field is required to be at least {{ min }} characters.", "maxlength": "This field cannot be longer than {{ max }} characters.", "min": "This field should be at least {{ min }}.", "max": "This field cannot be more than {{ max }}.", "daysOffLimit": "Days difference can't be more than {{ days }}.", "minNumber": "This field should be at least {{ min }}.", "minbytes": "This field should be at least {{ min }} bytes.", "maxbytes": "This field cannot be more than {{ max }} bytes.", "pattern": "This field should follow pattern for {{ pattern }}.", "number": "This field should be a number.", "datetimelocal": "This field should be a date and time.", "patternLogin": "This field can only contain letters, digits and e-mail addresses.", "patternAR": "This field should follow Arabic letters ", "patternEN": "This field should follow English letters", "workersError": "The total saudi Labor Count and foreigner Count must be at least 1 .", "noNumbers": "Numbers are not allowed in this field."}, "filters": {"set": "Following filters are set", "clear": "Clear filter", "clearAll": "Clear all filters"}}, "error": {"internalServerError": "Internal server error", "server.not.reachable": "Server not reachable", "url.not.found": "Not found", "NotNull": "Field {{ fieldName }} cannot be empty!", "Size": "Field {{ fieldName }} does not meet min/max size requirements!", "userexists": "Login name already used!", "emailexists": "Email is already in use!", "idexists": "A new {{ entityName }} cannot already have an ID", "idnull": "Invalid ID", "idinvalid": "Invalid ID", "idnotfound": "ID cannot be found", "file": {"could.not.extract": "Could not extract file", "not.image": "File was expected to be an image but was found to be \"{{ fileType }}\""}, "paymentError": "Error while calling payment service", "pageNoteFound": "Page not found", "pageNoteFoundMessage": "Sorry, but the page you were trying to view does not exist.", "generalError": "System error", "generalErrorMessage": "Sorry, System error. Please contact the technical support."}, "token": {"remainingTime": "Remaining Time", "body": "Current session is about to expire, please extend session or logout", "extend": "Extend"}, "payment": {"chooseMethod": "Choose your payment method"}, "footer": {"faq": "FAQ", "about": "About RASA", "contactUs": "Contact Us", "siteMap": "Site Map", "copyright": "All rights reserved to Digital Government Authority © 2024"}, "T&C": "Terms & Conditions", "privacy-policy": "Privacy Policy"}