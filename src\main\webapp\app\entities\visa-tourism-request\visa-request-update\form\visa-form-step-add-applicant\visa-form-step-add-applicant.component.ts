import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import TranslateDirective from 'app/shared/language/translate.directive';
import { VisaApplicant } from 'app/entities/visa-tourism-request/visa-applicant.model';
import { PersonListTableComponent } from 'app/entities/visa-tourism-request/visa-request-update/form/visa-form-step-add-applicant/person-list-table/person-list-table.component';
import { PersonDetailsModalComponent } from './person-details-modal/person-details-modal.component';

@Component({
  selector: 'jhi-visa-form-step-add-applicant',
  standalone: true,
  imports: [CommonModule, TranslateModule, TranslateDirective, PersonDetailsModalComponent, PersonListTableComponent],
  templateUrl: './visa-form-step-add-applicant.component.html',
  styleUrl: './visa-form-step-add-applicant.component.scss',
})
export class VisaFormStepAddApplicantComponent {
  applicants: VisaApplicant[] = [];

  onPersonAdded(person: VisaApplicant): void {
    this.applicants.push(person);
    console.log('Person added to list:', person);
    console.log('Total applicants:', this.applicants.length);
  }
}
