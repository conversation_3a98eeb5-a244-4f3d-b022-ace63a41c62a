package com.elm.srsa.service.dto;

import com.elm.srsa.domain.visa.enumerations.*;
import java.time.LocalDate;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VisaApplicantDTO {

    private Long id;
    private Long visaRequestId;

    // Full name related fields
    private String fullName; // optional, will be computed from English names
    private String firstNameAr;
    private String fatherNameAr;
    private String grandfatherNameAr;
    private String firstNameEn;
    private String fatherNameEn;
    private String grandfatherNameEn;

    private String passportNumber;
    private String nationality;
    private LocalDate birthDate;
    private Gender gender;
    private IdentityType identityType; // PASSPORT
    private Role role; // default: PASSENGER

    private String jobTitle; // occupation in form
    private MaritalStatus maritalStatus;
    private Religion religion;
    private String email;
    private String phoneNumber;
    private String address;

    private LocalDate passportIssueDate;
    private LocalDate passportExpiryDate;
    private String passportCountry;
    private String birthCountry;
    private String residenceCountry;
    private String birthPlace; // additional field from form
    private String countryCode; // from form

    private LocalDate expectedEntryDate;
    private LocalDate expectedExitDate;
    private EntryType numberOfEntries;
    private Integer visaDurationDays;
    private LocalDate visaValidity;

    private Boolean wantsUmrah;
    private Boolean hasOtherNationality;
    private String otherNationality;
    private Boolean tookVaccines;
    private String vaccineDetails;

    private Boolean hasDisability;
    private String disabilityDetails;

    private Boolean previousVisaToSaudi;
    private String previousVisaDetails;

    private String purposeOfVisit;

    // Location fields from form
    private String city;
    private String country;
    private String postalCode;
    private String alternatePhone;

    // Nested objects
    private VisaMedicalHistoryDTO medicalHistory;
    private VisaSecurityClearanceDTO securityClearance;
    private List<VisaApplicantPreviousTravelDTO> previousTravels;
}
